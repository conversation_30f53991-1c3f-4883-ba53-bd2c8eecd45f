{"name": "smartpick-backend", "version": "1.0.0", "description": "AI选品工具后端服务", "main": "server.js", "scripts": {"start": "node minimal-server.js", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "prisma db seed", "db:studio": "prisma studio", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix"}, "dependencies": {"@prisma/client": "^5.22.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "redis": "^4.6.10", "socket.io": "^4.7.4", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20.9.0", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "prettier": "^3.1.0", "prisma": "^5.22.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "prisma": {"seed": "node prisma/seed.js"}}