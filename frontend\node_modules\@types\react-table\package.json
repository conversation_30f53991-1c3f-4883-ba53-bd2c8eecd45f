{"name": "@types/react-table", "version": "7.7.20", "description": "TypeScript definitions for react-table", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-table", "license": "MIT", "contributors": [{"name": "<PERSON>-<PERSON>", "githubUsername": "ggascoigne", "url": "https://github.com/ggascoigne"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "gargroh", "url": "https://github.com/gargroh"}, {"name": "<PERSON>", "githubUsername": "riceboyler", "url": "https://github.com/riceboyler"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-table"}, "scripts": {}, "dependencies": {"@types/react": "*"}, "typesPublisherContentHash": "082373b74943e8a642413a7e2ed348fdf9c131e34fa5c94e51c85c611c86d73b", "typeScriptVersion": "4.7"}