# React Table v7

> [Looking for the latest version?](https://github.com/tanstack/react-table)

## Visit [react-table-v7.tanstack.com](https://react-table-v7.tanstack.com) for docs, guides, API and more!

## Quick Features

- Lightweight (5kb - 14kb+ depending on features used and tree-shaking)
- Headless (100% customizable, Bring-your-own-UI)
- Auto out of the box, fully controllable API
- Sorting (Multi and Stable)
- Filters
- Pivoting & Aggregation
- Row Selection
- Row Expansion
- Column Ordering
- Animatable
- Virtualizable
- Resizable
- Server-side/controlled data/state
- Extensible via hook-based plugin system

### [Become a Sponsor](https://github.com/sponsors/tannerlinsley/)

<!-- Force 1 -->
