{"version": 3, "file": "react-table.development.js", "sources": ["../src/publicUtils.js", "../src/utils.js", "../src/makeDefaultPluginHooks.js", "../src/hooks/useColumnVisibility.js", "../src/hooks/useTable.js", "../src/plugin-hooks/useExpanded.js", "../src/filterTypes.js", "../src/plugin-hooks/useFilters.js", "../src/plugin-hooks/useGlobalFilter.js", "../src/aggregations.js", "../src/plugin-hooks/useGroupBy.js", "../src/sortTypes.js", "../src/plugin-hooks/useSortBy.js", "../src/plugin-hooks/usePagination.js", "../src/plugin-hooks/_UNSTABLE_usePivotColumns.js", "../src/plugin-hooks/useRowSelect.js", "../src/plugin-hooks/useRowState.js", "../src/plugin-hooks/useColumnOrder.js", "../src/plugin-hooks/useResizeColumns.js", "../src/plugin-hooks/useAbsoluteLayout.js", "../src/plugin-hooks/useBlockLayout.js", "../src/plugin-hooks/useFlexLayout.js", "../src/plugin-hooks/useGridLayout.js"], "sourcesContent": ["import React from 'react'\n\nlet renderErr = 'Renderer Error ☝️'\n\nexport const actions = {\n  init: 'init',\n}\n\nexport const defaultRenderer = ({ value = '' }) => value;\nexport const emptyRenderer = () => <>&nbsp;</>;\n\nexport const defaultColumn = {\n  Cell: defaultRenderer,\n  width: 150,\n  minWidth: 0,\n  maxWidth: Number.MAX_SAFE_INTEGER,\n}\n\nfunction mergeProps(...propList) {\n  return propList.reduce((props, next) => {\n    const { style, className, ...rest } = next\n\n    props = {\n      ...props,\n      ...rest,\n    }\n\n    if (style) {\n      props.style = props.style\n        ? { ...(props.style || {}), ...(style || {}) }\n        : style\n    }\n\n    if (className) {\n      props.className = props.className\n        ? props.className + ' ' + className\n        : className\n    }\n\n    if (props.className === '') {\n      delete props.className\n    }\n\n    return props\n  }, {})\n}\n\nfunction handlePropGetter(prevProps, userProps, meta) {\n  // Handle a lambda, pass it the previous props\n  if (typeof userProps === 'function') {\n    return handlePropGetter({}, userProps(prevProps, meta))\n  }\n\n  // Handle an array, merge each item as separate props\n  if (Array.isArray(userProps)) {\n    return mergeProps(prevProps, ...userProps)\n  }\n\n  // Handle an object by default, merge the two objects\n  return mergeProps(prevProps, userProps)\n}\n\nexport const makePropGetter = (hooks, meta = {}) => {\n  return (userProps = {}) =>\n    [...hooks, userProps].reduce(\n      (prev, next) =>\n        handlePropGetter(prev, next, {\n          ...meta,\n          userProps,\n        }),\n      {}\n    )\n}\n\nexport const reduceHooks = (hooks, initial, meta = {}, allowUndefined) =>\n  hooks.reduce((prev, next) => {\n    const nextValue = next(prev, meta)\n    if (process.env.NODE_ENV !== 'production') {\n      if (!allowUndefined && typeof nextValue === 'undefined') {\n        console.info(next)\n        throw new Error(\n          'React Table: A reducer hook ☝️ just returned undefined! This is not allowed.'\n        )\n      }\n    }\n    return nextValue\n  }, initial)\n\nexport const loopHooks = (hooks, context, meta = {}) =>\n  hooks.forEach(hook => {\n    const nextValue = hook(context, meta)\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof nextValue !== 'undefined') {\n        console.info(hook, nextValue)\n        throw new Error(\n          'React Table: A loop-type hook ☝️ just returned a value! This is not allowed.'\n        )\n      }\n    }\n  })\n\nexport function ensurePluginOrder(plugins, befores, pluginName, afters) {\n  if (process.env.NODE_ENV !== 'production' && afters) {\n    throw new Error(\n      `Defining plugins in the \"after\" section of ensurePluginOrder is no longer supported (see plugin ${pluginName})`\n    )\n  }\n  const pluginIndex = plugins.findIndex(\n    plugin => plugin.pluginName === pluginName\n  )\n\n  if (pluginIndex === -1) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(`The plugin \"${pluginName}\" was not found in the plugin list!\nThis usually means you need to need to name your plugin hook by setting the 'pluginName' property of the hook function, eg:\n\n  ${pluginName}.pluginName = '${pluginName}'\n`)\n    }\n  }\n\n  befores.forEach(before => {\n    const beforeIndex = plugins.findIndex(\n      plugin => plugin.pluginName === before\n    )\n    if (beforeIndex > -1 && beforeIndex > pluginIndex) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error(\n          `React Table: The ${pluginName} plugin hook must be placed after the ${before} plugin hook!`\n        )\n      }\n    }\n  })\n}\n\nexport function functionalUpdate(updater, old) {\n  return typeof updater === 'function' ? updater(old) : updater\n}\n\nexport function useGetLatest(obj) {\n  const ref = React.useRef()\n  ref.current = obj\n\n  return React.useCallback(() => ref.current, [])\n}\n\n// SSR has issues with useLayoutEffect still, so use useEffect during SSR\nexport const safeUseLayoutEffect =\n  typeof document !== 'undefined' ? React.useLayoutEffect : React.useEffect\n\nexport function useMountedLayoutEffect(fn, deps) {\n  const mountedRef = React.useRef(false)\n\n  safeUseLayoutEffect(() => {\n    if (mountedRef.current) {\n      fn()\n    }\n    mountedRef.current = true\n    // eslint-disable-next-line\n  }, deps)\n}\n\nexport function useAsyncDebounce(defaultFn, defaultWait = 0) {\n  const debounceRef = React.useRef({})\n\n  const getDefaultFn = useGetLatest(defaultFn)\n  const getDefaultWait = useGetLatest(defaultWait)\n\n  return React.useCallback(\n    async (...args) => {\n      if (!debounceRef.current.promise) {\n        debounceRef.current.promise = new Promise((resolve, reject) => {\n          debounceRef.current.resolve = resolve\n          debounceRef.current.reject = reject\n        })\n      }\n\n      if (debounceRef.current.timeout) {\n        clearTimeout(debounceRef.current.timeout)\n      }\n\n      debounceRef.current.timeout = setTimeout(async () => {\n        delete debounceRef.current.timeout\n        try {\n          debounceRef.current.resolve(await getDefaultFn()(...args))\n        } catch (err) {\n          debounceRef.current.reject(err)\n        } finally {\n          delete debounceRef.current.promise\n        }\n      }, getDefaultWait())\n\n      return debounceRef.current.promise\n    },\n    [getDefaultFn, getDefaultWait]\n  )\n}\n\nexport function makeRenderer(instance, column, meta = {}) {\n  return (type, userProps = {}) => {\n    const Comp = typeof type === 'string' ? column[type] : type\n\n    if (typeof Comp === 'undefined') {\n      console.info(column)\n      throw new Error(renderErr)\n    }\n\n    return flexRender(Comp, { ...instance, column, ...meta, ...userProps })\n  }\n}\n\nexport function flexRender(Comp, props) {\n  return isReactComponent(Comp) ? <Comp {...props} /> : Comp\n}\n\nfunction isReactComponent(component) {\n  return (\n    isClassComponent(component) ||\n    typeof component === 'function' ||\n    isExoticComponent(component)\n  )\n}\n\nfunction isClassComponent(component) {\n  return (\n    typeof component === 'function' &&\n    (() => {\n      const proto = Object.getPrototypeOf(component)\n      return proto.prototype && proto.prototype.isReactComponent\n    })()\n  )\n}\n\nfunction isExoticComponent(component) {\n  return (\n    typeof component === 'object' &&\n    typeof component.$$typeof === 'symbol' &&\n    ['react.memo', 'react.forward_ref'].includes(component.$$typeof.description)\n  )\n}\n", "import { defaultColumn, emptyRenderer } from './publicUtils'\n\n// Find the depth of the columns\nexport function findMaxDepth(columns, depth = 0) {\n  return columns.reduce((prev, curr) => {\n    if (curr.columns) {\n      return Math.max(prev, findMaxDepth(curr.columns, depth + 1))\n    }\n    return depth\n  }, 0)\n}\n\n// Build the visible columns, headers and flat column list\nexport function linkColumnStructure(columns, parent, depth = 0) {\n  return columns.map(column => {\n    column = {\n      ...column,\n      parent,\n      depth,\n    }\n\n    assignColumnAccessor(column)\n\n    if (column.columns) {\n      column.columns = linkColumnStructure(column.columns, column, depth + 1)\n    }\n    return column\n  })\n}\n\nexport function flattenColumns(columns) {\n  return flattenBy(columns, 'columns')\n}\n\nexport function assignColumnAccessor(column) {\n  // First check for string accessor\n  let { id, accessor, Header } = column\n\n  if (typeof accessor === 'string') {\n    id = id || accessor\n    const accessorPath = accessor.split('.')\n    accessor = row => getBy(row, accessorPath)\n  }\n\n  if (!id && typeof Header === 'string' && Header) {\n    id = Header\n  }\n\n  if (!id && column.columns) {\n    console.error(column)\n    throw new Error('A column ID (or unique \"Header\" value) is required!')\n  }\n\n  if (!id) {\n    console.error(column)\n    throw new Error('A column ID (or string accessor) is required!')\n  }\n\n  Object.assign(column, {\n    id,\n    accessor,\n  })\n\n  return column\n}\n\nexport function decorateColumn(column, userDefaultColumn) {\n  if (!userDefaultColumn) {\n    throw new Error()\n  }\n  Object.assign(column, {\n    // Make sure there is a fallback header, just in case\n    Header: emptyRenderer,\n    Footer: emptyRenderer,\n    ...defaultColumn,\n    ...userDefaultColumn,\n    ...column,\n  })\n\n  Object.assign(column, {\n    originalWidth: column.width,\n  })\n\n  return column\n}\n\n// Build the header groups from the bottom up\nexport function makeHeaderGroups(\n  allColumns,\n  defaultColumn,\n  additionalHeaderProperties = () => ({})\n) {\n  const headerGroups = []\n\n  let scanColumns = allColumns\n\n  let uid = 0\n  const getUID = () => uid++\n\n  while (scanColumns.length) {\n    // The header group we are creating\n    const headerGroup = {\n      headers: [],\n    }\n\n    // The parent columns we're going to scan next\n    const parentColumns = []\n\n    const hasParents = scanColumns.some(d => d.parent)\n\n    // Scan each column for parents\n    scanColumns.forEach(column => {\n      // What is the latest (last) parent column?\n      let latestParentColumn = [...parentColumns].reverse()[0]\n\n      let newParent\n\n      if (hasParents) {\n        // If the column has a parent, add it if necessary\n        if (column.parent) {\n          newParent = {\n            ...column.parent,\n            originalId: column.parent.id,\n            id: `${column.parent.id}_${getUID()}`,\n            headers: [column],\n            ...additionalHeaderProperties(column),\n          }\n        } else {\n          // If other columns have parents, we'll need to add a place holder if necessary\n          const originalId = `${column.id}_placeholder`\n          newParent = decorateColumn(\n            {\n              originalId,\n              id: `${column.id}_placeholder_${getUID()}`,\n              placeholderOf: column,\n              headers: [column],\n              ...additionalHeaderProperties(column),\n            },\n            defaultColumn\n          )\n        }\n\n        // If the resulting parent columns are the same, just add\n        // the column and increment the header span\n        if (\n          latestParentColumn &&\n          latestParentColumn.originalId === newParent.originalId\n        ) {\n          latestParentColumn.headers.push(column)\n        } else {\n          parentColumns.push(newParent)\n        }\n      }\n\n      headerGroup.headers.push(column)\n    })\n\n    headerGroups.push(headerGroup)\n\n    // Start scanning the parent columns\n    scanColumns = parentColumns\n  }\n\n  return headerGroups.reverse()\n}\n\nconst pathObjCache = new Map()\n\nexport function getBy(obj, path, def) {\n  if (!path) {\n    return obj\n  }\n  const cacheKey = typeof path === 'function' ? path : JSON.stringify(path)\n\n  const pathObj =\n    pathObjCache.get(cacheKey) ||\n    (() => {\n      const pathObj = makePathArray(path)\n      pathObjCache.set(cacheKey, pathObj)\n      return pathObj\n    })()\n\n  let val\n\n  try {\n    val = pathObj.reduce((cursor, pathPart) => cursor[pathPart], obj)\n  } catch (e) {\n    // continue regardless of error\n  }\n  return typeof val !== 'undefined' ? val : def\n}\n\nexport function getFirstDefined(...args) {\n  for (let i = 0; i < args.length; i += 1) {\n    if (typeof args[i] !== 'undefined') {\n      return args[i]\n    }\n  }\n}\n\nexport function getElementDimensions(element) {\n  const rect = element.getBoundingClientRect()\n  const style = window.getComputedStyle(element)\n  const margins = {\n    left: parseInt(style.marginLeft),\n    right: parseInt(style.marginRight),\n  }\n  const padding = {\n    left: parseInt(style.paddingLeft),\n    right: parseInt(style.paddingRight),\n  }\n  return {\n    left: Math.ceil(rect.left),\n    width: Math.ceil(rect.width),\n    outerWidth: Math.ceil(\n      rect.width + margins.left + margins.right + padding.left + padding.right\n    ),\n    marginLeft: margins.left,\n    marginRight: margins.right,\n    paddingLeft: padding.left,\n    paddingRight: padding.right,\n    scrollWidth: element.scrollWidth,\n  }\n}\n\nexport function isFunction(a) {\n  if (typeof a === 'function') {\n    return a\n  }\n}\n\nexport function flattenBy(arr, key) {\n  const flat = []\n\n  const recurse = arr => {\n    arr.forEach(d => {\n      if (!d[key]) {\n        flat.push(d)\n      } else {\n        recurse(d[key])\n      }\n    })\n  }\n\n  recurse(arr)\n\n  return flat\n}\n\nexport function expandRows(\n  rows,\n  { manualExpandedKey, expanded, expandSubRows = true }\n) {\n  const expandedRows = []\n\n  const handleRow = (row, addToExpandedRows = true) => {\n    row.isExpanded =\n      (row.original && row.original[manualExpandedKey]) || expanded[row.id]\n\n    row.canExpand = row.subRows && !!row.subRows.length\n\n    if (addToExpandedRows) {\n      expandedRows.push(row)\n    }\n\n    if (row.subRows && row.subRows.length && row.isExpanded) {\n      row.subRows.forEach(row => handleRow(row, expandSubRows))\n    }\n  }\n\n  rows.forEach(row => handleRow(row))\n\n  return expandedRows\n}\n\nexport function getFilterMethod(filter, userFilterTypes, filterTypes) {\n  return (\n    isFunction(filter) ||\n    userFilterTypes[filter] ||\n    filterTypes[filter] ||\n    filterTypes.text\n  )\n}\n\nexport function shouldAutoRemoveFilter(autoRemove, value, column) {\n  return autoRemove ? autoRemove(value, column) : typeof value === 'undefined'\n}\n\nexport function unpreparedAccessWarning() {\n  throw new Error(\n    'React-Table: You have not called prepareRow(row) one or more rows you are attempting to render.'\n  )\n}\n\nlet passiveSupported = null\nexport function passiveEventSupported() {\n  // memoize support to avoid adding multiple test events\n  if (typeof passiveSupported === 'boolean') return passiveSupported\n\n  let supported = false\n  try {\n    const options = {\n      get passive() {\n        supported = true\n        return false\n      },\n    }\n\n    window.addEventListener('test', null, options)\n    window.removeEventListener('test', null, options)\n  } catch (err) {\n    supported = false\n  }\n  passiveSupported = supported\n  return passiveSupported\n}\n\n//\n\nconst reOpenBracket = /\\[/g\nconst reCloseBracket = /\\]/g\n\nfunction makePathArray(obj) {\n  return (\n    flattenDeep(obj)\n      // remove all periods in parts\n      .map(d => String(d).replace('.', '_'))\n      // join parts using period\n      .join('.')\n      // replace brackets with periods\n      .replace(reOpenBracket, '.')\n      .replace(reCloseBracket, '')\n      // split it back out on periods\n      .split('.')\n  )\n}\n\nfunction flattenDeep(arr, newArr = []) {\n  if (!Array.isArray(arr)) {\n    newArr.push(arr)\n  } else {\n    for (let i = 0; i < arr.length; i += 1) {\n      flattenDeep(arr[i], newArr)\n    }\n  }\n  return newArr\n}\n", "const defaultGetTableProps = props => ({\n  role: 'table',\n  ...props,\n})\n\nconst defaultGetTableBodyProps = props => ({\n  role: 'rowgroup',\n  ...props,\n})\n\nconst defaultGetHeaderProps = (props, { column }) => ({\n  key: `header_${column.id}`,\n  colSpan: column.totalVisibleHeaderCount,\n  role: 'columnheader',\n  ...props,\n})\n\nconst defaultGetFooterProps = (props, { column }) => ({\n  key: `footer_${column.id}`,\n  colSpan: column.totalVisibleHeaderCount,\n  ...props,\n})\n\nconst defaultGetHeaderGroupProps = (props, { index }) => ({\n  key: `headerGroup_${index}`,\n  role: 'row',\n  ...props,\n})\n\nconst defaultGetFooterGroupProps = (props, { index }) => ({\n  key: `footerGroup_${index}`,\n  ...props,\n})\n\nconst defaultGetRowProps = (props, { row }) => ({\n  key: `row_${row.id}`,\n  role: 'row',\n  ...props,\n})\n\nconst defaultGetCellProps = (props, { cell }) => ({\n  key: `cell_${cell.row.id}_${cell.column.id}`,\n  role: 'cell',\n  ...props,\n})\n\nexport default function makeDefaultPluginHooks() {\n  return {\n    useOptions: [],\n    stateReducers: [],\n    useControlledState: [],\n    columns: [],\n    columnsDeps: [],\n    allColumns: [],\n    allColumnsDeps: [],\n    accessValue: [],\n    materializedColumns: [],\n    materializedColumnsDeps: [],\n    useInstanceAfterData: [],\n    visibleColumns: [],\n    visibleColumnsDeps: [],\n    headerGroups: [],\n    headerGroupsDeps: [],\n    useInstanceBeforeDimensions: [],\n    useInstance: [],\n    prepareRow: [],\n    getTableProps: [defaultGetTableProps],\n    getTableBodyProps: [defaultGetTableBodyProps],\n    getHeaderGroupProps: [defaultGetHeaderGroupProps],\n    getFooterGroupProps: [defaultGetFooterGroupProps],\n    getHeaderProps: [defaultGetHeaderProps],\n    getFooterProps: [defaultGetFooterProps],\n    getRowProps: [defaultGetRowProps],\n    getCellProps: [defaultGetCellProps],\n    useFinalInstance: [],\n  }\n}\n", "import React from 'react'\n\nimport {\n  actions,\n  functionalUpdate,\n  useGetLatest,\n  makePropGetter,\n  useMountedLayoutEffect,\n} from '../publicUtils'\n\nactions.resetHiddenColumns = 'resetHiddenColumns'\nactions.toggleHideColumn = 'toggleHideColumn'\nactions.setHiddenColumns = 'setHiddenColumns'\nactions.toggleHideAllColumns = 'toggleHideAllColumns'\n\nexport const useColumnVisibility = hooks => {\n  hooks.getToggleHiddenProps = [defaultGetToggleHiddenProps]\n  hooks.getToggleHideAllColumnsProps = [defaultGetToggleHideAllColumnsProps]\n\n  hooks.stateReducers.push(reducer)\n  hooks.useInstanceBeforeDimensions.push(useInstanceBeforeDimensions)\n  hooks.headerGroupsDeps.push((deps, { instance }) => [\n    ...deps,\n    instance.state.hiddenColumns,\n  ])\n  hooks.useInstance.push(useInstance)\n}\n\nuseColumnVisibility.pluginName = 'useColumnVisibility'\n\nconst defaultGetToggleHiddenProps = (props, { column }) => [\n  props,\n  {\n    onChange: e => {\n      column.toggleHidden(!e.target.checked)\n    },\n    style: {\n      cursor: 'pointer',\n    },\n    checked: column.isVisible,\n    title: 'Toggle Column Visible',\n  },\n]\n\nconst defaultGetToggleHideAllColumnsProps = (props, { instance }) => [\n  props,\n  {\n    onChange: e => {\n      instance.toggleHideAllColumns(!e.target.checked)\n    },\n    style: {\n      cursor: 'pointer',\n    },\n    checked: !instance.allColumnsHidden && !instance.state.hiddenColumns.length,\n    title: 'Toggle All Columns Hidden',\n    indeterminate:\n      !instance.allColumnsHidden && instance.state.hiddenColumns.length,\n  },\n]\n\nfunction reducer(state, action, previousState, instance) {\n  if (action.type === actions.init) {\n    return {\n      hiddenColumns: [],\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetHiddenColumns) {\n    return {\n      ...state,\n      hiddenColumns: instance.initialState.hiddenColumns || [],\n    }\n  }\n\n  if (action.type === actions.toggleHideColumn) {\n    const should =\n      typeof action.value !== 'undefined'\n        ? action.value\n        : !state.hiddenColumns.includes(action.columnId)\n\n    const hiddenColumns = should\n      ? [...state.hiddenColumns, action.columnId]\n      : state.hiddenColumns.filter(d => d !== action.columnId)\n\n    return {\n      ...state,\n      hiddenColumns,\n    }\n  }\n\n  if (action.type === actions.setHiddenColumns) {\n    return {\n      ...state,\n      hiddenColumns: functionalUpdate(action.value, state.hiddenColumns),\n    }\n  }\n\n  if (action.type === actions.toggleHideAllColumns) {\n    const shouldAll =\n      typeof action.value !== 'undefined'\n        ? action.value\n        : !state.hiddenColumns.length\n\n    return {\n      ...state,\n      hiddenColumns: shouldAll ? instance.allColumns.map(d => d.id) : [],\n    }\n  }\n}\n\nfunction useInstanceBeforeDimensions(instance) {\n  const {\n    headers,\n    state: { hiddenColumns },\n  } = instance\n\n  const isMountedRef = React.useRef(false)\n\n  if (!isMountedRef.current) {\n  }\n\n  const handleColumn = (column, parentVisible) => {\n    column.isVisible = parentVisible && !hiddenColumns.includes(column.id)\n\n    let totalVisibleHeaderCount = 0\n\n    if (column.headers && column.headers.length) {\n      column.headers.forEach(\n        subColumn =>\n          (totalVisibleHeaderCount += handleColumn(subColumn, column.isVisible))\n      )\n    } else {\n      totalVisibleHeaderCount = column.isVisible ? 1 : 0\n    }\n\n    column.totalVisibleHeaderCount = totalVisibleHeaderCount\n\n    return totalVisibleHeaderCount\n  }\n\n  let totalVisibleHeaderCount = 0\n\n  headers.forEach(\n    subHeader => (totalVisibleHeaderCount += handleColumn(subHeader, true))\n  )\n}\n\nfunction useInstance(instance) {\n  const {\n    columns,\n    flatHeaders,\n    dispatch,\n    allColumns,\n    getHooks,\n    state: { hiddenColumns },\n    autoResetHiddenColumns = true,\n  } = instance\n\n  const getInstance = useGetLatest(instance)\n\n  const allColumnsHidden = allColumns.length === hiddenColumns.length\n\n  const toggleHideColumn = React.useCallback(\n    (columnId, value) =>\n      dispatch({ type: actions.toggleHideColumn, columnId, value }),\n    [dispatch]\n  )\n\n  const setHiddenColumns = React.useCallback(\n    value => dispatch({ type: actions.setHiddenColumns, value }),\n    [dispatch]\n  )\n\n  const toggleHideAllColumns = React.useCallback(\n    value => dispatch({ type: actions.toggleHideAllColumns, value }),\n    [dispatch]\n  )\n\n  const getToggleHideAllColumnsProps = makePropGetter(\n    getHooks().getToggleHideAllColumnsProps,\n    { instance: getInstance() }\n  )\n\n  flatHeaders.forEach(column => {\n    column.toggleHidden = value => {\n      dispatch({\n        type: actions.toggleHideColumn,\n        columnId: column.id,\n        value,\n      })\n    }\n\n    column.getToggleHiddenProps = makePropGetter(\n      getHooks().getToggleHiddenProps,\n      {\n        instance: getInstance(),\n        column,\n      }\n    )\n  })\n\n  const getAutoResetHiddenColumns = useGetLatest(autoResetHiddenColumns)\n\n  useMountedLayoutEffect(() => {\n    if (getAutoResetHiddenColumns()) {\n      dispatch({ type: actions.resetHiddenColumns })\n    }\n  }, [dispatch, columns])\n\n  Object.assign(instance, {\n    allColumnsHidden,\n    toggleHideColumn,\n    setHiddenColumns,\n    toggleHideAllColumns,\n    getToggleHideAllColumnsProps,\n  })\n}\n", "import React from 'react'\n\n//\n\nimport {\n  linkColumnStructure,\n  flattenColumns,\n  assignColumnAccessor,\n  unpreparedAccessWarning,\n  makeHeaderGroups,\n  decorateColumn,\n} from '../utils'\n\nimport {\n  useGetLatest,\n  reduceHooks,\n  actions,\n  loopHooks,\n  makePropGetter,\n  makeRenderer,\n} from '../publicUtils'\n\nimport makeDefaultPluginHooks from '../makeDefaultPluginHooks'\n\nimport { useColumnVisibility } from './useColumnVisibility'\n\nconst defaultInitialState = {}\nconst defaultColumnInstance = {}\nconst defaultReducer = (state, action, prevState) => state\nconst defaultGetSubRows = (row, index) => row.subRows || []\nconst defaultGetRowId = (row, index, parent) =>\n  `${parent ? [parent.id, index].join('.') : index}`\nconst defaultUseControlledState = d => d\n\nfunction applyDefaults(props) {\n  const {\n    initialState = defaultInitialState,\n    defaultColumn = defaultColumnInstance,\n    getSubRows = defaultGetSubRows,\n    getRowId = defaultGetRowId,\n    stateReducer = defaultReducer,\n    useControlledState = defaultUseControlledState,\n    ...rest\n  } = props\n\n  return {\n    ...rest,\n    initialState,\n    defaultColumn,\n    getSubRows,\n    getRowId,\n    stateReducer,\n    useControlledState,\n  }\n}\n\nexport const useTable = (props, ...plugins) => {\n  // Apply default props\n  props = applyDefaults(props)\n\n  // Add core plugins\n  plugins = [useColumnVisibility, ...plugins]\n\n  // Create the table instance\n  let instanceRef = React.useRef({})\n\n  // Create a getter for the instance (helps avoid a lot of potential memory leaks)\n  const getInstance = useGetLatest(instanceRef.current)\n\n  // Assign the props, plugins and hooks to the instance\n  Object.assign(getInstance(), {\n    ...props,\n    plugins,\n    hooks: makeDefaultPluginHooks(),\n  })\n\n  // Allow plugins to register hooks as early as possible\n  plugins.filter(Boolean).forEach(plugin => {\n    plugin(getInstance().hooks)\n  })\n\n  // Consume all hooks and make a getter for them\n  const getHooks = useGetLatest(getInstance().hooks)\n  getInstance().getHooks = getHooks\n  delete getInstance().hooks\n\n  // Allow useOptions hooks to modify the options coming into the table\n  Object.assign(\n    getInstance(),\n    reduceHooks(getHooks().useOptions, applyDefaults(props))\n  )\n\n  const {\n    data,\n    columns: userColumns,\n    initialState,\n    defaultColumn,\n    getSubRows,\n    getRowId,\n    stateReducer,\n    useControlledState,\n  } = getInstance()\n\n  // Setup user reducer ref\n  const getStateReducer = useGetLatest(stateReducer)\n\n  // Build the reducer\n  const reducer = React.useCallback(\n    (state, action) => {\n      // Detect invalid actions\n      if (!action.type) {\n        console.info({ action })\n        throw new Error('Unknown Action 👆')\n      }\n\n      // Reduce the state from all plugin reducers\n      return [\n        ...getHooks().stateReducers,\n        // Allow the user to add their own state reducer(s)\n        ...(Array.isArray(getStateReducer())\n          ? getStateReducer()\n          : [getStateReducer()]),\n      ].reduce(\n        (s, handler) => handler(s, action, state, getInstance()) || s,\n        state\n      )\n    },\n    [getHooks, getStateReducer, getInstance]\n  )\n\n  // Start the reducer\n  const [reducerState, dispatch] = React.useReducer(reducer, undefined, () =>\n    reducer(initialState, { type: actions.init })\n  )\n\n  // Allow the user to control the final state with hooks\n  const state = reduceHooks(\n    [...getHooks().useControlledState, useControlledState],\n    reducerState,\n    { instance: getInstance() }\n  )\n\n  Object.assign(getInstance(), {\n    state,\n    dispatch,\n  })\n\n  // Decorate All the columns\n  const columns = React.useMemo(\n    () =>\n      linkColumnStructure(\n        reduceHooks(getHooks().columns, userColumns, {\n          instance: getInstance(),\n        })\n      ),\n    [\n      getHooks,\n      getInstance,\n      userColumns,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      ...reduceHooks(getHooks().columnsDeps, [], { instance: getInstance() }),\n    ]\n  )\n  getInstance().columns = columns\n\n  // Get the flat list of all columns and allow hooks to decorate\n  // those columns (and trigger this memoization via deps)\n  let allColumns = React.useMemo(\n    () =>\n      reduceHooks(getHooks().allColumns, flattenColumns(columns), {\n        instance: getInstance(),\n      }).map(assignColumnAccessor),\n    [\n      columns,\n      getHooks,\n      getInstance,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      ...reduceHooks(getHooks().allColumnsDeps, [], {\n        instance: getInstance(),\n      }),\n    ]\n  )\n  getInstance().allColumns = allColumns\n\n  // Access the row model using initial columns\n  const [rows, flatRows, rowsById] = React.useMemo(() => {\n    let rows = []\n    let flatRows = []\n    const rowsById = {}\n\n    const allColumnsQueue = [...allColumns]\n\n    while (allColumnsQueue.length) {\n      const column = allColumnsQueue.shift()\n      accessRowsForColumn({\n        data,\n        rows,\n        flatRows,\n        rowsById,\n        column,\n        getRowId,\n        getSubRows,\n        accessValueHooks: getHooks().accessValue,\n        getInstance,\n      })\n    }\n\n    return [rows, flatRows, rowsById]\n  }, [allColumns, data, getRowId, getSubRows, getHooks, getInstance])\n\n  Object.assign(getInstance(), {\n    rows,\n    initialRows: [...rows],\n    flatRows,\n    rowsById,\n    // materializedColumns,\n  })\n\n  loopHooks(getHooks().useInstanceAfterData, getInstance())\n\n  // Get the flat list of all columns AFTER the rows\n  // have been access, and allow hooks to decorate\n  // those columns (and trigger this memoization via deps)\n  let visibleColumns = React.useMemo(\n    () =>\n      reduceHooks(getHooks().visibleColumns, allColumns, {\n        instance: getInstance(),\n      }).map(d => decorateColumn(d, defaultColumn)),\n    [\n      getHooks,\n      allColumns,\n      getInstance,\n      defaultColumn,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      ...reduceHooks(getHooks().visibleColumnsDeps, [], {\n        instance: getInstance(),\n      }),\n    ]\n  )\n\n  // Combine new visible columns with all columns\n  allColumns = React.useMemo(() => {\n    const columns = [...visibleColumns]\n\n    allColumns.forEach(column => {\n      if (!columns.find(d => d.id === column.id)) {\n        columns.push(column)\n      }\n    })\n\n    return columns\n  }, [allColumns, visibleColumns])\n  getInstance().allColumns = allColumns\n\n  if (process.env.NODE_ENV !== 'production') {\n    const duplicateColumns = allColumns.filter((column, i) => {\n      return allColumns.findIndex(d => d.id === column.id) !== i\n    })\n\n    if (duplicateColumns.length) {\n      console.info(allColumns)\n      throw new Error(\n        `Duplicate columns were found with ids: \"${duplicateColumns\n          .map(d => d.id)\n          .join(', ')}\" in the columns array above`\n      )\n    }\n  }\n\n  // Make the headerGroups\n  const headerGroups = React.useMemo(\n    () =>\n      reduceHooks(\n        getHooks().headerGroups,\n        makeHeaderGroups(visibleColumns, defaultColumn),\n        getInstance()\n      ),\n    [\n      getHooks,\n      visibleColumns,\n      defaultColumn,\n      getInstance,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      ...reduceHooks(getHooks().headerGroupsDeps, [], {\n        instance: getInstance(),\n      }),\n    ]\n  )\n  getInstance().headerGroups = headerGroups\n\n  // Get the first level of headers\n  const headers = React.useMemo(\n    () => (headerGroups.length ? headerGroups[0].headers : []),\n    [headerGroups]\n  )\n  getInstance().headers = headers\n\n  // Provide a flat header list for utilities\n  getInstance().flatHeaders = headerGroups.reduce(\n    (all, headerGroup) => [...all, ...headerGroup.headers],\n    []\n  )\n\n  loopHooks(getHooks().useInstanceBeforeDimensions, getInstance())\n\n  // Filter columns down to visible ones\n  const visibleColumnsDep = visibleColumns\n    .filter(d => d.isVisible)\n    .map(d => d.id)\n    .sort()\n    .join('_')\n\n  visibleColumns = React.useMemo(\n    () => visibleColumns.filter(d => d.isVisible),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [visibleColumns, visibleColumnsDep]\n  )\n  getInstance().visibleColumns = visibleColumns\n\n  // Header Visibility is needed by this point\n  const [\n    totalColumnsMinWidth,\n    totalColumnsWidth,\n    totalColumnsMaxWidth,\n  ] = calculateHeaderWidths(headers)\n\n  getInstance().totalColumnsMinWidth = totalColumnsMinWidth\n  getInstance().totalColumnsWidth = totalColumnsWidth\n  getInstance().totalColumnsMaxWidth = totalColumnsMaxWidth\n\n  loopHooks(getHooks().useInstance, getInstance())\n\n  // Each materialized header needs to be assigned a render function and other\n  // prop getter properties here.\n  ;[...getInstance().flatHeaders, ...getInstance().allColumns].forEach(\n    column => {\n      // Give columns/headers rendering power\n      column.render = makeRenderer(getInstance(), column)\n\n      // Give columns/headers a default getHeaderProps\n      column.getHeaderProps = makePropGetter(getHooks().getHeaderProps, {\n        instance: getInstance(),\n        column,\n      })\n\n      // Give columns/headers a default getFooterProps\n      column.getFooterProps = makePropGetter(getHooks().getFooterProps, {\n        instance: getInstance(),\n        column,\n      })\n    }\n  )\n\n  getInstance().headerGroups = React.useMemo(\n    () =>\n      headerGroups.filter((headerGroup, i) => {\n        // Filter out any headers and headerGroups that don't have visible columns\n        headerGroup.headers = headerGroup.headers.filter(column => {\n          const recurse = headers =>\n            headers.filter(column => {\n              if (column.headers) {\n                return recurse(column.headers)\n              }\n              return column.isVisible\n            }).length\n          if (column.headers) {\n            return recurse(column.headers)\n          }\n          return column.isVisible\n        })\n\n        // Give headerGroups getRowProps\n        if (headerGroup.headers.length) {\n          headerGroup.getHeaderGroupProps = makePropGetter(\n            getHooks().getHeaderGroupProps,\n            { instance: getInstance(), headerGroup, index: i }\n          )\n\n          headerGroup.getFooterGroupProps = makePropGetter(\n            getHooks().getFooterGroupProps,\n            { instance: getInstance(), headerGroup, index: i }\n          )\n\n          return true\n        }\n\n        return false\n      }),\n    [headerGroups, getInstance, getHooks]\n  )\n\n  getInstance().footerGroups = [...getInstance().headerGroups].reverse()\n\n  // The prepareRow function is absolutely necessary and MUST be called on\n  // any rows the user wishes to be displayed.\n\n  getInstance().prepareRow = React.useCallback(\n    row => {\n      row.getRowProps = makePropGetter(getHooks().getRowProps, {\n        instance: getInstance(),\n        row,\n      })\n\n      // Build the visible cells for each row\n      row.allCells = allColumns.map(column => {\n        const value = row.values[column.id]\n\n        const cell = {\n          column,\n          row,\n          value,\n        }\n\n        // Give each cell a getCellProps base\n        cell.getCellProps = makePropGetter(getHooks().getCellProps, {\n          instance: getInstance(),\n          cell,\n        })\n\n        // Give each cell a renderer function (supports multiple renderers)\n        cell.render = makeRenderer(getInstance(), column, {\n          row,\n          cell,\n          value,\n        })\n\n        return cell\n      })\n\n      row.cells = visibleColumns.map(column =>\n        row.allCells.find(cell => cell.column.id === column.id)\n      )\n\n      // need to apply any row specific hooks (useExpanded requires this)\n      loopHooks(getHooks().prepareRow, row, { instance: getInstance() })\n    },\n    [getHooks, getInstance, allColumns, visibleColumns]\n  )\n\n  getInstance().getTableProps = makePropGetter(getHooks().getTableProps, {\n    instance: getInstance(),\n  })\n\n  getInstance().getTableBodyProps = makePropGetter(\n    getHooks().getTableBodyProps,\n    {\n      instance: getInstance(),\n    }\n  )\n\n  loopHooks(getHooks().useFinalInstance, getInstance())\n\n  return getInstance()\n}\n\nfunction calculateHeaderWidths(headers, left = 0) {\n  let sumTotalMinWidth = 0\n  let sumTotalWidth = 0\n  let sumTotalMaxWidth = 0\n  let sumTotalFlexWidth = 0\n\n  headers.forEach(header => {\n    let { headers: subHeaders } = header\n\n    header.totalLeft = left\n\n    if (subHeaders && subHeaders.length) {\n      const [\n        totalMinWidth,\n        totalWidth,\n        totalMaxWidth,\n        totalFlexWidth,\n      ] = calculateHeaderWidths(subHeaders, left)\n      header.totalMinWidth = totalMinWidth\n      header.totalWidth = totalWidth\n      header.totalMaxWidth = totalMaxWidth\n      header.totalFlexWidth = totalFlexWidth\n    } else {\n      header.totalMinWidth = header.minWidth\n      header.totalWidth = Math.min(\n        Math.max(header.minWidth, header.width),\n        header.maxWidth\n      )\n      header.totalMaxWidth = header.maxWidth\n      header.totalFlexWidth = header.canResize ? header.totalWidth : 0\n    }\n    if (header.isVisible) {\n      left += header.totalWidth\n      sumTotalMinWidth += header.totalMinWidth\n      sumTotalWidth += header.totalWidth\n      sumTotalMaxWidth += header.totalMaxWidth\n      sumTotalFlexWidth += header.totalFlexWidth\n    }\n  })\n\n  return [sumTotalMinWidth, sumTotalWidth, sumTotalMaxWidth, sumTotalFlexWidth]\n}\n\nfunction accessRowsForColumn({\n  data,\n  rows,\n  flatRows,\n  rowsById,\n  column,\n  getRowId,\n  getSubRows,\n  accessValueHooks,\n  getInstance,\n}) {\n  // Access the row's data column-by-column\n  // We do it this way so we can incrementally add materialized\n  // columns after the first pass and avoid excessive looping\n  const accessRow = (originalRow, rowIndex, depth = 0, parent, parentRows) => {\n    // Keep the original reference around\n    const original = originalRow\n\n    const id = getRowId(originalRow, rowIndex, parent)\n\n    let row = rowsById[id]\n\n    // If the row hasn't been created, let's make it\n    if (!row) {\n      row = {\n        id,\n        original,\n        index: rowIndex,\n        depth,\n        cells: [{}], // This is a dummy cell\n      }\n\n      // Override common array functions (and the dummy cell's getCellProps function)\n      // to show an error if it is accessed without calling prepareRow\n      row.cells.map = unpreparedAccessWarning\n      row.cells.filter = unpreparedAccessWarning\n      row.cells.forEach = unpreparedAccessWarning\n      row.cells[0].getCellProps = unpreparedAccessWarning\n\n      // Create the cells and values\n      row.values = {}\n\n      // Push this row into the parentRows array\n      parentRows.push(row)\n      // Keep track of every row in a flat array\n      flatRows.push(row)\n      // Also keep track of every row by its ID\n      rowsById[id] = row\n\n      // Get the original subrows\n      row.originalSubRows = getSubRows(originalRow, rowIndex)\n\n      // Then recursively access them\n      if (row.originalSubRows) {\n        const subRows = []\n        row.originalSubRows.forEach((d, i) =>\n          accessRow(d, i, depth + 1, row, subRows)\n        )\n        // Keep the new subRows array on the row\n        row.subRows = subRows\n      }\n    } else if (row.subRows) {\n      // If the row exists, then it's already been accessed\n      // Keep recursing, but don't worry about passing the\n      // accumlator array (those rows already exist)\n      row.originalSubRows.forEach((d, i) => accessRow(d, i, depth + 1, row))\n    }\n\n    // If the column has an accessor, use it to get a value\n    if (column.accessor) {\n      row.values[column.id] = column.accessor(\n        originalRow,\n        rowIndex,\n        row,\n        parentRows,\n        data\n      )\n    }\n\n    // Allow plugins to manipulate the column value\n    row.values[column.id] = reduceHooks(\n      accessValueHooks,\n      row.values[column.id],\n      {\n        row,\n        column,\n        instance: getInstance(),\n      },\n      true\n    )\n  }\n\n  data.forEach((originalRow, rowIndex) =>\n    accessRow(originalRow, rowIndex, 0, undefined, rows)\n  )\n}\n", "import React from 'react'\n\nimport { expandRows } from '../utils'\n\nimport {\n  useGetLatest,\n  actions,\n  useMountedLayoutEffect,\n  makePropGetter,\n  ensurePluginOrder,\n} from '../publicUtils'\n\n// Actions\nactions.resetExpanded = 'resetExpanded'\nactions.toggleRowExpanded = 'toggleRowExpanded'\nactions.toggleAllRowsExpanded = 'toggleAllRowsExpanded'\n\nexport const useExpanded = hooks => {\n  hooks.getToggleAllRowsExpandedProps = [defaultGetToggleAllRowsExpandedProps]\n  hooks.getToggleRowExpandedProps = [defaultGetToggleRowExpandedProps]\n  hooks.stateReducers.push(reducer)\n  hooks.useInstance.push(useInstance)\n  hooks.prepareRow.push(prepareRow)\n}\n\nuseExpanded.pluginName = 'useExpanded'\n\nconst defaultGetToggleAllRowsExpandedProps = (props, { instance }) => [\n  props,\n  {\n    onClick: e => {\n      instance.toggleAllRowsExpanded()\n    },\n    style: {\n      cursor: 'pointer',\n    },\n    title: 'Toggle All Rows Expanded',\n  },\n]\n\nconst defaultGetToggleRowExpandedProps = (props, { row }) => [\n  props,\n  {\n    onClick: () => {\n      row.toggleRowExpanded()\n    },\n    style: {\n      cursor: 'pointer',\n    },\n    title: 'Toggle Row Expanded',\n  },\n]\n\n// Reducer\nfunction reducer(state, action, previousState, instance) {\n  if (action.type === actions.init) {\n    return {\n      expanded: {},\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetExpanded) {\n    return {\n      ...state,\n      expanded: instance.initialState.expanded || {},\n    }\n  }\n\n  if (action.type === actions.toggleAllRowsExpanded) {\n    const { value } = action\n    const { rowsById } = instance\n\n    const isAllRowsExpanded =\n      Object.keys(rowsById).length === Object.keys(state.expanded).length\n\n    const expandAll = typeof value !== 'undefined' ? value : !isAllRowsExpanded\n\n    if (expandAll) {\n      const expanded = {}\n\n      Object.keys(rowsById).forEach(rowId => {\n        expanded[rowId] = true\n      })\n\n      return {\n        ...state,\n        expanded,\n      }\n    }\n\n    return {\n      ...state,\n      expanded: {},\n    }\n  }\n\n  if (action.type === actions.toggleRowExpanded) {\n    const { id, value: setExpanded } = action\n    const exists = state.expanded[id]\n\n    const shouldExist =\n      typeof setExpanded !== 'undefined' ? setExpanded : !exists\n\n    if (!exists && shouldExist) {\n      return {\n        ...state,\n        expanded: {\n          ...state.expanded,\n          [id]: true,\n        },\n      }\n    } else if (exists && !shouldExist) {\n      const { [id]: _, ...rest } = state.expanded\n      return {\n        ...state,\n        expanded: rest,\n      }\n    } else {\n      return state\n    }\n  }\n}\n\nfunction useInstance(instance) {\n  const {\n    data,\n    rows,\n    rowsById,\n    manualExpandedKey = 'expanded',\n    paginateExpandedRows = true,\n    expandSubRows = true,\n    autoResetExpanded = true,\n    getHooks,\n    plugins,\n    state: { expanded },\n    dispatch,\n  } = instance\n\n  ensurePluginOrder(\n    plugins,\n    ['useSortBy', 'useGroupBy', 'usePivotColumns', 'useGlobalFilter'],\n    'useExpanded'\n  )\n\n  const getAutoResetExpanded = useGetLatest(autoResetExpanded)\n\n  let isAllRowsExpanded = Boolean(\n    Object.keys(rowsById).length && Object.keys(expanded).length\n  )\n\n  if (isAllRowsExpanded) {\n    if (Object.keys(rowsById).some(id => !expanded[id])) {\n      isAllRowsExpanded = false\n    }\n  }\n\n  // Bypass any effects from firing when this changes\n  useMountedLayoutEffect(() => {\n    if (getAutoResetExpanded()) {\n      dispatch({ type: actions.resetExpanded })\n    }\n  }, [dispatch, data])\n\n  const toggleRowExpanded = React.useCallback(\n    (id, value) => {\n      dispatch({ type: actions.toggleRowExpanded, id, value })\n    },\n    [dispatch]\n  )\n\n  const toggleAllRowsExpanded = React.useCallback(\n    value => dispatch({ type: actions.toggleAllRowsExpanded, value }),\n    [dispatch]\n  )\n\n  const expandedRows = React.useMemo(() => {\n    if (paginateExpandedRows) {\n      return expandRows(rows, { manualExpandedKey, expanded, expandSubRows })\n    }\n\n    return rows\n  }, [paginateExpandedRows, rows, manualExpandedKey, expanded, expandSubRows])\n\n  const expandedDepth = React.useMemo(() => findExpandedDepth(expanded), [\n    expanded,\n  ])\n\n  const getInstance = useGetLatest(instance)\n\n  const getToggleAllRowsExpandedProps = makePropGetter(\n    getHooks().getToggleAllRowsExpandedProps,\n    { instance: getInstance() }\n  )\n\n  Object.assign(instance, {\n    preExpandedRows: rows,\n    expandedRows,\n    rows: expandedRows,\n    expandedDepth,\n    isAllRowsExpanded,\n    toggleRowExpanded,\n    toggleAllRowsExpanded,\n    getToggleAllRowsExpandedProps,\n  })\n}\n\nfunction prepareRow(row, { instance: { getHooks }, instance }) {\n  row.toggleRowExpanded = set => instance.toggleRowExpanded(row.id, set)\n\n  row.getToggleRowExpandedProps = makePropGetter(\n    getHooks().getToggleRowExpandedProps,\n    {\n      instance,\n      row,\n    }\n  )\n}\n\nfunction findExpandedDepth(expanded) {\n  let maxDepth = 0\n\n  Object.keys(expanded).forEach(id => {\n    const splitId = id.split('.')\n    maxDepth = Math.max(maxDepth, splitId.length)\n  })\n\n  return maxDepth\n}\n", "export const text = (rows, ids, filterValue) => {\n  rows = rows.filter(row => {\n    return ids.some(id => {\n      const rowValue = row.values[id]\n      return String(rowValue)\n        .toLowerCase()\n        .includes(String(filterValue).toLowerCase())\n    })\n  })\n  return rows\n}\n\ntext.autoRemove = val => !val\n\nexport const exactText = (rows, ids, filterValue) => {\n  return rows.filter(row => {\n    return ids.some(id => {\n      const rowValue = row.values[id]\n      return rowValue !== undefined\n        ? String(rowValue).toLowerCase() === String(filterValue).toLowerCase()\n        : true\n    })\n  })\n}\n\nexactText.autoRemove = val => !val\n\nexport const exactTextCase = (rows, ids, filterValue) => {\n  return rows.filter(row => {\n    return ids.some(id => {\n      const rowValue = row.values[id]\n      return rowValue !== undefined\n        ? String(rowValue) === String(filterValue)\n        : true\n    })\n  })\n}\n\nexactTextCase.autoRemove = val => !val\n\nexport const includes = (rows, ids, filterValue) => {\n  return rows.filter(row => {\n    return ids.some(id => {\n      const rowValue = row.values[id]\n      return rowValue.includes(filterValue)\n    })\n  })\n}\n\nincludes.autoRemove = val => !val || !val.length\n\nexport const includesAll = (rows, ids, filterValue) => {\n  return rows.filter(row => {\n    return ids.some(id => {\n      const rowValue = row.values[id]\n      return (\n        rowValue &&\n        rowValue.length &&\n        filterValue.every(val => rowValue.includes(val))\n      )\n    })\n  })\n}\n\nincludesAll.autoRemove = val => !val || !val.length\n\nexport const includesSome = (rows, ids, filterValue) => {\n  return rows.filter(row => {\n    return ids.some(id => {\n      const rowValue = row.values[id]\n      return (\n        rowValue &&\n        rowValue.length &&\n        filterValue.some(val => rowValue.includes(val))\n      )\n    })\n  })\n}\n\nincludesSome.autoRemove = val => !val || !val.length\n\nexport const includesValue = (rows, ids, filterValue) => {\n  return rows.filter(row => {\n    return ids.some(id => {\n      const rowValue = row.values[id]\n      return filterValue.includes(rowValue)\n    })\n  })\n}\n\nincludesValue.autoRemove = val => !val || !val.length\n\nexport const exact = (rows, ids, filterValue) => {\n  return rows.filter(row => {\n    return ids.some(id => {\n      const rowValue = row.values[id]\n      return rowValue === filterValue\n    })\n  })\n}\n\nexact.autoRemove = val => typeof val === 'undefined'\n\nexport const equals = (rows, ids, filterValue) => {\n  return rows.filter(row => {\n    return ids.some(id => {\n      const rowValue = row.values[id]\n      // eslint-disable-next-line eqeqeq\n      return rowValue == filterValue\n    })\n  })\n}\n\nequals.autoRemove = val => val == null\n\nexport const between = (rows, ids, filterValue) => {\n  let [min, max] = filterValue || []\n\n  min = typeof min === 'number' ? min : -Infinity\n  max = typeof max === 'number' ? max : Infinity\n\n  if (min > max) {\n    const temp = min\n    min = max\n    max = temp\n  }\n\n  return rows.filter(row => {\n    return ids.some(id => {\n      const rowValue = row.values[id]\n      return rowValue >= min && rowValue <= max\n    })\n  })\n}\n\nbetween.autoRemove = val =>\n  !val || (typeof val[0] !== 'number' && typeof val[1] !== 'number')\n", "import React from 'react'\n\nimport {\n  getFirstDefined,\n  getFilterMethod,\n  shouldAutoRemoveFilter,\n} from '../utils'\n\nimport {\n  actions,\n  useGetLatest,\n  functionalUpdate,\n  useMountedLayoutEffect,\n} from '../publicUtils'\n\nimport * as filterTypes from '../filterTypes'\n\n// Actions\nactions.resetFilters = 'resetFilters'\nactions.setFilter = 'setFilter'\nactions.setAllFilters = 'setAllFilters'\n\nexport const useFilters = hooks => {\n  hooks.stateReducers.push(reducer)\n  hooks.useInstance.push(useInstance)\n}\n\nuseFilters.pluginName = 'useFilters'\n\nfunction reducer(state, action, previousState, instance) {\n  if (action.type === actions.init) {\n    return {\n      filters: [],\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetFilters) {\n    return {\n      ...state,\n      filters: instance.initialState.filters || [],\n    }\n  }\n\n  if (action.type === actions.setFilter) {\n    const { columnId, filterValue } = action\n    const { allColumns, filterTypes: userFilterTypes } = instance\n\n    const column = allColumns.find(d => d.id === columnId)\n\n    if (!column) {\n      throw new Error(\n        `React-Table: Could not find a column with id: ${columnId}`\n      )\n    }\n\n    const filterMethod = getFilterMethod(\n      column.filter,\n      userFilterTypes || {},\n      filterTypes\n    )\n\n    const previousfilter = state.filters.find(d => d.id === columnId)\n\n    const newFilter = functionalUpdate(\n      filterValue,\n      previousfilter && previousfilter.value\n    )\n\n    //\n    if (shouldAutoRemoveFilter(filterMethod.autoRemove, newFilter, column)) {\n      return {\n        ...state,\n        filters: state.filters.filter(d => d.id !== columnId),\n      }\n    }\n\n    if (previousfilter) {\n      return {\n        ...state,\n        filters: state.filters.map(d => {\n          if (d.id === columnId) {\n            return { id: columnId, value: newFilter }\n          }\n          return d\n        }),\n      }\n    }\n\n    return {\n      ...state,\n      filters: [...state.filters, { id: columnId, value: newFilter }],\n    }\n  }\n\n  if (action.type === actions.setAllFilters) {\n    const { filters } = action\n    const { allColumns, filterTypes: userFilterTypes } = instance\n\n    return {\n      ...state,\n      // Filter out undefined values\n      filters: functionalUpdate(filters, state.filters).filter(filter => {\n        const column = allColumns.find(d => d.id === filter.id)\n        const filterMethod = getFilterMethod(\n          column.filter,\n          userFilterTypes || {},\n          filterTypes\n        )\n\n        if (\n          shouldAutoRemoveFilter(filterMethod.autoRemove, filter.value, column)\n        ) {\n          return false\n        }\n        return true\n      }),\n    }\n  }\n}\n\nfunction useInstance(instance) {\n  const {\n    data,\n    rows,\n    flatRows,\n    rowsById,\n    allColumns,\n    filterTypes: userFilterTypes,\n    manualFilters,\n    defaultCanFilter = false,\n    disableFilters,\n    state: { filters },\n    dispatch,\n    autoResetFilters = true,\n  } = instance\n\n  const setFilter = React.useCallback(\n    (columnId, filterValue) => {\n      dispatch({ type: actions.setFilter, columnId, filterValue })\n    },\n    [dispatch]\n  )\n\n  const setAllFilters = React.useCallback(\n    filters => {\n      dispatch({\n        type: actions.setAllFilters,\n        filters,\n      })\n    },\n    [dispatch]\n  )\n\n  allColumns.forEach(column => {\n    const {\n      id,\n      accessor,\n      defaultCanFilter: columnDefaultCanFilter,\n      disableFilters: columnDisableFilters,\n    } = column\n\n    // Determine if a column is filterable\n    column.canFilter = accessor\n      ? getFirstDefined(\n          columnDisableFilters === true ? false : undefined,\n          disableFilters === true ? false : undefined,\n          true\n        )\n      : getFirstDefined(columnDefaultCanFilter, defaultCanFilter, false)\n\n    // Provide the column a way of updating the filter value\n    column.setFilter = val => setFilter(column.id, val)\n\n    // Provide the current filter value to the column for\n    // convenience\n    const found = filters.find(d => d.id === id)\n    column.filterValue = found && found.value\n  })\n\n  const [\n    filteredRows,\n    filteredFlatRows,\n    filteredRowsById,\n  ] = React.useMemo(() => {\n    if (manualFilters || !filters.length) {\n      return [rows, flatRows, rowsById]\n    }\n\n    const filteredFlatRows = []\n    const filteredRowsById = {}\n\n    // Filters top level and nested rows\n    const filterRows = (rows, depth = 0) => {\n      let filteredRows = rows\n\n      filteredRows = filters.reduce(\n        (filteredSoFar, { id: columnId, value: filterValue }) => {\n          // Find the filters column\n          const column = allColumns.find(d => d.id === columnId)\n\n          if (!column) {\n            return filteredSoFar\n          }\n\n          if (depth === 0) {\n            column.preFilteredRows = filteredSoFar\n          }\n\n          const filterMethod = getFilterMethod(\n            column.filter,\n            userFilterTypes || {},\n            filterTypes\n          )\n\n          if (!filterMethod) {\n            console.warn(\n              `Could not find a valid 'column.filter' for column with the ID: ${column.id}.`\n            )\n            return filteredSoFar\n          }\n\n          // Pass the rows, id, filterValue and column to the filterMethod\n          // to get the filtered rows back\n          column.filteredRows = filterMethod(\n            filteredSoFar,\n            [columnId],\n            filterValue\n          )\n\n          return column.filteredRows\n        },\n        rows\n      )\n\n      // Apply the filter to any subRows\n      // We technically could do this recursively in the above loop,\n      // but that would severely hinder the API for the user, since they\n      // would be required to do that recursion in some scenarios\n      filteredRows.forEach(row => {\n        filteredFlatRows.push(row)\n        filteredRowsById[row.id] = row\n        if (!row.subRows) {\n          return\n        }\n\n        row.subRows =\n          row.subRows && row.subRows.length > 0\n            ? filterRows(row.subRows, depth + 1)\n            : row.subRows\n      })\n\n      return filteredRows\n    }\n\n    return [filterRows(rows), filteredFlatRows, filteredRowsById]\n  }, [\n    manualFilters,\n    filters,\n    rows,\n    flatRows,\n    rowsById,\n    allColumns,\n    userFilterTypes,\n  ])\n\n  React.useMemo(() => {\n    // Now that each filtered column has it's partially filtered rows,\n    // lets assign the final filtered rows to all of the other columns\n    const nonFilteredColumns = allColumns.filter(\n      column => !filters.find(d => d.id === column.id)\n    )\n\n    // This essentially enables faceted filter options to be built easily\n    // using every column's preFilteredRows value\n    nonFilteredColumns.forEach(column => {\n      column.preFilteredRows = filteredRows\n      column.filteredRows = filteredRows\n    })\n  }, [filteredRows, filters, allColumns])\n\n  const getAutoResetFilters = useGetLatest(autoResetFilters)\n\n  useMountedLayoutEffect(() => {\n    if (getAutoResetFilters()) {\n      dispatch({ type: actions.resetFilters })\n    }\n  }, [dispatch, manualFilters ? null : data])\n\n  Object.assign(instance, {\n    preFilteredRows: rows,\n    preFilteredFlatRows: flatRows,\n    preFilteredRowsById: rowsById,\n    filteredRows,\n    filteredFlatRows,\n    filteredRowsById,\n    rows: filteredRows,\n    flatRows: filteredFlatRows,\n    rowsById: filteredRowsById,\n    setFilter,\n    setAllFilters,\n  })\n}\n", "import React from 'react'\n\nimport {\n  getFilterMethod,\n  shouldAutoRemoveFilter,\n  getFirstDefined,\n} from '../utils'\n\nimport {\n  actions,\n  useMountedLayoutEffect,\n  functionalUpdate,\n  useGetLatest,\n} from '../publicUtils'\n\nimport * as filterTypes from '../filterTypes'\n\n// Actions\nactions.resetGlobalFilter = 'resetGlobalFilter'\nactions.setGlobalFilter = 'setGlobalFilter'\n\nexport const useGlobalFilter = hooks => {\n  hooks.stateReducers.push(reducer)\n  hooks.useInstance.push(useInstance)\n}\n\nuseGlobalFilter.pluginName = 'useGlobalFilter'\n\nfunction reducer(state, action, previousState, instance) {\n  if (action.type === actions.resetGlobalFilter) {\n    return {\n      ...state,\n      globalFilter: instance.initialState.globalFilter || undefined,\n    }\n  }\n\n  if (action.type === actions.setGlobalFilter) {\n    const { filterValue } = action\n    const { userFilterTypes } = instance\n\n    const filterMethod = getFilterMethod(\n      instance.globalFilter,\n      userFilterTypes || {},\n      filterTypes\n    )\n\n    const newFilter = functionalUpdate(filterValue, state.globalFilter)\n\n    //\n    if (shouldAutoRemoveFilter(filterMethod.autoRemove, newFilter)) {\n      const { globalFilter, ...stateWithoutGlobalFilter } = state\n      return stateWithoutGlobalFilter\n    }\n\n    return {\n      ...state,\n      globalFilter: newFilter,\n    }\n  }\n}\n\nfunction useInstance(instance) {\n  const {\n    data,\n    rows,\n    flatRows,\n    rowsById,\n    allColumns,\n    filterTypes: userFilterTypes,\n    globalFilter,\n    manualGlobalFilter,\n    state: { globalFilter: globalFilterValue },\n    dispatch,\n    autoResetGlobalFilter = true,\n    disableGlobalFilter,\n  } = instance\n\n  const setGlobalFilter = React.useCallback(\n    filterValue => {\n      dispatch({ type: actions.setGlobalFilter, filterValue })\n    },\n    [dispatch]\n  )\n\n  // TODO: Create a filter cache for incremental high speed multi-filtering\n  // This gets pretty complicated pretty fast, since you have to maintain a\n  // cache for each row group (top-level rows, and each row's recursive subrows)\n  // This would make multi-filtering a lot faster though. Too far?\n\n  const [\n    globalFilteredRows,\n    globalFilteredFlatRows,\n    globalFilteredRowsById,\n  ] = React.useMemo(() => {\n    if (manualGlobalFilter || typeof globalFilterValue === 'undefined') {\n      return [rows, flatRows, rowsById]\n    }\n\n    const filteredFlatRows = []\n    const filteredRowsById = {}\n\n    const filterMethod = getFilterMethod(\n      globalFilter,\n      userFilterTypes || {},\n      filterTypes\n    )\n\n    if (!filterMethod) {\n      console.warn(`Could not find a valid 'globalFilter' option.`)\n      return rows\n    }\n\n    allColumns.forEach(column => {\n      const { disableGlobalFilter: columnDisableGlobalFilter } = column\n\n      column.canFilter = getFirstDefined(\n        columnDisableGlobalFilter === true ? false : undefined,\n        disableGlobalFilter === true ? false : undefined,\n        true\n      )\n    })\n\n    const filterableColumns = allColumns.filter(c => c.canFilter === true)\n\n    // Filters top level and nested rows\n    const filterRows = filteredRows => {\n      filteredRows = filterMethod(\n        filteredRows,\n        filterableColumns.map(d => d.id),\n        globalFilterValue\n      )\n\n      filteredRows.forEach(row => {\n        filteredFlatRows.push(row)\n        filteredRowsById[row.id] = row\n\n        row.subRows =\n          row.subRows && row.subRows.length\n            ? filterRows(row.subRows)\n            : row.subRows\n      })\n\n      return filteredRows\n    }\n\n    return [filterRows(rows), filteredFlatRows, filteredRowsById]\n  }, [\n    manualGlobalFilter,\n    globalFilterValue,\n    globalFilter,\n    userFilterTypes,\n    allColumns,\n    rows,\n    flatRows,\n    rowsById,\n    disableGlobalFilter,\n  ])\n\n  const getAutoResetGlobalFilter = useGetLatest(autoResetGlobalFilter)\n\n  useMountedLayoutEffect(() => {\n    if (getAutoResetGlobalFilter()) {\n      dispatch({ type: actions.resetGlobalFilter })\n    }\n  }, [dispatch, manualGlobalFilter ? null : data])\n\n  Object.assign(instance, {\n    preGlobalFilteredRows: rows,\n    preGlobalFilteredFlatRows: flatRows,\n    preGlobalFilteredRowsById: rowsById,\n    globalFilteredRows,\n    globalFilteredFlatRows,\n    globalFilteredRowsById,\n    rows: globalFilteredRows,\n    flatRows: globalFilteredFlatRows,\n    rowsById: globalFilteredRowsById,\n    setGlobalFilter,\n    disableGlobalFilter,\n  })\n}\n", "export function sum(values, aggregatedValues) {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return aggregatedValues.reduce(\n    (sum, next) => sum + (typeof next === 'number' ? next : 0),\n    0\n  )\n}\n\nexport function min(values) {\n  let min = values[0] || 0\n\n  values.forEach(value => {\n    if (typeof value === 'number') {\n      min = Math.min(min, value)\n    }\n  })\n\n  return min\n}\n\nexport function max(values) {\n  let max = values[0] || 0\n\n  values.forEach(value => {\n    if (typeof value === 'number') {\n      max = Math.max(max, value)\n    }\n  })\n\n  return max\n}\n\nexport function minMax(values) {\n  let min = values[0] || 0\n  let max = values[0] || 0\n\n  values.forEach(value => {\n    if (typeof value === 'number') {\n      min = Math.min(min, value)\n      max = Math.max(max, value)\n    }\n  })\n\n  return `${min}..${max}`\n}\n\nexport function average(values) {\n  return sum(null, values) / values.length\n}\n\nexport function median(values) {\n  if (!values.length) {\n    return null\n  }\n\n  const mid = Math.floor(values.length / 2)\n  const nums = [...values].sort((a, b) => a - b)\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1] + nums[mid]) / 2\n}\n\nexport function unique(values) {\n  return Array.from(new Set(values).values())\n}\n\nexport function uniqueCount(values) {\n  return new Set(values).size\n}\n\nexport function count(values) {\n  return values.length\n}\n", "import React from 'react'\n\nimport * as aggregations from '../aggregations'\n\nimport { getFirstDefined, flattenBy } from '../utils'\n\nimport {\n  actions,\n  makePropGetter,\n  ensurePluginOrder,\n  useMountedLayoutEffect,\n  useGetLatest,\n} from '../publicUtils'\n\nconst emptyArray = []\nconst emptyObject = {}\n\n// Actions\nactions.resetGroupBy = 'resetGroupBy'\nactions.setGroupBy = 'setGroupBy'\nactions.toggleGroupBy = 'toggleGroupBy'\n\nexport const useGroupBy = hooks => {\n  hooks.getGroupByToggleProps = [defaultGetGroupByToggleProps]\n  hooks.stateReducers.push(reducer)\n  hooks.visibleColumnsDeps.push((deps, { instance }) => [\n    ...deps,\n    instance.state.groupBy,\n  ])\n  hooks.visibleColumns.push(visibleColumns)\n  hooks.useInstance.push(useInstance)\n  hooks.prepareRow.push(prepareRow)\n}\n\nuseGroupBy.pluginName = 'useGroupBy'\n\nconst defaultGetGroupByToggleProps = (props, { header }) => [\n  props,\n  {\n    onClick: header.canGroupBy\n      ? e => {\n          e.persist()\n          header.toggleGroupBy()\n        }\n      : undefined,\n    style: {\n      cursor: header.canGroupBy ? 'pointer' : undefined,\n    },\n    title: 'Toggle GroupBy',\n  },\n]\n\n// Reducer\nfunction reducer(state, action, previousState, instance) {\n  if (action.type === actions.init) {\n    return {\n      groupBy: [],\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetGroupBy) {\n    return {\n      ...state,\n      groupBy: instance.initialState.groupBy || [],\n    }\n  }\n\n  if (action.type === actions.setGroupBy) {\n    const { value } = action\n    return {\n      ...state,\n      groupBy: value,\n    }\n  }\n\n  if (action.type === actions.toggleGroupBy) {\n    const { columnId, value: setGroupBy } = action\n\n    const resolvedGroupBy =\n      typeof setGroupBy !== 'undefined'\n        ? setGroupBy\n        : !state.groupBy.includes(columnId)\n\n    if (resolvedGroupBy) {\n      return {\n        ...state,\n        groupBy: [...state.groupBy, columnId],\n      }\n    }\n\n    return {\n      ...state,\n      groupBy: state.groupBy.filter(d => d !== columnId),\n    }\n  }\n}\n\nfunction visibleColumns(\n  columns,\n  {\n    instance: {\n      state: { groupBy },\n    },\n  }\n) {\n  // Sort grouped columns to the start of the column list\n  // before the headers are built\n\n  const groupByColumns = groupBy\n    .map(g => columns.find(col => col.id === g))\n    .filter(Boolean)\n\n  const nonGroupByColumns = columns.filter(col => !groupBy.includes(col.id))\n\n  columns = [...groupByColumns, ...nonGroupByColumns]\n\n  columns.forEach(column => {\n    column.isGrouped = groupBy.includes(column.id)\n    column.groupedIndex = groupBy.indexOf(column.id)\n  })\n\n  return columns\n}\n\nconst defaultUserAggregations = {}\n\nfunction useInstance(instance) {\n  const {\n    data,\n    rows,\n    flatRows,\n    rowsById,\n    allColumns,\n    flatHeaders,\n    groupByFn = defaultGroupByFn,\n    manualGroupBy,\n    aggregations: userAggregations = defaultUserAggregations,\n    plugins,\n    state: { groupBy },\n    dispatch,\n    autoResetGroupBy = true,\n    disableGroupBy,\n    defaultCanGroupBy,\n    getHooks,\n  } = instance\n\n  ensurePluginOrder(plugins, ['useColumnOrder', 'useFilters'], 'useGroupBy')\n\n  const getInstance = useGetLatest(instance)\n\n  allColumns.forEach(column => {\n    const {\n      accessor,\n      defaultGroupBy: defaultColumnGroupBy,\n      disableGroupBy: columnDisableGroupBy,\n    } = column\n\n    column.canGroupBy = accessor\n      ? getFirstDefined(\n          column.canGroupBy,\n          columnDisableGroupBy === true ? false : undefined,\n          disableGroupBy === true ? false : undefined,\n          true\n        )\n      : getFirstDefined(\n          column.canGroupBy,\n          defaultColumnGroupBy,\n          defaultCanGroupBy,\n          false\n        )\n\n    if (column.canGroupBy) {\n      column.toggleGroupBy = () => instance.toggleGroupBy(column.id)\n    }\n\n    column.Aggregated = column.Aggregated || column.Cell\n  })\n\n  const toggleGroupBy = React.useCallback(\n    (columnId, value) => {\n      dispatch({ type: actions.toggleGroupBy, columnId, value })\n    },\n    [dispatch]\n  )\n\n  const setGroupBy = React.useCallback(\n    value => {\n      dispatch({ type: actions.setGroupBy, value })\n    },\n    [dispatch]\n  )\n\n  flatHeaders.forEach(header => {\n    header.getGroupByToggleProps = makePropGetter(\n      getHooks().getGroupByToggleProps,\n      { instance: getInstance(), header }\n    )\n  })\n\n  const [\n    groupedRows,\n    groupedFlatRows,\n    groupedRowsById,\n    onlyGroupedFlatRows,\n    onlyGroupedRowsById,\n    nonGroupedFlatRows,\n    nonGroupedRowsById,\n  ] = React.useMemo(() => {\n    if (manualGroupBy || !groupBy.length) {\n      return [\n        rows,\n        flatRows,\n        rowsById,\n        emptyArray,\n        emptyObject,\n        flatRows,\n        rowsById,\n      ]\n    }\n\n    // Ensure that the list of filtered columns exist\n    const existingGroupBy = groupBy.filter(g =>\n      allColumns.find(col => col.id === g)\n    )\n\n    // Find the columns that can or are aggregating\n    // Uses each column to aggregate rows into a single value\n    const aggregateRowsToValues = (leafRows, groupedRows, depth) => {\n      const values = {}\n\n      allColumns.forEach(column => {\n        // Don't aggregate columns that are in the groupBy\n        if (existingGroupBy.includes(column.id)) {\n          values[column.id] = groupedRows[0]\n            ? groupedRows[0].values[column.id]\n            : null\n          return\n        }\n\n        // Aggregate the values\n        let aggregateFn =\n          typeof column.aggregate === 'function'\n            ? column.aggregate\n            : userAggregations[column.aggregate] ||\n              aggregations[column.aggregate]\n\n        if (aggregateFn) {\n          // Get the columnValues to aggregate\n          const groupedValues = groupedRows.map(row => row.values[column.id])\n\n          // Get the columnValues to aggregate\n          const leafValues = leafRows.map(row => {\n            let columnValue = row.values[column.id]\n\n            if (!depth && column.aggregateValue) {\n              const aggregateValueFn =\n                typeof column.aggregateValue === 'function'\n                  ? column.aggregateValue\n                  : userAggregations[column.aggregateValue] ||\n                    aggregations[column.aggregateValue]\n\n              if (!aggregateValueFn) {\n                console.info({ column })\n                throw new Error(\n                  `React Table: Invalid column.aggregateValue option for column listed above`\n                )\n              }\n\n              columnValue = aggregateValueFn(columnValue, row, column)\n            }\n            return columnValue\n          })\n\n          values[column.id] = aggregateFn(leafValues, groupedValues)\n        } else if (column.aggregate) {\n          console.info({ column })\n          throw new Error(\n            `React Table: Invalid column.aggregate option for column listed above`\n          )\n        } else {\n          values[column.id] = null\n        }\n      })\n\n      return values\n    }\n\n    let groupedFlatRows = []\n    const groupedRowsById = {}\n    const onlyGroupedFlatRows = []\n    const onlyGroupedRowsById = {}\n    const nonGroupedFlatRows = []\n    const nonGroupedRowsById = {}\n\n    // Recursively group the data\n    const groupUpRecursively = (rows, depth = 0, parentId) => {\n      // This is the last level, just return the rows\n      if (depth === existingGroupBy.length) {\n        return rows.map((row) => ({ ...row, depth }))\n      }\n\n      const columnId = existingGroupBy[depth]\n\n      // Group the rows together for this level\n      let rowGroupsMap = groupByFn(rows, columnId)\n\n      // Peform aggregations for each group\n      const aggregatedGroupedRows = Object.entries(rowGroupsMap).map(\n        ([groupByVal, groupedRows], index) => {\n          let id = `${columnId}:${groupByVal}`\n          id = parentId ? `${parentId}>${id}` : id\n\n          // First, Recurse to group sub rows before aggregation\n          const subRows = groupUpRecursively(groupedRows, depth + 1, id)\n\n          // Flatten the leaf rows of the rows in this group\n          const leafRows = depth\n            ? flattenBy(groupedRows, 'leafRows')\n            : groupedRows\n\n          const values = aggregateRowsToValues(leafRows, groupedRows, depth)\n\n          const row = {\n            id,\n            isGrouped: true,\n            groupByID: columnId,\n            groupByVal,\n            values,\n            subRows,\n            leafRows,\n            depth,\n            index,\n          }\n\n          subRows.forEach(subRow => {\n            groupedFlatRows.push(subRow)\n            groupedRowsById[subRow.id] = subRow\n            if (subRow.isGrouped) {\n              onlyGroupedFlatRows.push(subRow)\n              onlyGroupedRowsById[subRow.id] = subRow\n            } else {\n              nonGroupedFlatRows.push(subRow)\n              nonGroupedRowsById[subRow.id] = subRow\n            }\n          })\n\n          return row\n        }\n      )\n\n      return aggregatedGroupedRows\n    }\n\n    const groupedRows = groupUpRecursively(rows)\n\n    groupedRows.forEach(subRow => {\n      groupedFlatRows.push(subRow)\n      groupedRowsById[subRow.id] = subRow\n      if (subRow.isGrouped) {\n        onlyGroupedFlatRows.push(subRow)\n        onlyGroupedRowsById[subRow.id] = subRow\n      } else {\n        nonGroupedFlatRows.push(subRow)\n        nonGroupedRowsById[subRow.id] = subRow\n      }\n    })\n\n    // Assign the new data\n    return [\n      groupedRows,\n      groupedFlatRows,\n      groupedRowsById,\n      onlyGroupedFlatRows,\n      onlyGroupedRowsById,\n      nonGroupedFlatRows,\n      nonGroupedRowsById,\n    ]\n  }, [\n    manualGroupBy,\n    groupBy,\n    rows,\n    flatRows,\n    rowsById,\n    allColumns,\n    userAggregations,\n    groupByFn,\n  ])\n\n  const getAutoResetGroupBy = useGetLatest(autoResetGroupBy)\n\n  useMountedLayoutEffect(() => {\n    if (getAutoResetGroupBy()) {\n      dispatch({ type: actions.resetGroupBy })\n    }\n  }, [dispatch, manualGroupBy ? null : data])\n\n  Object.assign(instance, {\n    preGroupedRows: rows,\n    preGroupedFlatRow: flatRows,\n    preGroupedRowsById: rowsById,\n    groupedRows,\n    groupedFlatRows,\n    groupedRowsById,\n    onlyGroupedFlatRows,\n    onlyGroupedRowsById,\n    nonGroupedFlatRows,\n    nonGroupedRowsById,\n    rows: groupedRows,\n    flatRows: groupedFlatRows,\n    rowsById: groupedRowsById,\n    toggleGroupBy,\n    setGroupBy,\n  })\n}\n\nfunction prepareRow(row) {\n  row.allCells.forEach(cell => {\n    // Grouped cells are in the groupBy and the pivot cell for the row\n    cell.isGrouped = cell.column.isGrouped && cell.column.id === row.groupByID\n    // Placeholder cells are any columns in the groupBy that are not grouped\n    cell.isPlaceholder = !cell.isGrouped && cell.column.isGrouped\n    // Aggregated cells are not grouped, not repeated, but still have subRows\n    cell.isAggregated =\n      !cell.isGrouped && !cell.isPlaceholder && row.subRows?.length\n  })\n}\n\nexport function defaultGroupByFn(rows, columnId) {\n  return rows.reduce((prev, row, i) => {\n    // TODO: Might want to implement a key serializer here so\n    // irregular column values can still be grouped if needed?\n    const resKey = `${row.values[columnId]}`\n    prev[resKey] = Array.isArray(prev[resKey]) ? prev[resKey] : []\n    prev[resKey].push(row)\n    return prev\n  }, {})\n}\n", "const reSplitAlphaNumeric = /([0-9]+)/gm\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nexport const alphanumeric = (rowA, rowB, columnId) => {\n  let [a, b] = getRowValuesByColumnID(rowA, rowB, columnId)\n\n  // Force to strings (or \"\" for unsupported types)\n  a = toString(a)\n  b = toString(b)\n\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  a = a.split(reSplitAlphaNumeric).filter(Boolean)\n  b = b.split(reSplitAlphaNumeric).filter(Boolean)\n\n  // While\n  while (a.length && b.length) {\n    let aa = a.shift()\n    let bb = b.shift()\n\n    const an = parseInt(aa, 10)\n    const bn = parseInt(bb, 10)\n\n    const combo = [an, bn].sort()\n\n    // Both are string\n    if (isNaN(combo[0])) {\n      if (aa > bb) {\n        return 1\n      }\n      if (bb > aa) {\n        return -1\n      }\n      continue\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1])) {\n      return isNaN(an) ? -1 : 1\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1\n    }\n    if (bn > an) {\n      return -1\n    }\n  }\n\n  return a.length - b.length\n}\nexport function datetime(rowA, rowB, columnId) {\n  let [a, b] = getRowValuesByColumnID(rowA, rowB, columnId)\n\n  a = a.getTime()\n  b = b.getTime()\n\n  return compareBasic(a, b)\n}\n\nexport function basic(rowA, rowB, columnId) {\n  let [a, b] = getRowValuesByColumnID(rowA, rowB, columnId)\n\n  return compareBasic(a, b)\n}\n\nexport function string(rowA, rowB, columnId) {\n  let [a, b] = getRowValuesByColumnID(rowA, rowB, columnId)\n\n  a = a.split('').filter(Boolean)\n  b = b.split('').filter(Boolean)\n\n  while (a.length && b.length) {\n    let aa = a.shift()\n    let bb = b.shift()\n\n    let alower = aa.toLowerCase()\n    let blower = bb.toLowerCase()\n\n    // Case insensitive comparison until characters match\n    if (alower > blower) {\n      return 1\n    }\n    if (blower > alower) {\n      return -1\n    }\n    // If lowercase characters are identical\n    if (aa > bb) {\n      return 1\n    }\n    if (bb > aa) {\n      return -1\n    }\n    continue\n  }\n\n  return a.length - b.length\n}\n\nexport function number(rowA, rowB, columnId) {\n  let [a, b] = getRowValuesByColumnID(rowA, rowB, columnId)\n\n  const replaceNonNumeric = /[^0-9.]/gi\n\n  a = Number(String(a).replace(replaceNonNumeric, ''))\n  b = Number(String(b).replace(replaceNonNumeric, ''))\n\n  return compareBasic(a, b)\n}\n\n// Utils\n\nfunction compareBasic(a, b) {\n  return a === b ? 0 : a > b ? 1 : -1\n}\n\nfunction getRowValuesByColumnID(row1, row2, columnId) {\n  return [row1.values[columnId], row2.values[columnId]]\n}\n\nfunction toString(a) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return ''\n    }\n    return String(a)\n  }\n  if (typeof a === 'string') {\n    return a\n  }\n  return ''\n}\n", "import React from 'react'\n\nimport {\n  actions,\n  ensurePluginOrder,\n  defaultColumn,\n  makePropGetter,\n  useGetLatest,\n  useMountedLayoutEffect,\n} from '../publicUtils'\n\nimport { getFirstDefined, isFunction } from '../utils'\n\nimport * as sortTypes from '../sortTypes'\n\n// Actions\nactions.resetSortBy = 'resetSortBy'\nactions.setSortBy = 'setSortBy'\nactions.toggleSortBy = 'toggleSortBy'\nactions.clearSortBy = 'clearSortBy'\n\ndefaultColumn.sortType = 'alphanumeric'\ndefaultColumn.sortDescFirst = false\n\nexport const useSortBy = hooks => {\n  hooks.getSortByToggleProps = [defaultGetSortByToggleProps]\n  hooks.stateReducers.push(reducer)\n  hooks.useInstance.push(useInstance)\n}\n\nuseSortBy.pluginName = 'useSortBy'\n\nconst defaultGetSortByToggleProps = (props, { instance, column }) => {\n  const { isMultiSortEvent = e => e.shift<PERSON><PERSON> } = instance\n\n  return [\n    props,\n    {\n      onClick: column.canSort\n        ? e => {\n            e.persist()\n            column.toggleSortBy(\n              undefined,\n              !instance.disableMultiSort && isMultiSortEvent(e)\n            )\n          }\n        : undefined,\n      style: {\n        cursor: column.canSort ? 'pointer' : undefined,\n      },\n      title: column.canSort ? 'Toggle SortBy' : undefined,\n    },\n  ]\n}\n\n// Reducer\nfunction reducer(state, action, previousState, instance) {\n  if (action.type === actions.init) {\n    return {\n      sortBy: [],\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetSortBy) {\n    return {\n      ...state,\n      sortBy: instance.initialState.sortBy || [],\n    }\n  }\n\n  if (action.type === actions.clearSortBy) {\n    const { sortBy } = state\n    const newSortBy = sortBy.filter(d => d.id !== action.columnId)\n\n    return {\n      ...state,\n      sortBy: newSortBy,\n    }\n  }\n\n  if (action.type === actions.setSortBy) {\n    const { sortBy } = action\n    return {\n      ...state,\n      sortBy,\n    }\n  }\n\n  if (action.type === actions.toggleSortBy) {\n    const { columnId, desc, multi } = action\n\n    const {\n      allColumns,\n      disableMultiSort,\n      disableSortRemove,\n      disableMultiRemove,\n      maxMultiSortColCount = Number.MAX_SAFE_INTEGER,\n    } = instance\n\n    const { sortBy } = state\n\n    // Find the column for this columnId\n    const column = allColumns.find(d => d.id === columnId)\n    const { sortDescFirst } = column\n\n    // Find any existing sortBy for this column\n    const existingSortBy = sortBy.find(d => d.id === columnId)\n    const existingIndex = sortBy.findIndex(d => d.id === columnId)\n    const hasDescDefined = typeof desc !== 'undefined' && desc !== null\n\n    let newSortBy = []\n\n    // What should we do with this sort action?\n    let sortAction\n\n    if (!disableMultiSort && multi) {\n      if (existingSortBy) {\n        sortAction = 'toggle'\n      } else {\n        sortAction = 'add'\n      }\n    } else {\n      // Normal mode\n      if (existingIndex !== sortBy.length - 1 || sortBy.length !== 1) {\n        sortAction = 'replace'\n      } else if (existingSortBy) {\n        sortAction = 'toggle'\n      } else {\n        sortAction = 'replace'\n      }\n    }\n\n    // Handle toggle states that will remove the sortBy\n    if (\n      sortAction === 'toggle' && // Must be toggling\n      !disableSortRemove && // If disableSortRemove, disable in general\n      !hasDescDefined && // Must not be setting desc\n      (multi ? !disableMultiRemove : true) && // If multi, don't allow if disableMultiRemove\n      ((existingSortBy && // Finally, detect if it should indeed be removed\n        existingSortBy.desc &&\n        !sortDescFirst) ||\n        (!existingSortBy.desc && sortDescFirst))\n    ) {\n      sortAction = 'remove'\n    }\n\n    if (sortAction === 'replace') {\n      newSortBy = [\n        {\n          id: columnId,\n          desc: hasDescDefined ? desc : sortDescFirst,\n        },\n      ]\n    } else if (sortAction === 'add') {\n      newSortBy = [\n        ...sortBy,\n        {\n          id: columnId,\n          desc: hasDescDefined ? desc : sortDescFirst,\n        },\n      ]\n      // Take latest n columns\n      newSortBy.splice(0, newSortBy.length - maxMultiSortColCount)\n    } else if (sortAction === 'toggle') {\n      // This flips (or sets) the\n      newSortBy = sortBy.map(d => {\n        if (d.id === columnId) {\n          return {\n            ...d,\n            desc: hasDescDefined ? desc : !existingSortBy.desc,\n          }\n        }\n        return d\n      })\n    } else if (sortAction === 'remove') {\n      newSortBy = sortBy.filter(d => d.id !== columnId)\n    }\n\n    return {\n      ...state,\n      sortBy: newSortBy,\n    }\n  }\n}\n\nfunction useInstance(instance) {\n  const {\n    data,\n    rows,\n    flatRows,\n    allColumns,\n    orderByFn = defaultOrderByFn,\n    sortTypes: userSortTypes,\n    manualSortBy,\n    defaultCanSort,\n    disableSortBy,\n    flatHeaders,\n    state: { sortBy },\n    dispatch,\n    plugins,\n    getHooks,\n    autoResetSortBy = true,\n  } = instance\n\n  ensurePluginOrder(\n    plugins,\n    ['useFilters', 'useGlobalFilter', 'useGroupBy', 'usePivotColumns'],\n    'useSortBy'\n  )\n\n  const setSortBy = React.useCallback(\n    sortBy => {\n      dispatch({ type: actions.setSortBy, sortBy })\n    },\n    [dispatch]\n  )\n\n  // Updates sorting based on a columnId, desc flag and multi flag\n  const toggleSortBy = React.useCallback(\n    (columnId, desc, multi) => {\n      dispatch({ type: actions.toggleSortBy, columnId, desc, multi })\n    },\n    [dispatch]\n  )\n\n  // use reference to avoid memory leak in #1608\n  const getInstance = useGetLatest(instance)\n\n  // Add the getSortByToggleProps method to columns and headers\n  flatHeaders.forEach(column => {\n    const {\n      accessor,\n      canSort: defaultColumnCanSort,\n      disableSortBy: columnDisableSortBy,\n      id,\n    } = column\n\n    const canSort = accessor\n      ? getFirstDefined(\n          columnDisableSortBy === true ? false : undefined,\n          disableSortBy === true ? false : undefined,\n          true\n        )\n      : getFirstDefined(defaultCanSort, defaultColumnCanSort, false)\n\n    column.canSort = canSort\n\n    if (column.canSort) {\n      column.toggleSortBy = (desc, multi) =>\n        toggleSortBy(column.id, desc, multi)\n\n      column.clearSortBy = () => {\n        dispatch({ type: actions.clearSortBy, columnId: column.id })\n      }\n    }\n\n    column.getSortByToggleProps = makePropGetter(\n      getHooks().getSortByToggleProps,\n      {\n        instance: getInstance(),\n        column,\n      }\n    )\n\n    const columnSort = sortBy.find(d => d.id === id)\n    column.isSorted = !!columnSort\n    column.sortedIndex = sortBy.findIndex(d => d.id === id)\n    column.isSortedDesc = column.isSorted ? columnSort.desc : undefined\n  })\n\n  const [sortedRows, sortedFlatRows] = React.useMemo(() => {\n    if (manualSortBy || !sortBy.length) {\n      return [rows, flatRows]\n    }\n\n    const sortedFlatRows = []\n\n    // Filter out sortBys that correspond to non existing columns\n    const availableSortBy = sortBy.filter(sort =>\n      allColumns.find(col => col.id === sort.id)\n    )\n\n    const sortData = rows => {\n      // Use the orderByFn to compose multiple sortBy's together.\n      // This will also perform a stable sorting using the row index\n      // if needed.\n      const sortedData = orderByFn(\n        rows,\n        availableSortBy.map(sort => {\n          // Support custom sorting methods for each column\n          const column = allColumns.find(d => d.id === sort.id)\n\n          if (!column) {\n            throw new Error(\n              `React-Table: Could not find a column with id: ${sort.id} while sorting`\n            )\n          }\n\n          const { sortType } = column\n\n          // Look up sortBy functions in this order:\n          // column function\n          // column string lookup on user sortType\n          // column string lookup on built-in sortType\n          // default function\n          // default string lookup on user sortType\n          // default string lookup on built-in sortType\n          const sortMethod =\n            isFunction(sortType) ||\n            (userSortTypes || {})[sortType] ||\n            sortTypes[sortType]\n\n          if (!sortMethod) {\n            throw new Error(\n              `React-Table: Could not find a valid sortType of '${sortType}' for column '${sort.id}'.`\n            )\n          }\n\n          // Return the correct sortFn.\n          // This function should always return in ascending order\n          return (a, b) => sortMethod(a, b, sort.id, sort.desc)\n        }),\n        // Map the directions\n        availableSortBy.map(sort => {\n          // Detect and use the sortInverted option\n          const column = allColumns.find(d => d.id === sort.id)\n\n          if (column && column.sortInverted) {\n            return sort.desc\n          }\n\n          return !sort.desc\n        })\n      )\n\n      // If there are sub-rows, sort them\n      sortedData.forEach(row => {\n        sortedFlatRows.push(row)\n        if (!row.subRows || row.subRows.length === 0) {\n          return\n        }\n        row.subRows = sortData(row.subRows)\n      })\n\n      return sortedData\n    }\n\n    return [sortData(rows), sortedFlatRows]\n  }, [\n    manualSortBy,\n    sortBy,\n    rows,\n    flatRows,\n    allColumns,\n    orderByFn,\n    userSortTypes,\n  ])\n\n  const getAutoResetSortBy = useGetLatest(autoResetSortBy)\n\n  useMountedLayoutEffect(() => {\n    if (getAutoResetSortBy()) {\n      dispatch({ type: actions.resetSortBy })\n    }\n  }, [manualSortBy ? null : data])\n\n  Object.assign(instance, {\n    preSortedRows: rows,\n    preSortedFlatRows: flatRows,\n    sortedRows,\n    sortedFlatRows,\n    rows: sortedRows,\n    flatRows: sortedFlatRows,\n    setSortBy,\n    toggleSortBy,\n  })\n}\n\nexport function defaultOrderByFn(arr, funcs, dirs) {\n  return [...arr].sort((rowA, rowB) => {\n    for (let i = 0; i < funcs.length; i += 1) {\n      const sortFn = funcs[i]\n      const desc = dirs[i] === false || dirs[i] === 'desc'\n      const sortInt = sortFn(rowA, rowB)\n      if (sortInt !== 0) {\n        return desc ? -sortInt : sortInt\n      }\n    }\n    return dirs[0] ? rowA.index - rowB.index : rowB.index - rowA.index\n  })\n}\n", "import React from 'react'\n\n//\n\nimport {\n  actions,\n  ensurePluginOrder,\n  functionalUpdate,\n  useMountedLayoutEffect,\n  useGetLatest,\n} from '../publicUtils'\n\nimport { expandRows } from '../utils'\n\nconst pluginName = 'usePagination'\n\n// Actions\nactions.resetPage = 'resetPage'\nactions.gotoPage = 'gotoPage'\nactions.setPageSize = 'setPageSize'\n\nexport const usePagination = hooks => {\n  hooks.stateReducers.push(reducer)\n  hooks.useInstance.push(useInstance)\n}\n\nusePagination.pluginName = pluginName\n\nfunction reducer(state, action, previousState, instance) {\n  if (action.type === actions.init) {\n    return {\n      pageSize: 10,\n      pageIndex: 0,\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetPage) {\n    return {\n      ...state,\n      pageIndex: instance.initialState.pageIndex || 0,\n    }\n  }\n\n  if (action.type === actions.gotoPage) {\n    const { pageCount, page } = instance\n    const newPageIndex = functionalUpdate(action.pageIndex, state.pageIndex)\n    let canNavigate = false\n\n    if (newPageIndex > state.pageIndex) {\n      // next page\n      canNavigate =\n        pageCount === -1\n          ? page.length >= state.pageSize\n          : newPageIndex < pageCount\n    } else if (newPageIndex < state.pageIndex) {\n      // prev page\n      canNavigate = newPageIndex > -1\n    }\n\n    if (!canNavigate) {\n      return state\n    }\n\n    return {\n      ...state,\n      pageIndex: newPageIndex,\n    }\n  }\n\n  if (action.type === actions.setPageSize) {\n    const { pageSize } = action\n    const topRowIndex = state.pageSize * state.pageIndex\n    const pageIndex = Math.floor(topRowIndex / pageSize)\n\n    return {\n      ...state,\n      pageIndex,\n      pageSize,\n    }\n  }\n}\n\nfunction useInstance(instance) {\n  const {\n    rows,\n    autoResetPage = true,\n    manualExpandedKey = 'expanded',\n    plugins,\n    pageCount: userPageCount,\n    paginateExpandedRows = true,\n    expandSubRows = true,\n    state: {\n      pageSize,\n      pageIndex,\n      expanded,\n      globalFilter,\n      filters,\n      groupBy,\n      sortBy,\n    },\n    dispatch,\n    data,\n    manualPagination,\n  } = instance\n\n  ensurePluginOrder(\n    plugins,\n    ['useGlobalFilter', 'useFilters', 'useGroupBy', 'useSortBy', 'useExpanded'],\n    'usePagination'\n  )\n\n  const getAutoResetPage = useGetLatest(autoResetPage)\n\n  useMountedLayoutEffect(() => {\n    if (getAutoResetPage()) {\n      dispatch({ type: actions.resetPage })\n    }\n  }, [\n    dispatch,\n    manualPagination ? null : data,\n    globalFilter,\n    filters,\n    groupBy,\n    sortBy,\n  ])\n\n  const pageCount = manualPagination\n    ? userPageCount\n    : Math.ceil(rows.length / pageSize)\n\n  const pageOptions = React.useMemo(\n    () =>\n      pageCount > 0\n        ? [...new Array(pageCount)].fill(null).map((d, i) => i)\n        : [],\n    [pageCount]\n  )\n\n  const page = React.useMemo(() => {\n    let page\n\n    if (manualPagination) {\n      page = rows\n    } else {\n      const pageStart = pageSize * pageIndex\n      const pageEnd = pageStart + pageSize\n\n      page = rows.slice(pageStart, pageEnd)\n    }\n\n    if (paginateExpandedRows) {\n      return page\n    }\n\n    return expandRows(page, { manualExpandedKey, expanded, expandSubRows })\n  }, [\n    expandSubRows,\n    expanded,\n    manualExpandedKey,\n    manualPagination,\n    pageIndex,\n    pageSize,\n    paginateExpandedRows,\n    rows,\n  ])\n\n  const canPreviousPage = pageIndex > 0\n  const canNextPage =\n    pageCount === -1 ? page.length >= pageSize : pageIndex < pageCount - 1\n\n  const gotoPage = React.useCallback(\n    pageIndex => {\n      dispatch({ type: actions.gotoPage, pageIndex })\n    },\n    [dispatch]\n  )\n\n  const previousPage = React.useCallback(() => {\n    return gotoPage(old => old - 1)\n  }, [gotoPage])\n\n  const nextPage = React.useCallback(() => {\n    return gotoPage(old => old + 1)\n  }, [gotoPage])\n\n  const setPageSize = React.useCallback(\n    pageSize => {\n      dispatch({ type: actions.setPageSize, pageSize })\n    },\n    [dispatch]\n  )\n\n  Object.assign(instance, {\n    pageOptions,\n    pageCount,\n    page,\n    canPreviousPage,\n    canNextPage,\n    gotoPage,\n    previousPage,\n    nextPage,\n    setPageSize,\n  })\n}\n", "/* istanbul ignore file */\n\nimport {\n  actions,\n  makePropGetter,\n  ensurePluginOrder,\n  useMountedLayoutEffect,\n  useGetLatest,\n} from '../publicUtils'\n\nimport { flattenColumns, getFirstDefined } from '../utils'\n\n// Actions\nactions.resetPivot = 'resetPivot'\nactions.togglePivot = 'togglePivot'\n\nexport const _UNSTABLE_usePivotColumns = hooks => {\n  hooks.getPivotToggleProps = [defaultGetPivotToggleProps]\n  hooks.stateReducers.push(reducer)\n  hooks.useInstanceAfterData.push(useInstanceAfterData)\n  hooks.allColumns.push(allColumns)\n  hooks.accessValue.push(accessValue)\n  hooks.materializedColumns.push(materializedColumns)\n  hooks.materializedColumnsDeps.push(materializedColumnsDeps)\n  hooks.visibleColumns.push(visibleColumns)\n  hooks.visibleColumnsDeps.push(visibleColumnsDeps)\n  hooks.useInstance.push(useInstance)\n  hooks.prepareRow.push(prepareRow)\n}\n\n_UNSTABLE_usePivotColumns.pluginName = 'usePivotColumns'\n\nconst defaultPivotColumns = []\n\nconst defaultGetPivotToggleProps = (props, { header }) => [\n  props,\n  {\n    onClick: header.canPivot\n      ? e => {\n          e.persist()\n          header.togglePivot()\n        }\n      : undefined,\n    style: {\n      cursor: header.canPivot ? 'pointer' : undefined,\n    },\n    title: 'Toggle Pivot',\n  },\n]\n\n// Reducer\nfunction reducer(state, action, previousState, instance) {\n  if (action.type === actions.init) {\n    return {\n      pivotColumns: defaultPivotColumns,\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetPivot) {\n    return {\n      ...state,\n      pivotColumns: instance.initialState.pivotColumns || defaultPivotColumns,\n    }\n  }\n\n  if (action.type === actions.togglePivot) {\n    const { columnId, value: setPivot } = action\n\n    const resolvedPivot =\n      typeof setPivot !== 'undefined'\n        ? setPivot\n        : !state.pivotColumns.includes(columnId)\n\n    if (resolvedPivot) {\n      return {\n        ...state,\n        pivotColumns: [...state.pivotColumns, columnId],\n      }\n    }\n\n    return {\n      ...state,\n      pivotColumns: state.pivotColumns.filter(d => d !== columnId),\n    }\n  }\n}\n\nfunction useInstanceAfterData(instance) {\n  instance.allColumns.forEach(column => {\n    column.isPivotSource = instance.state.pivotColumns.includes(column.id)\n  })\n}\n\nfunction allColumns(columns, { instance }) {\n  columns.forEach(column => {\n    column.isPivotSource = instance.state.pivotColumns.includes(column.id)\n    column.uniqueValues = new Set()\n  })\n  return columns\n}\n\nfunction accessValue(value, { column }) {\n  if (column.uniqueValues && typeof value !== 'undefined') {\n    column.uniqueValues.add(value)\n  }\n  return value\n}\n\nfunction materializedColumns(materialized, { instance }) {\n  const { allColumns, state } = instance\n\n  if (!state.pivotColumns.length || !state.groupBy || !state.groupBy.length) {\n    return materialized\n  }\n\n  const pivotColumns = state.pivotColumns\n    .map(id => allColumns.find(d => d.id === id))\n    .filter(Boolean)\n\n  const sourceColumns = allColumns.filter(\n    d =>\n      !d.isPivotSource &&\n      !state.groupBy.includes(d.id) &&\n      !state.pivotColumns.includes(d.id)\n  )\n\n  const buildPivotColumns = (depth = 0, parent, pivotFilters = []) => {\n    const pivotColumn = pivotColumns[depth]\n\n    if (!pivotColumn) {\n      return sourceColumns.map(sourceColumn => {\n        // TODO: We could offer support here for renesting pivoted\n        // columns inside copies of their header groups. For now,\n        // that seems like it would be (1) overkill on nesting, considering\n        // you already get nesting for every pivot level and (2)\n        // really hard. :)\n\n        return {\n          ...sourceColumn,\n          canPivot: false,\n          isPivoted: true,\n          parent,\n          depth: depth,\n          id: `${parent ? `${parent.id}.${sourceColumn.id}` : sourceColumn.id}`,\n          accessor: (originalRow, i, row) => {\n            if (pivotFilters.every(filter => filter(row))) {\n              return row.values[sourceColumn.id]\n            }\n          },\n        }\n      })\n    }\n\n    const uniqueValues = Array.from(pivotColumn.uniqueValues).sort()\n\n    return uniqueValues.map(uniqueValue => {\n      const columnGroup = {\n        ...pivotColumn,\n        Header:\n          pivotColumn.PivotHeader || typeof pivotColumn.header === 'string'\n            ? `${pivotColumn.Header}: ${uniqueValue}`\n            : uniqueValue,\n        isPivotGroup: true,\n        parent,\n        depth,\n        id: parent\n          ? `${parent.id}.${pivotColumn.id}.${uniqueValue}`\n          : `${pivotColumn.id}.${uniqueValue}`,\n        pivotValue: uniqueValue,\n      }\n\n      columnGroup.columns = buildPivotColumns(depth + 1, columnGroup, [\n        ...pivotFilters,\n        row => row.values[pivotColumn.id] === uniqueValue,\n      ])\n\n      return columnGroup\n    })\n  }\n\n  const newMaterialized = flattenColumns(buildPivotColumns())\n\n  return [...materialized, ...newMaterialized]\n}\n\nfunction materializedColumnsDeps(\n  deps,\n  {\n    instance: {\n      state: { pivotColumns, groupBy },\n    },\n  }\n) {\n  return [...deps, pivotColumns, groupBy]\n}\n\nfunction visibleColumns(visibleColumns, { instance: { state } }) {\n  visibleColumns = visibleColumns.filter(d => !d.isPivotSource)\n\n  if (state.pivotColumns.length && state.groupBy && state.groupBy.length) {\n    visibleColumns = visibleColumns.filter(\n      column => column.isGrouped || column.isPivoted\n    )\n  }\n\n  return visibleColumns\n}\n\nfunction visibleColumnsDeps(deps, { instance }) {\n  return [...deps, instance.state.pivotColumns, instance.state.groupBy]\n}\n\nfunction useInstance(instance) {\n  const {\n    columns,\n    allColumns,\n    flatHeaders,\n    // pivotFn = defaultPivotFn,\n    // manualPivot,\n    getHooks,\n    plugins,\n    dispatch,\n    autoResetPivot = true,\n    manaulPivot,\n    disablePivot,\n    defaultCanPivot,\n  } = instance\n\n  ensurePluginOrder(plugins, ['useGroupBy'], 'usePivotColumns')\n\n  const getInstance = useGetLatest(instance)\n\n  allColumns.forEach(column => {\n    const {\n      accessor,\n      defaultPivot: defaultColumnPivot,\n      disablePivot: columnDisablePivot,\n    } = column\n\n    column.canPivot = accessor\n      ? getFirstDefined(\n          column.canPivot,\n          columnDisablePivot === true ? false : undefined,\n          disablePivot === true ? false : undefined,\n          true\n        )\n      : getFirstDefined(\n          column.canPivot,\n          defaultColumnPivot,\n          defaultCanPivot,\n          false\n        )\n\n    if (column.canPivot) {\n      column.togglePivot = () => instance.togglePivot(column.id)\n    }\n\n    column.Aggregated = column.Aggregated || column.Cell\n  })\n\n  const togglePivot = (columnId, value) => {\n    dispatch({ type: actions.togglePivot, columnId, value })\n  }\n\n  flatHeaders.forEach(header => {\n    header.getPivotToggleProps = makePropGetter(\n      getHooks().getPivotToggleProps,\n      {\n        instance: getInstance(),\n        header,\n      }\n    )\n  })\n\n  const getAutoResetPivot = useGetLatest(autoResetPivot)\n\n  useMountedLayoutEffect(() => {\n    if (getAutoResetPivot()) {\n      dispatch({ type: actions.resetPivot })\n    }\n  }, [dispatch, manaulPivot ? null : columns])\n\n  Object.assign(instance, {\n    togglePivot,\n  })\n}\n\nfunction prepareRow(row) {\n  row.allCells.forEach(cell => {\n    // Grouped cells are in the pivotColumns and the pivot cell for the row\n    cell.isPivoted = cell.column.isPivoted\n  })\n}\n", "import React from 'react'\n\nimport {\n  actions,\n  makePropGetter,\n  ensurePluginOrder,\n  useGetLatest,\n  useMountedLayoutEffect,\n} from '../publicUtils'\n\nconst pluginName = 'useRowSelect'\n\n// Actions\nactions.resetSelectedRows = 'resetSelectedRows'\nactions.toggleAllRowsSelected = 'toggleAllRowsSelected'\nactions.toggleRowSelected = 'toggleRowSelected'\nactions.toggleAllPageRowsSelected = 'toggleAllPageRowsSelected'\n\nexport const useRowSelect = hooks => {\n  hooks.getToggleRowSelectedProps = [defaultGetToggleRowSelectedProps]\n  hooks.getToggleAllRowsSelectedProps = [defaultGetToggleAllRowsSelectedProps]\n  hooks.getToggleAllPageRowsSelectedProps = [\n    defaultGetToggleAllPageRowsSelectedProps,\n  ]\n  hooks.stateReducers.push(reducer)\n  hooks.useInstance.push(useInstance)\n  hooks.prepareRow.push(prepareRow)\n}\n\nuseRowSelect.pluginName = pluginName\n\nconst defaultGetToggleRowSelectedProps = (props, { instance, row }) => {\n  const { manualRowSelectedKey = 'isSelected' } = instance\n  let checked = false\n\n  if (row.original && row.original[manualRowSelectedKey]) {\n    checked = true\n  } else {\n    checked = row.isSelected\n  }\n\n  return [\n    props,\n    {\n      onChange: e => {\n        row.toggleRowSelected(e.target.checked)\n      },\n      style: {\n        cursor: 'pointer',\n      },\n      checked,\n      title: 'Toggle Row Selected',\n      indeterminate: row.isSomeSelected,\n    },\n  ]\n}\n\nconst defaultGetToggleAllRowsSelectedProps = (props, { instance }) => [\n  props,\n  {\n    onChange: e => {\n      instance.toggleAllRowsSelected(e.target.checked)\n    },\n    style: {\n      cursor: 'pointer',\n    },\n    checked: instance.isAllRowsSelected,\n    title: 'Toggle All Rows Selected',\n    indeterminate: Boolean(\n      !instance.isAllRowsSelected &&\n        Object.keys(instance.state.selectedRowIds).length\n    ),\n  },\n]\n\nconst defaultGetToggleAllPageRowsSelectedProps = (props, { instance }) => [\n  props,\n  {\n    onChange(e) {\n      instance.toggleAllPageRowsSelected(e.target.checked)\n    },\n    style: {\n      cursor: 'pointer',\n    },\n    checked: instance.isAllPageRowsSelected,\n    title: 'Toggle All Current Page Rows Selected',\n    indeterminate: Boolean(\n      !instance.isAllPageRowsSelected &&\n        instance.page.some(({ id }) => instance.state.selectedRowIds[id])\n    ),\n  },\n]\n\n// eslint-disable-next-line max-params\nfunction reducer(state, action, previousState, instance) {\n  if (action.type === actions.init) {\n    return {\n      selectedRowIds: {},\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetSelectedRows) {\n    return {\n      ...state,\n      selectedRowIds: instance.initialState.selectedRowIds || {},\n    }\n  }\n\n  if (action.type === actions.toggleAllRowsSelected) {\n    const { value: setSelected } = action\n    const {\n      isAllRowsSelected,\n      rowsById,\n      nonGroupedRowsById = rowsById,\n    } = instance\n\n    const selectAll =\n      typeof setSelected !== 'undefined' ? setSelected : !isAllRowsSelected\n\n    // Only remove/add the rows that are visible on the screen\n    //  Leave all the other rows that are selected alone.\n    const selectedRowIds = Object.assign({}, state.selectedRowIds)\n\n    if (selectAll) {\n      Object.keys(nonGroupedRowsById).forEach(rowId => {\n        selectedRowIds[rowId] = true\n      })\n    } else {\n      Object.keys(nonGroupedRowsById).forEach(rowId => {\n        delete selectedRowIds[rowId]\n      })\n    }\n\n    return {\n      ...state,\n      selectedRowIds,\n    }\n  }\n\n  if (action.type === actions.toggleRowSelected) {\n    const { id, value: setSelected } = action\n    const { rowsById, selectSubRows = true, getSubRows } = instance\n    const isSelected = state.selectedRowIds[id]\n    const shouldExist =\n      typeof setSelected !== 'undefined' ? setSelected : !isSelected\n\n    if (isSelected === shouldExist) {\n      return state\n    }\n\n    const newSelectedRowIds = { ...state.selectedRowIds }\n\n    const handleRowById = id => {\n      const row = rowsById[id]\n\n      if (row) {\n        if (!row.isGrouped) {\n          if (shouldExist) {\n            newSelectedRowIds[id] = true\n          } else {\n            delete newSelectedRowIds[id]\n          }\n        }\n\n        if (selectSubRows && getSubRows(row)) {\n          return getSubRows(row).forEach(row => handleRowById(row.id))\n        }\n      }\n    }\n\n    handleRowById(id)\n\n    return {\n      ...state,\n      selectedRowIds: newSelectedRowIds,\n    }\n  }\n\n  if (action.type === actions.toggleAllPageRowsSelected) {\n    const { value: setSelected } = action\n    const {\n      page,\n      rowsById,\n      selectSubRows = true,\n      isAllPageRowsSelected,\n      getSubRows,\n    } = instance\n\n    const selectAll =\n      typeof setSelected !== 'undefined' ? setSelected : !isAllPageRowsSelected\n\n    const newSelectedRowIds = { ...state.selectedRowIds }\n\n    const handleRowById = id => {\n      const row = rowsById[id]\n\n      if (!row.isGrouped) {\n        if (selectAll) {\n          newSelectedRowIds[id] = true\n        } else {\n          delete newSelectedRowIds[id]\n        }\n      }\n\n      if (selectSubRows && getSubRows(row)) {\n        return getSubRows(row).forEach(row => handleRowById(row.id))\n      }\n    }\n\n    page.forEach(row => handleRowById(row.id))\n\n    return {\n      ...state,\n      selectedRowIds: newSelectedRowIds,\n    }\n  }\n  return state\n}\n\nfunction useInstance(instance) {\n  const {\n    data,\n    rows,\n    getHooks,\n    plugins,\n    rowsById,\n    nonGroupedRowsById = rowsById,\n    autoResetSelectedRows = true,\n    state: { selectedRowIds },\n    selectSubRows = true,\n    dispatch,\n    page,\n    getSubRows,\n  } = instance\n\n  ensurePluginOrder(\n    plugins,\n    ['useFilters', 'useGroupBy', 'useSortBy', 'useExpanded', 'usePagination'],\n    'useRowSelect'\n  )\n\n  const selectedFlatRows = React.useMemo(() => {\n    const selectedFlatRows = []\n\n    rows.forEach(row => {\n      const isSelected = selectSubRows\n        ? getRowIsSelected(row, selectedRowIds, getSubRows)\n        : !!selectedRowIds[row.id]\n      row.isSelected = !!isSelected\n      row.isSomeSelected = isSelected === null\n\n      if (isSelected) {\n        selectedFlatRows.push(row)\n      }\n    })\n\n    return selectedFlatRows\n  }, [rows, selectSubRows, selectedRowIds, getSubRows])\n\n  let isAllRowsSelected = Boolean(\n    Object.keys(nonGroupedRowsById).length && Object.keys(selectedRowIds).length\n  )\n\n  let isAllPageRowsSelected = isAllRowsSelected\n\n  if (isAllRowsSelected) {\n    if (Object.keys(nonGroupedRowsById).some(id => !selectedRowIds[id])) {\n      isAllRowsSelected = false\n    }\n  }\n\n  if (!isAllRowsSelected) {\n    if (page && page.length && page.some(({ id }) => !selectedRowIds[id])) {\n      isAllPageRowsSelected = false\n    }\n  }\n\n  const getAutoResetSelectedRows = useGetLatest(autoResetSelectedRows)\n\n  useMountedLayoutEffect(() => {\n    if (getAutoResetSelectedRows()) {\n      dispatch({ type: actions.resetSelectedRows })\n    }\n  }, [dispatch, data])\n\n  const toggleAllRowsSelected = React.useCallback(\n    value => dispatch({ type: actions.toggleAllRowsSelected, value }),\n    [dispatch]\n  )\n\n  const toggleAllPageRowsSelected = React.useCallback(\n    value => dispatch({ type: actions.toggleAllPageRowsSelected, value }),\n    [dispatch]\n  )\n\n  const toggleRowSelected = React.useCallback(\n    (id, value) => dispatch({ type: actions.toggleRowSelected, id, value }),\n    [dispatch]\n  )\n\n  const getInstance = useGetLatest(instance)\n\n  const getToggleAllRowsSelectedProps = makePropGetter(\n    getHooks().getToggleAllRowsSelectedProps,\n    { instance: getInstance() }\n  )\n\n  const getToggleAllPageRowsSelectedProps = makePropGetter(\n    getHooks().getToggleAllPageRowsSelectedProps,\n    { instance: getInstance() }\n  )\n\n  Object.assign(instance, {\n    selectedFlatRows,\n    isAllRowsSelected,\n    isAllPageRowsSelected,\n    toggleRowSelected,\n    toggleAllRowsSelected,\n    getToggleAllRowsSelectedProps,\n    getToggleAllPageRowsSelectedProps,\n    toggleAllPageRowsSelected,\n  })\n}\n\nfunction prepareRow(row, { instance }) {\n  row.toggleRowSelected = set => instance.toggleRowSelected(row.id, set)\n\n  row.getToggleRowSelectedProps = makePropGetter(\n    instance.getHooks().getToggleRowSelectedProps,\n    { instance: instance, row }\n  )\n}\n\nfunction getRowIsSelected(row, selectedRowIds, getSubRows) {\n  if (selectedRowIds[row.id]) {\n    return true\n  }\n\n  const subRows = getSubRows(row)\n\n  if (subRows && subRows.length) {\n    let allChildrenSelected = true\n    let someSelected = false\n\n    subRows.forEach(subRow => {\n      // Bail out early if we know both of these\n      if (someSelected && !allChildrenSelected) {\n        return\n      }\n\n      if (getRowIsSelected(subRow, selectedRowIds, getSubRows)) {\n        someSelected = true\n      } else {\n        allChildrenSelected = false\n      }\n    })\n    return allChildrenSelected ? true : someSelected ? null : false\n  }\n\n  return false\n}\n", "import React from 'react'\n\nimport {\n  actions,\n  functionalUpdate,\n  useMountedLayoutEffect,\n  useGetLatest,\n} from '../publicUtils'\n\nconst defaultInitialRowStateAccessor = row => ({})\nconst defaultInitialCellStateAccessor = cell => ({})\n\n// Actions\nactions.setRowState = 'setRowState'\nactions.setCellState = 'setCellState'\nactions.resetRowState = 'resetRowState'\n\nexport const useRowState = hooks => {\n  hooks.stateReducers.push(reducer)\n  hooks.useInstance.push(useInstance)\n  hooks.prepareRow.push(prepareRow)\n}\n\nuseRowState.pluginName = 'useRowState'\n\nfunction reducer(state, action, previousState, instance) {\n  const {\n    initialRowStateAccessor = defaultInitialRowStateAccessor,\n    initialCellStateAccessor = defaultInitialCellStateAccessor,\n    rowsById,\n  } = instance\n\n  if (action.type === actions.init) {\n    return {\n      rowState: {},\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetRowState) {\n    return {\n      ...state,\n      rowState: instance.initialState.rowState || {},\n    }\n  }\n\n  if (action.type === actions.setRowState) {\n    const { rowId, value } = action\n\n    const oldRowState =\n      typeof state.rowState[rowId] !== 'undefined'\n        ? state.rowState[rowId]\n        : initialRowStateAccessor(rowsById[rowId])\n\n    return {\n      ...state,\n      rowState: {\n        ...state.rowState,\n        [rowId]: functionalUpdate(value, oldRowState),\n      },\n    }\n  }\n\n  if (action.type === actions.setCellState) {\n    const { rowId, columnId, value } = action\n\n    const oldRowState =\n      typeof state.rowState[rowId] !== 'undefined'\n        ? state.rowState[rowId]\n        : initialRowStateAccessor(rowsById[rowId])\n\n    const oldCellState =\n      typeof oldRowState?.cellState?.[columnId] !== 'undefined'\n        ? oldRowState.cellState[columnId]\n        : initialCellStateAccessor(\n            rowsById[rowId]?.cells?.find(cell => cell.column.id === columnId)\n          )\n\n    return {\n      ...state,\n      rowState: {\n        ...state.rowState,\n        [rowId]: {\n          ...oldRowState,\n          cellState: {\n            ...(oldRowState.cellState || {}),\n            [columnId]: functionalUpdate(value, oldCellState),\n          },\n        },\n      },\n    }\n  }\n}\n\nfunction useInstance(instance) {\n  const { autoResetRowState = true, data, dispatch } = instance\n\n  const setRowState = React.useCallback(\n    (rowId, value) =>\n      dispatch({\n        type: actions.setRowState,\n        rowId,\n        value,\n      }),\n    [dispatch]\n  )\n\n  const setCellState = React.useCallback(\n    (rowId, columnId, value) =>\n      dispatch({\n        type: actions.setCellState,\n        rowId,\n        columnId,\n        value,\n      }),\n    [dispatch]\n  )\n\n  const getAutoResetRowState = useGetLatest(autoResetRowState)\n\n  useMountedLayoutEffect(() => {\n    if (getAutoResetRowState()) {\n      dispatch({ type: actions.resetRowState })\n    }\n  }, [data])\n\n  Object.assign(instance, {\n    setRowState,\n    setCellState,\n  })\n}\n\nfunction prepareRow(row, { instance }) {\n  const {\n    initialRowStateAccessor = defaultInitialRowStateAccessor,\n    initialCellStateAccessor = defaultInitialCellStateAccessor,\n    state: { rowState },\n  } = instance\n\n  if (row) {\n    row.state =\n      typeof rowState[row.id] !== 'undefined'\n        ? rowState[row.id]\n        : initialRowStateAccessor(row)\n\n    row.setState = updater => {\n      return instance.setRowState(row.id, updater)\n    }\n\n    row.cells.forEach(cell => {\n      if (!row.state.cellState) {\n        row.state.cellState = {}\n      }\n\n      cell.state =\n        typeof row.state.cellState[cell.column.id] !== 'undefined'\n          ? row.state.cellState[cell.column.id]\n          : initialCellStateAccessor(cell)\n\n      cell.setState = updater => {\n        return instance.setCellState(row.id, cell.column.id, updater)\n      }\n    })\n  }\n}\n", "import React from 'react'\n\nimport { functionalUpdate, actions } from '../publicUtils'\n\n// Actions\nactions.resetColumnOrder = 'resetColumnOrder'\nactions.setColumnOrder = 'setColumnOrder'\n\nexport const useColumnOrder = hooks => {\n  hooks.stateReducers.push(reducer)\n  hooks.visibleColumnsDeps.push((deps, { instance }) => {\n    return [...deps, instance.state.columnOrder]\n  })\n  hooks.visibleColumns.push(visibleColumns)\n  hooks.useInstance.push(useInstance)\n}\n\nuseColumnOrder.pluginName = 'useColumnOrder'\n\nfunction reducer(state, action, previousState, instance) {\n  if (action.type === actions.init) {\n    return {\n      columnOrder: [],\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetColumnOrder) {\n    return {\n      ...state,\n      columnOrder: instance.initialState.columnOrder || [],\n    }\n  }\n\n  if (action.type === actions.setColumnOrder) {\n    return {\n      ...state,\n      columnOrder: functionalUpdate(action.columnOrder, state.columnOrder),\n    }\n  }\n}\n\nfunction visibleColumns(\n  columns,\n  {\n    instance: {\n      state: { columnOrder },\n    },\n  }\n) {\n  // If there is no order, return the normal columns\n  if (!columnOrder || !columnOrder.length) {\n    return columns\n  }\n\n  const columnOrderCopy = [...columnOrder]\n\n  // If there is an order, make a copy of the columns\n  const columnsCopy = [...columns]\n\n  // And make a new ordered array of the columns\n  const columnsInOrder = []\n\n  // Loop over the columns and place them in order into the new array\n  while (columnsCopy.length && columnOrderCopy.length) {\n    const targetColumnId = columnOrderCopy.shift()\n    const foundIndex = columnsCopy.findIndex(d => d.id === targetColumnId)\n    if (foundIndex > -1) {\n      columnsInOrder.push(columnsCopy.splice(foundIndex, 1)[0])\n    }\n  }\n\n  // If there are any columns left, add them to the end\n  return [...columnsInOrder, ...columnsCopy]\n}\n\nfunction useInstance(instance) {\n  const { dispatch } = instance\n\n  instance.setColumnOrder = React.useCallback(\n    columnOrder => {\n      return dispatch({ type: actions.setColumnOrder, columnOrder })\n    },\n    [dispatch]\n  )\n}\n", "import React from 'react'\n\nimport {\n  actions,\n  defaultColumn,\n  makePropGetter,\n  useGetLatest,\n  ensurePluginOrder,\n  useMountedLayoutEffect,\n} from '../publicUtils'\n\nimport { getFirstDefined, passiveEventSupported } from '../utils'\n\n// Default Column\ndefaultColumn.canResize = true\n\n// Actions\nactions.columnStartResizing = 'columnStartResizing'\nactions.columnResizing = 'columnResizing'\nactions.columnDoneResizing = 'columnDoneResizing'\nactions.resetResize = 'resetResize'\n\nexport const useResizeColumns = hooks => {\n  hooks.getResizerProps = [defaultGetResizerProps]\n  hooks.getHeaderProps.push({\n    style: {\n      position: 'relative',\n    },\n  })\n  hooks.stateReducers.push(reducer)\n  hooks.useInstance.push(useInstance)\n  hooks.useInstanceBeforeDimensions.push(useInstanceBeforeDimensions)\n}\n\nconst defaultGetResizerProps = (props, { instance, header }) => {\n  const { dispatch } = instance\n\n  const onResizeStart = (e, header) => {\n    let isTouchEvent = false\n    if (e.type === 'touchstart') {\n      // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n      if (e.touches && e.touches.length > 1) {\n        return\n      }\n      isTouchEvent = true\n    }\n    const headersToResize = getLeafHeaders(header)\n    const headerIdWidths = headersToResize.map(d => [d.id, d.totalWidth])\n\n    const clientX = isTouchEvent ? Math.round(e.touches[0].clientX) : e.clientX\n\n    let raf\n    let mostRecentClientX\n\n    const dispatchEnd = () => {\n      window.cancelAnimationFrame(raf)\n      raf = null\n      dispatch({ type: actions.columnDoneResizing })\n    }\n    const dispatchMove = () => {\n      window.cancelAnimationFrame(raf)\n      raf = null\n      dispatch({ type: actions.columnResizing, clientX: mostRecentClientX })\n    }\n\n    const scheduleDispatchMoveOnNextAnimationFrame = clientXPos => {\n      mostRecentClientX = clientXPos\n      if (!raf) {\n        raf = window.requestAnimationFrame(dispatchMove)\n      }\n    }\n\n    const handlersAndEvents = {\n      mouse: {\n        moveEvent: 'mousemove',\n        moveHandler: e => scheduleDispatchMoveOnNextAnimationFrame(e.clientX),\n        upEvent: 'mouseup',\n        upHandler: e => {\n          document.removeEventListener(\n            'mousemove',\n            handlersAndEvents.mouse.moveHandler\n          )\n          document.removeEventListener(\n            'mouseup',\n            handlersAndEvents.mouse.upHandler\n          )\n          dispatchEnd()\n        },\n      },\n      touch: {\n        moveEvent: 'touchmove',\n        moveHandler: e => {\n          if (e.cancelable) {\n            e.preventDefault()\n            e.stopPropagation()\n          }\n          scheduleDispatchMoveOnNextAnimationFrame(e.touches[0].clientX)\n          return false\n        },\n        upEvent: 'touchend',\n        upHandler: e => {\n          document.removeEventListener(\n            handlersAndEvents.touch.moveEvent,\n            handlersAndEvents.touch.moveHandler\n          )\n          document.removeEventListener(\n            handlersAndEvents.touch.upEvent,\n            handlersAndEvents.touch.moveHandler\n          )\n          dispatchEnd()\n        },\n      },\n    }\n\n    const events = isTouchEvent\n      ? handlersAndEvents.touch\n      : handlersAndEvents.mouse\n    const passiveIfSupported = passiveEventSupported()\n      ? { passive: false }\n      : false\n    document.addEventListener(\n      events.moveEvent,\n      events.moveHandler,\n      passiveIfSupported\n    )\n    document.addEventListener(\n      events.upEvent,\n      events.upHandler,\n      passiveIfSupported\n    )\n\n    dispatch({\n      type: actions.columnStartResizing,\n      columnId: header.id,\n      columnWidth: header.totalWidth,\n      headerIdWidths,\n      clientX,\n    })\n  }\n\n  return [\n    props,\n    {\n      onMouseDown: e => e.persist() || onResizeStart(e, header),\n      onTouchStart: e => e.persist() || onResizeStart(e, header),\n      style: {\n        cursor: 'col-resize',\n      },\n      draggable: false,\n      role: 'separator',\n    },\n  ]\n}\n\nuseResizeColumns.pluginName = 'useResizeColumns'\n\nfunction reducer(state, action) {\n  if (action.type === actions.init) {\n    return {\n      columnResizing: {\n        columnWidths: {},\n      },\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetResize) {\n    return {\n      ...state,\n      columnResizing: {\n        columnWidths: {},\n      },\n    }\n  }\n\n  if (action.type === actions.columnStartResizing) {\n    const { clientX, columnId, columnWidth, headerIdWidths } = action\n\n    return {\n      ...state,\n      columnResizing: {\n        ...state.columnResizing,\n        startX: clientX,\n        headerIdWidths,\n        columnWidth,\n        isResizingColumn: columnId,\n      },\n    }\n  }\n\n  if (action.type === actions.columnResizing) {\n    const { clientX } = action\n    const { startX, columnWidth, headerIdWidths = [] } = state.columnResizing\n\n    const deltaX = clientX - startX\n    const percentageDeltaX = deltaX / columnWidth\n\n    const newColumnWidths = {}\n\n    headerIdWidths.forEach(([headerId, headerWidth]) => {\n      newColumnWidths[headerId] = Math.max(\n        headerWidth + headerWidth * percentageDeltaX,\n        0\n      )\n    })\n\n    return {\n      ...state,\n      columnResizing: {\n        ...state.columnResizing,\n        columnWidths: {\n          ...state.columnResizing.columnWidths,\n          ...newColumnWidths,\n        },\n      },\n    }\n  }\n\n  if (action.type === actions.columnDoneResizing) {\n    return {\n      ...state,\n      columnResizing: {\n        ...state.columnResizing,\n        startX: null,\n        isResizingColumn: null,\n      },\n    }\n  }\n}\n\nconst useInstanceBeforeDimensions = instance => {\n  const {\n    flatHeaders,\n    disableResizing,\n    getHooks,\n    state: { columnResizing },\n  } = instance\n\n  const getInstance = useGetLatest(instance)\n\n  flatHeaders.forEach(header => {\n    const canResize = getFirstDefined(\n      header.disableResizing === true ? false : undefined,\n      disableResizing === true ? false : undefined,\n      true\n    )\n\n    header.canResize = canResize\n    header.width =\n      columnResizing.columnWidths[header.id] ||\n      header.originalWidth ||\n      header.width\n    header.isResizing = columnResizing.isResizingColumn === header.id\n\n    if (canResize) {\n      header.getResizerProps = makePropGetter(getHooks().getResizerProps, {\n        instance: getInstance(),\n        header,\n      })\n    }\n  })\n}\n\nfunction useInstance(instance) {\n  const { plugins, dispatch, autoResetResize = true, columns } = instance\n\n  ensurePluginOrder(plugins, ['useAbsoluteLayout'], 'useResizeColumns')\n\n  const getAutoResetResize = useGetLatest(autoResetResize)\n  useMountedLayoutEffect(() => {\n    if (getAutoResetResize()) {\n      dispatch({ type: actions.resetResize })\n    }\n  }, [columns])\n\n  const resetResizing = React.useCallback(\n    () => dispatch({ type: actions.resetResize }),\n    [dispatch]\n  )\n\n  Object.assign(instance, {\n    resetResizing,\n  })\n}\n\nfunction getLeafHeaders(header) {\n  const leafHeaders = []\n  const recurseHeader = header => {\n    if (header.columns && header.columns.length) {\n      header.columns.map(recurseHeader)\n    }\n    leafHeaders.push(header)\n  }\n  recurseHeader(header)\n  return leafHeaders\n}\n", "const cellStyles = {\n  position: 'absolute',\n  top: 0,\n}\n\nexport const useAbsoluteLayout = hooks => {\n  hooks.getTableBodyProps.push(getRowStyles)\n  hooks.getRowProps.push(getRowStyles)\n  hooks.getHeaderGroupProps.push(getRowStyles)\n  hooks.getFooterGroupProps.push(getRowStyles)\n\n  hooks.getHeaderProps.push((props, { column }) => [\n    props,\n    {\n      style: {\n        ...cellStyles,\n        left: `${column.totalLeft}px`,\n        width: `${column.totalWidth}px`,\n      },\n    },\n  ])\n\n  hooks.getCellProps.push((props, { cell }) => [\n    props,\n    {\n      style: {\n        ...cellStyles,\n        left: `${cell.column.totalLeft}px`,\n        width: `${cell.column.totalWidth}px`,\n      },\n    },\n  ])\n\n  hooks.getFooterProps.push((props, { column }) => [\n    props,\n    {\n      style: {\n        ...cellStyles,\n        left: `${column.totalLeft}px`,\n        width: `${column.totalWidth}px`,\n      },\n    },\n  ])\n}\n\nuseAbsoluteLayout.pluginName = 'useAbsoluteLayout'\n\nconst getRowStyles = (props, { instance }) => [\n  props,\n  {\n    style: {\n      position: 'relative',\n      width: `${instance.totalColumnsWidth}px`,\n    },\n  },\n]\n", "const cellStyles = {\n  display: 'inline-block',\n  boxSizing: 'border-box',\n}\n\nconst getRowStyles = (props, { instance }) => [\n  props,\n  {\n    style: {\n      display: 'flex',\n      width: `${instance.totalColumnsWidth}px`,\n    },\n  },\n]\n\nexport const useBlockLayout = hooks => {\n  hooks.getRowProps.push(getRowStyles)\n  hooks.getHeaderGroupProps.push(getRowStyles)\n  hooks.getFooterGroupProps.push(getRowStyles)\n\n  hooks.getHeaderProps.push((props, { column }) => [\n    props,\n    {\n      style: {\n        ...cellStyles,\n        width: `${column.totalWidth}px`,\n      },\n    },\n  ])\n\n  hooks.getCellProps.push((props, { cell }) => [\n    props,\n    {\n      style: {\n        ...cellStyles,\n        width: `${cell.column.totalWidth}px`,\n      },\n    },\n  ])\n\n  hooks.getFooterProps.push((props, { column }) => [\n    props,\n    {\n      style: {\n        ...cellStyles,\n        width: `${column.totalWidth}px`,\n      },\n    },\n  ])\n}\n\nuseBlockLayout.pluginName = 'useBlockLayout'\n", "export function useFlexLayout(hooks) {\n  hooks.getTableProps.push(getTableProps)\n  hooks.getRowProps.push(getRowStyles)\n  hooks.getHeaderGroupProps.push(getRowStyles)\n  hooks.getFooterGroupProps.push(getRowStyles)\n  hooks.getHeaderProps.push(getHeaderProps)\n  hooks.getCellProps.push(getCellProps)\n  hooks.getFooterProps.push(getFooterProps)\n}\n\nuseFlexLayout.pluginName = 'useFlexLayout'\n\nconst getTableProps = (props, { instance }) => [\n  props,\n  {\n    style: {\n      minWidth: `${instance.totalColumnsMinWidth}px`,\n    },\n  },\n]\n\nconst getRowStyles = (props, { instance }) => [\n  props,\n  {\n    style: {\n      display: 'flex',\n      flex: '1 0 auto',\n      minWidth: `${instance.totalColumnsMinWidth}px`,\n    },\n  },\n]\n\nconst getHeaderProps = (props, { column }) => [\n  props,\n  {\n    style: {\n      boxSizing: 'border-box',\n      flex: column.totalFlexWidth\n        ? `${column.totalFlexWidth} 0 auto`\n        : undefined,\n      minWidth: `${column.totalMinWidth}px`,\n      width: `${column.totalWidth}px`,\n    },\n  },\n]\n\nconst getCellProps = (props, { cell }) => [\n  props,\n  {\n    style: {\n      boxSizing: 'border-box',\n      flex: `${cell.column.totalFlexWidth} 0 auto`,\n      minWidth: `${cell.column.totalMinWidth}px`,\n      width: `${cell.column.totalWidth}px`,\n    },\n  },\n]\n\nconst getFooterProps = (props, { column }) => [\n  props,\n  {\n    style: {\n      boxSizing: 'border-box',\n      flex: column.totalFlexWidth\n        ? `${column.totalFlexWidth} 0 auto`\n        : undefined,\n      minWidth: `${column.totalMinWidth}px`,\n      width: `${column.totalWidth}px`,\n    },\n  },\n]\n", "import { actions } from '../publicUtils'\n\n// Actions\nactions.columnStartResizing = 'columnStartResizing'\nactions.columnResizing = 'columnResizing'\nactions.columnDoneResizing = 'columnDoneResizing'\nactions.resetResize = 'resetResize'\n\nexport function useGridLayout(hooks) {\n  hooks.stateReducers.push(reducer)\n  hooks.getTableProps.push(getTableProps)\n  hooks.getHeaderProps.push(getHeaderProps)\n  hooks.getRowProps.push(getRowProps)\n}\n\nuseGridLayout.pluginName = 'useGridLayout'\n\nconst getTableProps = (props, { instance }) => {\n  const gridTemplateColumns = instance.visibleColumns.map(column => {\n    if (instance.state.gridLayout.columnWidths[column.id])\n      return `${instance.state.gridLayout.columnWidths[column.id]}px`\n    // When resizing, lock the width of all unset columns\n    // instead of using user-provided width or defaultColumn width,\n    // which could potentially be 'auto' or 'fr' units that don't scale linearly\n    if (instance.state.columnResizing?.isResizingColumn)\n      return `${instance.state.gridLayout.startWidths[column.id]}px`\n    if (typeof column.width === 'number') return `${column.width}px`\n    return column.width\n  })\n  return [\n    props,\n    {\n      style: {\n        display: `grid`,\n        gridTemplateColumns: gridTemplateColumns.join(` `),\n      },\n    },\n  ]\n}\n\nconst getHeaderProps = (props, { column }) => [\n  props,\n  {\n    id: `header-cell-${column.id}`,\n    style: {\n      position: `sticky`, //enables a scroll wrapper to be placed around the table and have sticky headers\n      gridColumn: `span ${column.totalVisibleHeaderCount}`,\n    },\n  },\n]\n\nconst getRowProps = (props, { row }) => {\n  if (row.isExpanded) {\n    return [\n      props,\n      {\n        style: {\n          gridColumn: `1 / ${row.cells.length + 1}`,\n        },\n      },\n    ]\n  }\n  return [props, {}]\n}\n\nfunction reducer(state, action, previousState, instance) {\n  if (action.type === actions.init) {\n    return {\n      gridLayout: {\n        columnWidths: {},\n      },\n      ...state,\n    }\n  }\n\n  if (action.type === actions.resetResize) {\n    return {\n      ...state,\n      gridLayout: {\n        columnWidths: {},\n      },\n    }\n  }\n\n  if (action.type === actions.columnStartResizing) {\n    const { columnId, headerIdWidths } = action\n    const columnWidth = getElementWidth(columnId)\n\n    if (columnWidth !== undefined) {\n      const startWidths = instance.visibleColumns.reduce(\n        (acc, column) => ({\n          ...acc,\n          [column.id]: getElementWidth(column.id),\n        }),\n        {}\n      )\n      const minWidths = instance.visibleColumns.reduce(\n        (acc, column) => ({\n          ...acc,\n          [column.id]: column.minWidth,\n        }),\n        {}\n      )\n      const maxWidths = instance.visibleColumns.reduce(\n        (acc, column) => ({\n          ...acc,\n          [column.id]: column.maxWidth,\n        }),\n        {}\n      )\n\n      const headerIdGridWidths = headerIdWidths.map(([headerId]) => [\n        headerId,\n        getElementWidth(headerId),\n      ])\n\n      return {\n        ...state,\n        gridLayout: {\n          ...state.gridLayout,\n          startWidths,\n          minWidths,\n          maxWidths,\n          headerIdGridWidths,\n          columnWidth,\n        },\n      }\n    } else {\n      return state\n    }\n  }\n\n  if (action.type === actions.columnResizing) {\n    const { clientX } = action\n    const { startX } = state.columnResizing\n    const {\n      columnWidth,\n      minWidths,\n      maxWidths,\n      headerIdGridWidths = [],\n    } = state.gridLayout\n\n    const deltaX = clientX - startX\n    const percentageDeltaX = deltaX / columnWidth\n\n    const newColumnWidths = {}\n\n    headerIdGridWidths.forEach(([headerId, headerWidth]) => {\n      newColumnWidths[headerId] = Math.min(\n        Math.max(\n          minWidths[headerId],\n          headerWidth + headerWidth * percentageDeltaX\n        ),\n        maxWidths[headerId]\n      )\n    })\n\n    return {\n      ...state,\n      gridLayout: {\n        ...state.gridLayout,\n        columnWidths: {\n          ...state.gridLayout.columnWidths,\n          ...newColumnWidths,\n        },\n      },\n    }\n  }\n\n  if (action.type === actions.columnDoneResizing) {\n    return {\n      ...state,\n      gridLayout: {\n        ...state.gridLayout,\n        startWidths: {},\n        minWidths: {},\n        maxWidths: {},\n      },\n    }\n  }\n}\n\nfunction getElementWidth(columnId) {\n  const width = document.getElementById(`header-cell-${columnId}`)?.offsetWidth\n\n  if (width !== undefined) {\n    return width\n  }\n}\n"], "names": ["renderErr", "actions", "init", "defaultRenderer", "value", "emptyRenderer", "defaultColumn", "Cell", "width", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "Number", "MAX_SAFE_INTEGER", "mergeProps", "propList", "reduce", "props", "next", "style", "className", "rest", "handlePropGetter", "prevProps", "userProps", "meta", "Array", "isArray", "makePropGetter", "hooks", "prev", "reduceHooks", "initial", "allowUndefined", "nextValue", "console", "info", "Error", "loopHooks", "context", "for<PERSON>ach", "hook", "ensurePluginOrder", "plugins", "befores", "pluginName", "afters", "pluginIndex", "findIndex", "plugin", "before", "beforeIndex", "functionalUpdate", "updater", "old", "useGetLatest", "obj", "ref", "React", "useRef", "current", "useCallback", "safeUseLayoutEffect", "document", "useLayoutEffect", "useEffect", "useMountedLayoutEffect", "fn", "deps", "mountedRef", "useAsyncDebounce", "defaultFn", "defaultWait", "debounceRef", "getDefaultFn", "getDefaultWait", "args", "promise", "Promise", "resolve", "reject", "timeout", "clearTimeout", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "instance", "column", "type", "Comp", "flexRender", "isReactComponent", "component", "isClassComponent", "isExoticComponent", "proto", "Object", "getPrototypeOf", "prototype", "$$typeof", "includes", "description", "linkColumnStructure", "columns", "parent", "depth", "map", "assignColumnAccessor", "flattenColumns", "flattenBy", "id", "accessor", "Header", "accessorPath", "split", "row", "get<PERSON>y", "error", "assign", "decorateColumn", "userDefaultColumn", "Footer", "originalWidth", "makeHeaderGroups", "allColumns", "additionalHeaderProperties", "headerGroups", "scanColumns", "uid", "getUID", "headerGroup", "headers", "parentColumns", "hasParents", "some", "d", "latestParentColumn", "reverse", "newParent", "originalId", "placeholder<PERSON><PERSON>", "push", "length", "pathObjCache", "Map", "path", "def", "cache<PERSON>ey", "JSON", "stringify", "pathObj", "get", "makePathArray", "set", "val", "cursor", "pathPart", "e", "getFirstDefined", "i", "isFunction", "a", "arr", "key", "flat", "recurse", "expandRows", "rows", "manualExpandedKey", "expanded", "expandSubRows", "expandedRows", "handleRow", "addToExpandedRows", "isExpanded", "original", "canExpand", "subRows", "getFilterMethod", "filter", "userFilterTypes", "filterTypes", "text", "shouldAutoRemoveFilter", "autoRemove", "unpreparedAccessWarning", "passiveSupported", "passiveEventSupported", "supported", "options", "passive", "window", "addEventListener", "removeEventListener", "err", "reOpenBracket", "reCloseBracket", "flattenDeep", "String", "replace", "join", "newArr", "defaultGetTableProps", "role", "defaultGetTableBodyProps", "defaultGetHeaderProps", "colSpan", "totalVisibleHeaderCount", "defaultGetFooterProps", "defaultGetHeaderGroupProps", "index", "defaultGetFooterGroupProps", "defaultGetRowProps", "defaultGetCellProps", "cell", "makeDefaultPluginHooks", "useOptions", "stateReducers", "useControlledState", "columnsDeps", "allColumnsDeps", "accessValue", "materializedColumns", "materializedColumnsDeps", "useInstanceAfterData", "visibleColumns", "visibleColumnsDeps", "headerGroupsDeps", "useInstanceBeforeDimensions", "useInstance", "prepareRow", "getTableProps", "getTableBodyProps", "getHeaderGroupProps", "getFooterGroupProps", "getHeaderProps", "getFooterProps", "getRowProps", "getCellProps", "useFinalInstance", "resetHiddenColumns", "toggleHideColumn", "setHiddenColumns", "toggleHideAllColumns", "useColumnVisibility", "getToggleHiddenProps", "defaultGetToggleHiddenProps", "getToggleHideAllColumnsProps", "defaultGetToggleHideAllColumnsProps", "reducer", "state", "hiddenColumns", "onChange", "toggleHidden", "target", "checked", "isVisible", "title", "allColumnsHidden", "indeterminate", "action", "previousState", "initialState", "should", "columnId", "shouldAll", "isMountedRef", "handleColumn", "parentVisible", "subColumn", "subHeader", "flatHeaders", "dispatch", "getHooks", "autoResetHiddenColumns", "getInstance", "getAutoResetHiddenColumns", "defaultInitialState", "defaultColumnInstance", "defaultReducer", "prevState", "defaultGetSubRows", "defaultGetRowId", "defaultUseControlledState", "applyDefaults", "getSubRows", "getRowId", "stateReducer", "useTable", "instanceRef", "Boolean", "data", "userColumns", "getStateReducer", "s", "handler", "useReducer", "undefined", "reducerState", "useMemo", "flatRows", "rowsById", "allColumnsQueue", "shift", "accessRowsForColumn", "accessValueHooks", "initialRows", "find", "duplicateColumns", "all", "visibleColumnsDep", "sort", "calculateHeaderWidths", "totalColumnsMinWidth", "totalColumnsWidth", "totalColumnsMaxWidth", "render", "footerGroups", "allCells", "values", "cells", "left", "sumTotalMinWidth", "sumTotalWidth", "sumTotalMaxWidth", "sumTotalFlexWidth", "header", "subHeaders", "totalLeft", "totalMinWidth", "totalWidth", "totalMaxWidth", "totalFlexWidth", "Math", "min", "max", "canResize", "accessRow", "originalRow", "rowIndex", "parentRows", "originalSubRows", "resetExpanded", "toggleRowExpanded", "toggleAllRowsExpanded", "useExpanded", "getToggleAllRowsExpandedProps", "defaultGetToggleAllRowsExpandedProps", "getToggleRowExpandedProps", "defaultGetToggleRowExpandedProps", "onClick", "isAllRowsExpanded", "keys", "expandAll", "rowId", "setExpanded", "exists", "shouldExist", "_", "paginateExpandedRows", "autoResetExpanded", "getAutoResetExpanded", "expandedDepth", "findExpandedDepth", "preExpandedRows", "max<PERSON><PERSON><PERSON>", "splitId", "ids", "filterValue", "rowValue", "toLowerCase", "exactText", "exactTextCase", "includesAll", "every", "includesSome", "includesValue", "exact", "equals", "between", "Infinity", "temp", "resetFilters", "setFilter", "setAllFilters", "useFilters", "filters", "filterMethod", "previousfilter", "newFilter", "manualFilters", "defaultCanFilter", "disableFilters", "autoResetFilters", "columnDefaultCanFilter", "columnDisableFilters", "canFilter", "found", "filteredFlatRows", "filteredRowsById", "filterRows", "filteredRows", "filteredSoFar", "preFilteredRows", "warn", "nonFilteredColumns", "getAutoResetFilters", "preFilteredFlatRows", "preFilteredRowsById", "resetGlobalFilter", "setGlobalFilter", "useGlobalFilter", "globalFilter", "stateWithoutGlobalFilter", "manualGlobalFilter", "globalFilterValue", "autoResetGlobalFilter", "disableGlobal<PERSON><PERSON><PERSON>", "columnDisableGlobalFilter", "filterableColumns", "c", "globalFilteredRows", "globalFilteredFlatRows", "globalFilteredRowsById", "getAutoResetGlobalFilter", "preGlobalFilteredRows", "preGlobalFilteredFlatRows", "preGlobalFilteredRowsById", "sum", "aggregatedValues", "minMax", "average", "median", "mid", "floor", "nums", "b", "unique", "from", "Set", "uniqueCount", "size", "count", "emptyArray", "emptyObject", "resetGroupBy", "setGroupBy", "toggleGroupBy", "useGroupBy", "getGroupByToggleProps", "defaultGetGroupByToggleProps", "groupBy", "canGroupBy", "persist", "resolvedGroupBy", "groupByColumns", "g", "col", "nonGroupByColumns", "isGrouped", "groupedIndex", "indexOf", "defaultUserAggregations", "groupByFn", "defaultGroupByFn", "manualGroupBy", "aggregations", "userAggregations", "autoResetGroupBy", "disableGroupBy", "defaultCanGroupBy", "defaultColumnGroupBy", "defaultGroupBy", "columnDisableGroupBy", "Aggregated", "existingGroupBy", "aggregateRowsToValues", "leafRows", "groupedRows", "aggregateFn", "aggregate", "groupedValues", "leafValues", "columnValue", "aggregateValue", "aggregateValueFn", "groupedFlatRows", "groupedRowsById", "onlyGroupedFlatRows", "onlyGroupedRowsById", "nonGroupedFlatRows", "nonGroupedRowsById", "groupUpRecursively", "parentId", "rowGroupsMap", "aggregatedGroupedRows", "entries", "groupByVal", "groupByID", "subRow", "getAutoResetGroupBy", "preGroupedRows", "preGroupedFlatRow", "preGroupedRowsById", "isPlaceholder", "isAggregated", "res<PERSON>ey", "reSplitAlphaNumeric", "alphanumeric", "rowA", "rowB", "getRowValuesByColumnID", "toString", "aa", "bb", "an", "parseInt", "bn", "combo", "isNaN", "datetime", "getTime", "compareBasic", "basic", "string", "alower", "blower", "number", "replaceNonNumeric", "row1", "row2", "resetSortBy", "setSortBy", "toggleSortBy", "clearSortBy", "sortType", "sortDescFirst", "useSortBy", "getSortByToggleProps", "defaultGetSortByToggleProps", "isMultiSortEvent", "shift<PERSON>ey", "canSort", "disableMultiSort", "sortBy", "newSortBy", "desc", "multi", "disable<PERSON><PERSON><PERSON><PERSON><PERSON>", "disableMulti<PERSON><PERSON>ove", "maxMultiSortColCount", "existingSortBy", "existingIndex", "hasDescDefined", "sortAction", "splice", "orderByFn", "defaultOrderByFn", "userSortTypes", "sortTypes", "manualSortBy", "defaultCanSort", "disableSort<PERSON>y", "autoResetSortBy", "defaultColumnCanSort", "columnDisableSortBy", "columnSort", "isSorted", "sortedIndex", "isSortedDesc", "sortedFlatRows", "availableSortBy", "sortData", "sortedData", "sortMethod", "sortInverted", "sortedRows", "getAutoResetSortBy", "preSortedRows", "preSortedFlatRows", "funcs", "dirs", "sortFn", "sortInt", "resetPage", "gotoPage", "setPageSize", "usePagination", "pageSize", "pageIndex", "pageCount", "page", "newPageIndex", "canNavigate", "topRowIndex", "autoResetPage", "userPageCount", "manualPagination", "getAutoResetPage", "ceil", "pageOptions", "fill", "pageStart", "pageEnd", "slice", "canPreviousPage", "canNextPage", "previousPage", "nextPage", "resetPivot", "togglePivot", "_UNSTABLE_usePivotColumns", "getPivotToggleProps", "defaultGetPivotToggleProps", "defaultPivotColumns", "canPivot", "pivotColumns", "setPivot", "resolvedPivot", "isPivotSource", "uniqueValues", "add", "materialized", "sourceColumns", "buildPivotColumns", "pivotFilters", "pivotColumn", "sourceColumn", "isPivoted", "uniqueValue", "columnGroup", "PivotHeader", "isPivotGroup", "pivotValue", "newMaterialized", "autoResetPivot", "<PERSON>aul<PERSON><PERSON><PERSON>", "disablePivot", "defaultCanPivot", "defaultColumnPivot", "defaultPivot", "columnDisablePivot", "getAutoResetPivot", "resetSelectedRows", "toggleAllRowsSelected", "toggleRowSelected", "toggleAllPageRowsSelected", "useRowSelect", "getToggleRowSelectedProps", "defaultGetToggleRowSelectedProps", "getToggleAllRowsSelectedProps", "defaultGetToggleAllRowsSelectedProps", "getToggleAllPageRowsSelectedProps", "defaultGetToggleAllPageRowsSelectedProps", "manualRowSelectedKey", "isSelected", "isSomeSelected", "isAllRowsSelected", "selectedRowIds", "isAllPageRowsSelected", "setSelected", "selectAll", "selectSubRows", "newSelectedRowIds", "handleRowById", "autoResetSelectedRows", "selectedFlatRows", "getRowIsSelected", "getAutoResetSelectedRows", "allChildrenSelected", "someSelected", "defaultInitialRowStateAccessor", "defaultInitialCellStateAccessor", "setRowState", "setCellState", "resetRowState", "useRowState", "initialRowStateAccessor", "initialCellStateAccessor", "rowState", "oldRowState", "oldCellState", "cellState", "autoResetRowState", "getAutoResetRowState", "setState", "resetColumnOrder", "setColumnOrder", "useColumnOrder", "columnOrder", "columnOrderCopy", "columnsCopy", "columnsInOrder", "targetColumnId", "foundIndex", "columnStartResizing", "columnResizing", "columnDoneResizing", "resetResize", "useResizeColumns", "getResizerProps", "defaultGetResizerProps", "position", "onResizeStart", "isTouchEvent", "touches", "headersToResize", "getLeafHeaders", "headerIdWidths", "clientX", "round", "raf", "mostRecentClientX", "dispatchEnd", "cancelAnimationFrame", "dispatchMove", "scheduleDispatchMoveOnNextAnimationFrame", "clientXPos", "requestAnimationFrame", "handlersAndEvents", "mouse", "moveEvent", "<PERSON><PERSON><PERSON><PERSON>", "upEvent", "up<PERSON><PERSON><PERSON>", "touch", "cancelable", "preventDefault", "stopPropagation", "events", "passiveIfSupported", "columnWidth", "onMouseDown", "onTouchStart", "draggable", "columnWidths", "startX", "isResizingColumn", "deltaX", "percentageDeltaX", "newColumnWidths", "headerId", "headerWidth", "disableResizing", "isResizing", "autoResetResize", "getAutoResetResize", "resetResizing", "leafHeaders", "recurse<PERSON><PERSON><PERSON>", "cellStyles", "top", "useAbsoluteLayout", "getRowStyles", "display", "boxSizing", "useBlockLayout", "useFlexLayout", "flex", "useGridLayout", "gridTemplateColumns", "gridLayout", "startWidths", "gridColumn", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "acc", "minWidths", "maxWid<PERSON>", "headerIdGridWidths", "getElementById", "offsetWidth"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAEA,IAAIA,SAAS,GAAG,mBAAhB;MAEaC,OAAO,GAAG;EACrBC,EAAAA,IAAI,EAAE;EADe;MAIVC,eAAe,GAAG,SAAlBA,eAAkB;EAAA,wBAAGC,KAAH;EAAA,MAAGA,KAAH,2BAAW,EAAX;EAAA,SAAoBA,KAApB;EAAA;MAClBC,aAAa,GAAG,SAAhBA,aAAgB;EAAA,SAAM,iDAAN;EAAA;MAEhBC,aAAa,GAAG;EAC3BC,EAAAA,IAAI,EAAEJ,eADqB;EAE3BK,EAAAA,KAAK,EAAE,GAFoB;EAG3BC,EAAAA,QAAQ,EAAE,CAHiB;EAI3BC,EAAAA,QAAQ,EAAEC,MAAM,CAACC;EAJU;;EAO7B,SAASC,UAAT,GAAiC;EAAA,oCAAVC,QAAU;EAAVA,IAAAA,QAAU;EAAA;;EAC/B,SAAOA,QAAQ,CAACC,MAAT,CAAgB,UAACC,KAAD,EAAQC,IAAR,EAAiB;EAAA,QAC9BC,KAD8B,GACAD,IADA,CAC9BC,KAD8B;EAAA,QACvBC,SADuB,GACAF,IADA,CACvBE,SADuB;EAAA,QACTC,IADS,iCACAH,IADA;;EAGtCD,IAAAA,KAAK,gBACAA,KADA,MAEAI,IAFA,CAAL;;EAKA,QAAIF,KAAJ,EAAW;EACTF,MAAAA,KAAK,CAACE,KAAN,GAAcF,KAAK,CAACE,KAAN,gBACJF,KAAK,CAACE,KAAN,IAAe,EADX,MACoBA,KAAK,IAAI,EAD7B,IAEVA,KAFJ;EAGD;;EAED,QAAIC,SAAJ,EAAe;EACbH,MAAAA,KAAK,CAACG,SAAN,GAAkBH,KAAK,CAACG,SAAN,GACdH,KAAK,CAACG,SAAN,GAAkB,GAAlB,GAAwBA,SADV,GAEdA,SAFJ;EAGD;;EAED,QAAIH,KAAK,CAACG,SAAN,KAAoB,EAAxB,EAA4B;EAC1B,aAAOH,KAAK,CAACG,SAAb;EACD;;EAED,WAAOH,KAAP;EACD,GAzBM,EAyBJ,EAzBI,CAAP;EA0BD;;EAED,SAASK,gBAAT,CAA0BC,SAA1B,EAAqCC,SAArC,EAAgDC,IAAhD,EAAsD;EACpD;EACA,MAAI,OAAOD,SAAP,KAAqB,UAAzB,EAAqC;EACnC,WAAOF,gBAAgB,CAAC,EAAD,EAAKE,SAAS,CAACD,SAAD,EAAYE,IAAZ,CAAd,CAAvB;EACD,GAJmD;;;EAOpD,MAAIC,KAAK,CAACC,OAAN,CAAcH,SAAd,CAAJ,EAA8B;EAC5B,WAAOV,UAAU,MAAV,UAAWS,SAAX,SAAyBC,SAAzB,EAAP;EACD,GATmD;;;EAYpD,SAAOV,UAAU,CAACS,SAAD,EAAYC,SAAZ,CAAjB;EACD;;MAEYI,cAAc,GAAG,SAAjBA,cAAiB,CAACC,KAAD,EAAQJ,IAAR,EAAsB;EAAA,MAAdA,IAAc;EAAdA,IAAAA,IAAc,GAAP,EAAO;EAAA;;EAClD,SAAO,UAACD,SAAD;EAAA,QAACA,SAAD;EAACA,MAAAA,SAAD,GAAa,EAAb;EAAA;;EAAA,WACL,UAAIK,KAAJ,GAAWL,SAAX,GAAsBR,MAAtB,CACE,UAACc,IAAD,EAAOZ,IAAP;EAAA,aACEI,gBAAgB,CAACQ,IAAD,EAAOZ,IAAP,eACXO,IADW;EAEdD,QAAAA,SAAS,EAATA;EAFc,SADlB;EAAA,KADF,EAME,EANF,CADK;EAAA,GAAP;EASD;MAEYO,WAAW,GAAG,SAAdA,WAAc,CAACF,KAAD,EAAQG,OAAR,EAAiBP,IAAjB,EAA4BQ,cAA5B;EAAA,MAAiBR,IAAjB;EAAiBA,IAAAA,IAAjB,GAAwB,EAAxB;EAAA;;EAAA,SACzBI,KAAK,CAACb,MAAN,CAAa,UAACc,IAAD,EAAOZ,IAAP,EAAgB;EAC3B,QAAMgB,SAAS,GAAGhB,IAAI,CAACY,IAAD,EAAOL,IAAP,CAAtB;;EACA,IAA2C;EACzC,UAAI,CAACQ,cAAD,IAAmB,OAAOC,SAAP,KAAqB,WAA5C,EAAyD;EACvDC,QAAAA,OAAO,CAACC,IAAR,CAAalB,IAAb;EACA,cAAM,IAAImB,KAAJ,CACJ,8EADI,CAAN;EAGD;EACF;;EACD,WAAOH,SAAP;EACD,GAXD,EAWGF,OAXH,CADyB;EAAA;MAcdM,SAAS,GAAG,SAAZA,SAAY,CAACT,KAAD,EAAQU,OAAR,EAAiBd,IAAjB;EAAA,MAAiBA,IAAjB;EAAiBA,IAAAA,IAAjB,GAAwB,EAAxB;EAAA;;EAAA,SACvBI,KAAK,CAACW,OAAN,CAAc,UAAAC,IAAI,EAAI;EACpB,QAAMP,SAAS,GAAGO,IAAI,CAACF,OAAD,EAAUd,IAAV,CAAtB;;EACA,IAA2C;EACzC,UAAI,OAAOS,SAAP,KAAqB,WAAzB,EAAsC;EACpCC,QAAAA,OAAO,CAACC,IAAR,CAAaK,IAAb,EAAmBP,SAAnB;EACA,cAAM,IAAIG,KAAJ,CACJ,8EADI,CAAN;EAGD;EACF;EACF,GAVD,CADuB;EAAA;EAalB,SAASK,iBAAT,CAA2BC,OAA3B,EAAoCC,OAApC,EAA6CC,UAA7C,EAAyDC,MAAzD,EAAiE;EACtE,OAA6CA,MAA7C,EAAqD;EACnD,UAAM,IAAIT,KAAJ,wGAC+FQ,UAD/F,OAAN;EAGD;;EACD,MAAME,WAAW,GAAGJ,OAAO,CAACK,SAAR,CAClB,UAAAC,MAAM;EAAA,WAAIA,MAAM,CAACJ,UAAP,KAAsBA,UAA1B;EAAA,GADY,CAApB;;EAIA,MAAIE,WAAW,KAAK,CAAC,CAArB,EAAwB;EACtB,IAA2C;EACzC,YAAM,IAAIV,KAAJ,mBAAyBQ,UAAzB,+KAGRA,UAHQ,uBAGoBA,UAHpB,SAAN;EAKD;EACF;;EAEDD,EAAAA,OAAO,CAACJ,OAAR,CAAgB,UAAAU,MAAM,EAAI;EACxB,QAAMC,WAAW,GAAGR,OAAO,CAACK,SAAR,CAClB,UAAAC,MAAM;EAAA,aAAIA,MAAM,CAACJ,UAAP,KAAsBK,MAA1B;EAAA,KADY,CAApB;;EAGA,QAAIC,WAAW,GAAG,CAAC,CAAf,IAAoBA,WAAW,GAAGJ,WAAtC,EAAmD;EACjD,MAA2C;EACzC,cAAM,IAAIV,KAAJ,uBACgBQ,UADhB,8CACmEK,MADnE,mBAAN;EAGD;EACF;EACF,GAXD;EAYD;EAEM,SAASE,gBAAT,CAA0BC,OAA1B,EAAmCC,GAAnC,EAAwC;EAC7C,SAAO,OAAOD,OAAP,KAAmB,UAAnB,GAAgCA,OAAO,CAACC,GAAD,CAAvC,GAA+CD,OAAtD;EACD;EAEM,SAASE,YAAT,CAAsBC,GAAtB,EAA2B;EAChC,MAAMC,GAAG,GAAGC,KAAK,CAACC,MAAN,EAAZ;EACAF,EAAAA,GAAG,CAACG,OAAJ,GAAcJ,GAAd;EAEA,SAAOE,KAAK,CAACG,WAAN,CAAkB;EAAA,WAAMJ,GAAG,CAACG,OAAV;EAAA,GAAlB,EAAqC,EAArC,CAAP;EACD;;MAGYE,mBAAmB,GAC9B,OAAOC,QAAP,KAAoB,WAApB,GAAkCL,KAAK,CAACM,eAAxC,GAA0DN,KAAK,CAACO;EAE3D,SAASC,sBAAT,CAAgCC,EAAhC,EAAoCC,IAApC,EAA0C;EAC/C,MAAMC,UAAU,GAAGX,KAAK,CAACC,MAAN,CAAa,KAAb,CAAnB;EAEAG,EAAAA,mBAAmB,CAAC,YAAM;EACxB,QAAIO,UAAU,CAACT,OAAf,EAAwB;EACtBO,MAAAA,EAAE;EACH;;EACDE,IAAAA,UAAU,CAACT,OAAX,GAAqB,IAArB,CAJwB;EAMzB,GANkB,EAMhBQ,IANgB,CAAnB;EAOD;EAEM,SAASE,gBAAT,CAA0BC,SAA1B,EAAqCC,WAArC,EAAsD;EAAA,MAAjBA,WAAiB;EAAjBA,IAAAA,WAAiB,GAAH,CAAG;EAAA;;EAC3D,MAAMC,WAAW,GAAGf,KAAK,CAACC,MAAN,CAAa,EAAb,CAApB;EAEA,MAAMe,YAAY,GAAGnB,YAAY,CAACgB,SAAD,CAAjC;EACA,MAAMI,cAAc,GAAGpB,YAAY,CAACiB,WAAD,CAAnC;EAEA,SAAOd,KAAK,CAACG,WAAN;EAAA;EAAA;EAAA;EAAA;EAAA,4BACL;EAAA;EAAA;EAAA;EAAA;;EAAA;EAAA;EAAA;EAAA;EAAA,0CAAUe,IAAV;EAAUA,gBAAAA,IAAV;EAAA;;EACE,kBAAI,CAACH,WAAW,CAACb,OAAZ,CAAoBiB,OAAzB,EAAkC;EAChCJ,gBAAAA,WAAW,CAACb,OAAZ,CAAoBiB,OAApB,GAA8B,IAAIC,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;EAC7DP,kBAAAA,WAAW,CAACb,OAAZ,CAAoBmB,OAApB,GAA8BA,OAA9B;EACAN,kBAAAA,WAAW,CAACb,OAAZ,CAAoBoB,MAApB,GAA6BA,MAA7B;EACD,iBAH6B,CAA9B;EAID;;EAED,kBAAIP,WAAW,CAACb,OAAZ,CAAoBqB,OAAxB,EAAiC;EAC/BC,gBAAAA,YAAY,CAACT,WAAW,CAACb,OAAZ,CAAoBqB,OAArB,CAAZ;EACD;;EAEDR,cAAAA,WAAW,CAACb,OAAZ,CAAoBqB,OAApB,GAA8BE,UAAU;EAAA;EAAA;EAAA;EAAA,sCAAC;EAAA;EAAA;EAAA;EAAA;EACvC,+BAAOV,WAAW,CAACb,OAAZ,CAAoBqB,OAA3B;EADuC;EAAA,sCAGrCR,WAAW,CAACb,OAHyB;EAAA;EAAA,+BAGHc,YAAY,QAAZ,SAAkBE,IAAlB,CAHG;;EAAA;EAAA;;EAAA,oCAGjBG,OAHiB;;EAAA;EAAA;;EAAA;EAAA;EAAA;EAKrCN,wBAAAA,WAAW,CAACb,OAAZ,CAAoBoB,MAApB;;EALqC;EAAA;EAOrC,+BAAOP,WAAW,CAACb,OAAZ,CAAoBiB,OAA3B;EAPqC;;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA,eAAD,IASrCF,cAAc,EATuB,CAAxC;EAZF,gDAuBSF,WAAW,CAACb,OAAZ,CAAoBiB,OAvB7B;;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA,KADK;;EAAA;EAAA;EAAA;EAAA,OA0BL,CAACH,YAAD,EAAeC,cAAf,CA1BK,CAAP;EA4BD;EAEM,SAASS,YAAT,CAAsBC,QAAtB,EAAgCC,MAAhC,EAAwC7D,IAAxC,EAAmD;EAAA,MAAXA,IAAW;EAAXA,IAAAA,IAAW,GAAJ,EAAI;EAAA;;EACxD,SAAO,UAAC8D,IAAD,EAAO/D,SAAP,EAA0B;EAAA,QAAnBA,SAAmB;EAAnBA,MAAAA,SAAmB,GAAP,EAAO;EAAA;;EAC/B,QAAMgE,IAAI,GAAG,OAAOD,IAAP,KAAgB,QAAhB,GAA2BD,MAAM,CAACC,IAAD,CAAjC,GAA0CA,IAAvD;;EAEA,QAAI,OAAOC,IAAP,KAAgB,WAApB,EAAiC;EAC/BrD,MAAAA,OAAO,CAACC,IAAR,CAAakD,MAAb;EACA,YAAM,IAAIjD,KAAJ,CAAUpC,SAAV,CAAN;EACD;;EAED,WAAOwF,UAAU,CAACD,IAAD,eAAYH,QAAZ;EAAsBC,MAAAA,MAAM,EAANA;EAAtB,OAAiC7D,IAAjC,MAA0CD,SAA1C,EAAjB;EACD,GATD;EAUD;EAEM,SAASiE,UAAT,CAAoBD,IAApB,EAA0BvE,KAA1B,EAAiC;EACtC,SAAOyE,gBAAgB,CAACF,IAAD,CAAhB,GAAyB,oBAAC,IAAD,EAAUvE,KAAV,CAAzB,GAA+CuE,IAAtD;EACD;;EAED,SAASE,gBAAT,CAA0BC,SAA1B,EAAqC;EACnC,SACEC,gBAAgB,CAACD,SAAD,CAAhB,IACA,OAAOA,SAAP,KAAqB,UADrB,IAEAE,iBAAiB,CAACF,SAAD,CAHnB;EAKD;;EAED,SAASC,gBAAT,CAA0BD,SAA1B,EAAqC;EACnC,SACE,OAAOA,SAAP,KAAqB,UAArB,IACC,YAAM;EACL,QAAMG,KAAK,GAAGC,MAAM,CAACC,cAAP,CAAsBL,SAAtB,CAAd;EACA,WAAOG,KAAK,CAACG,SAAN,IAAmBH,KAAK,CAACG,SAAN,CAAgBP,gBAA1C;EACD,GAHD,EAFF;EAOD;;EAED,SAASG,iBAAT,CAA2BF,SAA3B,EAAsC;EACpC,SACE,OAAOA,SAAP,KAAqB,QAArB,IACA,OAAOA,SAAS,CAACO,QAAjB,KAA8B,QAD9B,IAEA,CAAC,YAAD,EAAe,mBAAf,EAAoCC,QAApC,CAA6CR,SAAS,CAACO,QAAV,CAAmBE,WAAhE,CAHF;EAKD;;EClOM,SAASC,mBAAT,CAA6BC,OAA7B,EAAsCC,MAAtC,EAA8CC,KAA9C,EAAyD;EAAA,MAAXA,KAAW;EAAXA,IAAAA,KAAW,GAAH,CAAG;EAAA;;EAC9D,SAAOF,OAAO,CAACG,GAAR,CAAY,UAAAnB,MAAM,EAAI;EAC3BA,IAAAA,MAAM,gBACDA,MADC;EAEJiB,MAAAA,MAAM,EAANA,MAFI;EAGJC,MAAAA,KAAK,EAALA;EAHI,MAAN;EAMAE,IAAAA,oBAAoB,CAACpB,MAAD,CAApB;;EAEA,QAAIA,MAAM,CAACgB,OAAX,EAAoB;EAClBhB,MAAAA,MAAM,CAACgB,OAAP,GAAiBD,mBAAmB,CAACf,MAAM,CAACgB,OAAR,EAAiBhB,MAAjB,EAAyBkB,KAAK,GAAG,CAAjC,CAApC;EACD;;EACD,WAAOlB,MAAP;EACD,GAbM,CAAP;EAcD;AAED,EAAO,SAASqB,cAAT,CAAwBL,OAAxB,EAAiC;EACtC,SAAOM,SAAS,CAACN,OAAD,EAAU,SAAV,CAAhB;EACD;AAED,EAAO,SAASI,oBAAT,CAA8BpB,MAA9B,EAAsC;EAC3C;EAD2C,MAErCuB,EAFqC,GAEZvB,MAFY,CAErCuB,EAFqC;EAAA,MAEjCC,QAFiC,GAEZxB,MAFY,CAEjCwB,QAFiC;EAAA,MAEvBC,MAFuB,GAEZzB,MAFY,CAEvByB,MAFuB;;EAI3C,MAAI,OAAOD,QAAP,KAAoB,QAAxB,EAAkC;EAChCD,IAAAA,EAAE,GAAGA,EAAE,IAAIC,QAAX;EACA,QAAME,YAAY,GAAGF,QAAQ,CAACG,KAAT,CAAe,GAAf,CAArB;;EACAH,IAAAA,QAAQ,GAAG,kBAAAI,GAAG;EAAA,aAAIC,KAAK,CAACD,GAAD,EAAMF,YAAN,CAAT;EAAA,KAAd;EACD;;EAED,MAAI,CAACH,EAAD,IAAO,OAAOE,MAAP,KAAkB,QAAzB,IAAqCA,MAAzC,EAAiD;EAC/CF,IAAAA,EAAE,GAAGE,MAAL;EACD;;EAED,MAAI,CAACF,EAAD,IAAOvB,MAAM,CAACgB,OAAlB,EAA2B;EACzBnE,IAAAA,OAAO,CAACiF,KAAR,CAAc9B,MAAd;EACA,UAAM,IAAIjD,KAAJ,CAAU,qDAAV,CAAN;EACD;;EAED,MAAI,CAACwE,EAAL,EAAS;EACP1E,IAAAA,OAAO,CAACiF,KAAR,CAAc9B,MAAd;EACA,UAAM,IAAIjD,KAAJ,CAAU,+CAAV,CAAN;EACD;;EAED0D,EAAAA,MAAM,CAACsB,MAAP,CAAc/B,MAAd,EAAsB;EACpBuB,IAAAA,EAAE,EAAFA,EADoB;EAEpBC,IAAAA,QAAQ,EAARA;EAFoB,GAAtB;EAKA,SAAOxB,MAAP;EACD;AAED,EAAO,SAASgC,cAAT,CAAwBhC,MAAxB,EAAgCiC,iBAAhC,EAAmD;EACxD,MAAI,CAACA,iBAAL,EAAwB;EACtB,UAAM,IAAIlF,KAAJ,EAAN;EACD;;EACD0D,EAAAA,MAAM,CAACsB,MAAP,CAAc/B,MAAd;EACE;EACAyB,IAAAA,MAAM,EAAEzG,aAFV;EAGEkH,IAAAA,MAAM,EAAElH;EAHV,KAIKC,aAJL,MAKKgH,iBALL,MAMKjC,MANL;EASAS,EAAAA,MAAM,CAACsB,MAAP,CAAc/B,MAAd,EAAsB;EACpBmC,IAAAA,aAAa,EAAEnC,MAAM,CAAC7E;EADF,GAAtB;EAIA,SAAO6E,MAAP;EACD;;AAGD,EAAO,SAASoC,gBAAT,CACLC,UADK,EAELpH,aAFK,EAGLqH,0BAHK,EAIL;EAAA,MADAA,0BACA;EADAA,IAAAA,0BACA,GAD6B;EAAA,aAAO,EAAP;EAAA,KAC7B;EAAA;;EACA,MAAMC,YAAY,GAAG,EAArB;EAEA,MAAIC,WAAW,GAAGH,UAAlB;EAEA,MAAII,GAAG,GAAG,CAAV;;EACA,MAAMC,MAAM,GAAG,SAATA,MAAS;EAAA,WAAMD,GAAG,EAAT;EAAA,GAAf;;EANA;EASE;EACA,QAAME,WAAW,GAAG;EAClBC,MAAAA,OAAO,EAAE;EADS,KAApB,CAVF;;EAeE,QAAMC,aAAa,GAAG,EAAtB;EAEA,QAAMC,UAAU,GAAGN,WAAW,CAACO,IAAZ,CAAiB,UAAAC,CAAC;EAAA,aAAIA,CAAC,CAAC/B,MAAN;EAAA,KAAlB,CAAnB,CAjBF;;EAoBEuB,IAAAA,WAAW,CAACtF,OAAZ,CAAoB,UAAA8C,MAAM,EAAI;EAC5B;EACA,UAAIiD,kBAAkB,GAAG,UAAIJ,aAAJ,EAAmBK,OAAnB,GAA6B,CAA7B,CAAzB;EAEA,UAAIC,SAAJ;;EAEA,UAAIL,UAAJ,EAAgB;EACd;EACA,YAAI9C,MAAM,CAACiB,MAAX,EAAmB;EACjBkC,UAAAA,SAAS,gBACJnD,MAAM,CAACiB,MADH;EAEPmC,YAAAA,UAAU,EAAEpD,MAAM,CAACiB,MAAP,CAAcM,EAFnB;EAGPA,YAAAA,EAAE,EAAKvB,MAAM,CAACiB,MAAP,CAAcM,EAAnB,SAAyBmB,MAAM,EAH1B;EAIPE,YAAAA,OAAO,EAAE,CAAC5C,MAAD;EAJF,aAKJsC,0BAA0B,CAACtC,MAAD,CALtB,CAAT;EAOD,SARD,MAQO;EACL;EACA,cAAMoD,UAAU,GAAMpD,MAAM,CAACuB,EAAb,iBAAhB;EACA4B,UAAAA,SAAS,GAAGnB,cAAc;EAEtBoB,YAAAA,UAAU,EAAVA,UAFsB;EAGtB7B,YAAAA,EAAE,EAAKvB,MAAM,CAACuB,EAAZ,qBAA8BmB,MAAM,EAHhB;EAItBW,YAAAA,aAAa,EAAErD,MAJO;EAKtB4C,YAAAA,OAAO,EAAE,CAAC5C,MAAD;EALa,aAMnBsC,0BAA0B,CAACtC,MAAD,CANP,GAQxB/E,aARwB,CAA1B;EAUD,SAvBa;EA0Bd;;;EACA,YACEgI,kBAAkB,IAClBA,kBAAkB,CAACG,UAAnB,KAAkCD,SAAS,CAACC,UAF9C,EAGE;EACAH,UAAAA,kBAAkB,CAACL,OAAnB,CAA2BU,IAA3B,CAAgCtD,MAAhC;EACD,SALD,MAKO;EACL6C,UAAAA,aAAa,CAACS,IAAd,CAAmBH,SAAnB;EACD;EACF;;EAEDR,MAAAA,WAAW,CAACC,OAAZ,CAAoBU,IAApB,CAAyBtD,MAAzB;EACD,KA5CD;EA8CAuC,IAAAA,YAAY,CAACe,IAAb,CAAkBX,WAAlB,EAlEF;;EAqEEH,IAAAA,WAAW,GAAGK,aAAd;EArEF;;EAQA,SAAOL,WAAW,CAACe,MAAnB,EAA2B;EAAA;EA8D1B;;EAED,SAAOhB,YAAY,CAACW,OAAb,EAAP;EACD;EAED,IAAMM,YAAY,GAAG,IAAIC,GAAJ,EAArB;AAEA,EAAO,SAAS5B,KAAT,CAAe3D,GAAf,EAAoBwF,IAApB,EAA0BC,GAA1B,EAA+B;EACpC,MAAI,CAACD,IAAL,EAAW;EACT,WAAOxF,GAAP;EACD;;EACD,MAAM0F,QAAQ,GAAG,OAAOF,IAAP,KAAgB,UAAhB,GAA6BA,IAA7B,GAAoCG,IAAI,CAACC,SAAL,CAAeJ,IAAf,CAArD;;EAEA,MAAMK,OAAO,GACXP,YAAY,CAACQ,GAAb,CAAiBJ,QAAjB,KACC,YAAM;EACL,QAAMG,OAAO,GAAGE,aAAa,CAACP,IAAD,CAA7B;EACAF,IAAAA,YAAY,CAACU,GAAb,CAAiBN,QAAjB,EAA2BG,OAA3B;EACA,WAAOA,OAAP;EACD,GAJD,EAFF;;EAQA,MAAII,GAAJ;;EAEA,MAAI;EACFA,IAAAA,GAAG,GAAGJ,OAAO,CAACrI,MAAR,CAAe,UAAC0I,MAAD,EAASC,QAAT;EAAA,aAAsBD,MAAM,CAACC,QAAD,CAA5B;EAAA,KAAf,EAAuDnG,GAAvD,CAAN;EACD,GAFD,CAEE,OAAOoG,CAAP,EAAU;EAEX;;EACD,SAAO,OAAOH,GAAP,KAAe,WAAf,GAA6BA,GAA7B,GAAmCR,GAA1C;EACD;AAED,EAAO,SAASY,eAAT,GAAkC;EAAA,oCAANjF,IAAM;EAANA,IAAAA,IAAM;EAAA;;EACvC,OAAK,IAAIkF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlF,IAAI,CAACiE,MAAzB,EAAiCiB,CAAC,IAAI,CAAtC,EAAyC;EACvC,QAAI,OAAOlF,IAAI,CAACkF,CAAD,CAAX,KAAmB,WAAvB,EAAoC;EAClC,aAAOlF,IAAI,CAACkF,CAAD,CAAX;EACD;EACF;EACF;AAED,EAyBO,SAASC,UAAT,CAAoBC,CAApB,EAAuB;EAC5B,MAAI,OAAOA,CAAP,KAAa,UAAjB,EAA6B;EAC3B,WAAOA,CAAP;EACD;EACF;AAED,EAAO,SAASpD,SAAT,CAAmBqD,GAAnB,EAAwBC,GAAxB,EAA6B;EAClC,MAAMC,IAAI,GAAG,EAAb;;EAEA,MAAMC,OAAO,GAAG,SAAVA,OAAU,CAAAH,GAAG,EAAI;EACrBA,IAAAA,GAAG,CAACzH,OAAJ,CAAY,UAAA8F,CAAC,EAAI;EACf,UAAI,CAACA,CAAC,CAAC4B,GAAD,CAAN,EAAa;EACXC,QAAAA,IAAI,CAACvB,IAAL,CAAUN,CAAV;EACD,OAFD,MAEO;EACL8B,QAAAA,OAAO,CAAC9B,CAAC,CAAC4B,GAAD,CAAF,CAAP;EACD;EACF,KAND;EAOD,GARD;;EAUAE,EAAAA,OAAO,CAACH,GAAD,CAAP;EAEA,SAAOE,IAAP;EACD;AAED,EAAO,SAASE,UAAT,CACLC,IADK,QAGL;EAAA,MADEC,iBACF,QADEA,iBACF;EAAA,MADqBC,QACrB,QADqBA,QACrB;EAAA,gCAD+BC,aAC/B;EAAA,MAD+BA,aAC/B,mCAD+C,IAC/C;EACA,MAAMC,YAAY,GAAG,EAArB;;EAEA,MAAMC,SAAS,GAAG,SAAZA,SAAY,CAACzD,GAAD,EAAM0D,iBAAN,EAAmC;EAAA,QAA7BA,iBAA6B;EAA7BA,MAAAA,iBAA6B,GAAT,IAAS;EAAA;;EACnD1D,IAAAA,GAAG,CAAC2D,UAAJ,GACG3D,GAAG,CAAC4D,QAAJ,IAAgB5D,GAAG,CAAC4D,QAAJ,CAAaP,iBAAb,CAAjB,IAAqDC,QAAQ,CAACtD,GAAG,CAACL,EAAL,CAD/D;EAGAK,IAAAA,GAAG,CAAC6D,SAAJ,GAAgB7D,GAAG,CAAC8D,OAAJ,IAAe,CAAC,CAAC9D,GAAG,CAAC8D,OAAJ,CAAYnC,MAA7C;;EAEA,QAAI+B,iBAAJ,EAAuB;EACrBF,MAAAA,YAAY,CAAC9B,IAAb,CAAkB1B,GAAlB;EACD;;EAED,QAAIA,GAAG,CAAC8D,OAAJ,IAAe9D,GAAG,CAAC8D,OAAJ,CAAYnC,MAA3B,IAAqC3B,GAAG,CAAC2D,UAA7C,EAAyD;EACvD3D,MAAAA,GAAG,CAAC8D,OAAJ,CAAYxI,OAAZ,CAAoB,UAAA0E,GAAG;EAAA,eAAIyD,SAAS,CAACzD,GAAD,EAAMuD,aAAN,CAAb;EAAA,OAAvB;EACD;EACF,GAbD;;EAeAH,EAAAA,IAAI,CAAC9H,OAAL,CAAa,UAAA0E,GAAG;EAAA,WAAIyD,SAAS,CAACzD,GAAD,CAAb;EAAA,GAAhB;EAEA,SAAOwD,YAAP;EACD;AAED,EAAO,SAASO,eAAT,CAAyBC,MAAzB,EAAiCC,eAAjC,EAAkDC,WAAlD,EAA+D;EACpE,SACErB,UAAU,CAACmB,MAAD,CAAV,IACAC,eAAe,CAACD,MAAD,CADf,IAEAE,WAAW,CAACF,MAAD,CAFX,IAGAE,WAAW,CAACC,IAJd;EAMD;AAED,EAAO,SAASC,sBAAT,CAAgCC,UAAhC,EAA4ClL,KAA5C,EAAmDiF,MAAnD,EAA2D;EAChE,SAAOiG,UAAU,GAAGA,UAAU,CAAClL,KAAD,EAAQiF,MAAR,CAAb,GAA+B,OAAOjF,KAAP,KAAiB,WAAjE;EACD;AAED,EAAO,SAASmL,uBAAT,GAAmC;EACxC,QAAM,IAAInJ,KAAJ,CACJ,iGADI,CAAN;EAGD;EAED,IAAIoJ,gBAAgB,GAAG,IAAvB;AACA,EAAO,SAASC,qBAAT,GAAiC;EACtC;EACA,MAAI,OAAOD,gBAAP,KAA4B,SAAhC,EAA2C,OAAOA,gBAAP;EAE3C,MAAIE,SAAS,GAAG,KAAhB;;EACA,MAAI;EACF,QAAMC,OAAO,GAAG;EACd,UAAIC,OAAJ,GAAc;EACZF,QAAAA,SAAS,GAAG,IAAZ;EACA,eAAO,KAAP;EACD;;EAJa,KAAhB;EAOAG,IAAAA,MAAM,CAACC,gBAAP,CAAwB,MAAxB,EAAgC,IAAhC,EAAsCH,OAAtC;EACAE,IAAAA,MAAM,CAACE,mBAAP,CAA2B,MAA3B,EAAmC,IAAnC,EAAyCJ,OAAzC;EACD,GAVD,CAUE,OAAOK,GAAP,EAAY;EACZN,IAAAA,SAAS,GAAG,KAAZ;EACD;;EACDF,EAAAA,gBAAgB,GAAGE,SAAnB;EACA,SAAOF,gBAAP;EACD;;EAID,IAAMS,aAAa,GAAG,KAAtB;EACA,IAAMC,cAAc,GAAG,KAAvB;;EAEA,SAAS5C,aAAT,CAAuB/F,GAAvB,EAA4B;EAC1B,SACE4I,WAAW,CAAC5I,GAAD,CAAX;EAAA,GAEGiD,GAFH,CAEO,UAAA6B,CAAC;EAAA,WAAI+D,MAAM,CAAC/D,CAAD,CAAN,CAAUgE,OAAV,CAAkB,GAAlB,EAAuB,GAAvB,CAAJ;EAAA,GAFR;EAAA,GAIGC,IAJH,CAIQ,GAJR;EAAA,GAMGD,OANH,CAMWJ,aANX,EAM0B,GAN1B,EAOGI,OAPH,CAOWH,cAPX,EAO2B,EAP3B;EAAA,GASGlF,KATH,CASS,GATT,CADF;EAYD;;EAED,SAASmF,WAAT,CAAqBnC,GAArB,EAA0BuC,MAA1B,EAAuC;EAAA,MAAbA,MAAa;EAAbA,IAAAA,MAAa,GAAJ,EAAI;EAAA;;EACrC,MAAI,CAAC9K,KAAK,CAACC,OAAN,CAAcsI,GAAd,CAAL,EAAyB;EACvBuC,IAAAA,MAAM,CAAC5D,IAAP,CAAYqB,GAAZ;EACD,GAFD,MAEO;EACL,SAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGG,GAAG,CAACpB,MAAxB,EAAgCiB,CAAC,IAAI,CAArC,EAAwC;EACtCsC,MAAAA,WAAW,CAACnC,GAAG,CAACH,CAAD,CAAJ,EAAS0C,MAAT,CAAX;EACD;EACF;;EACD,SAAOA,MAAP;EACD;;EC1VD,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAAxL,KAAK;EAAA;EAChCyL,IAAAA,IAAI,EAAE;EAD0B,KAE7BzL,KAF6B;EAAA,CAAlC;;EAKA,IAAM0L,wBAAwB,GAAG,SAA3BA,wBAA2B,CAAA1L,KAAK;EAAA;EACpCyL,IAAAA,IAAI,EAAE;EAD8B,KAEjCzL,KAFiC;EAAA,CAAtC;;EAKA,IAAM2L,qBAAqB,GAAG,SAAxBA,qBAAwB,CAAC3L,KAAD;EAAA,MAAUqE,MAAV,QAAUA,MAAV;EAAA;EAC5B4E,IAAAA,GAAG,cAAY5E,MAAM,CAACuB,EADM;EAE5BgG,IAAAA,OAAO,EAAEvH,MAAM,CAACwH,uBAFY;EAG5BJ,IAAAA,IAAI,EAAE;EAHsB,KAIzBzL,KAJyB;EAAA,CAA9B;;EAOA,IAAM8L,qBAAqB,GAAG,SAAxBA,qBAAwB,CAAC9L,KAAD;EAAA,MAAUqE,MAAV,SAAUA,MAAV;EAAA;EAC5B4E,IAAAA,GAAG,cAAY5E,MAAM,CAACuB,EADM;EAE5BgG,IAAAA,OAAO,EAAEvH,MAAM,CAACwH;EAFY,KAGzB7L,KAHyB;EAAA,CAA9B;;EAMA,IAAM+L,0BAA0B,GAAG,SAA7BA,0BAA6B,CAAC/L,KAAD;EAAA,MAAUgM,KAAV,SAAUA,KAAV;EAAA;EACjC/C,IAAAA,GAAG,mBAAiB+C,KADa;EAEjCP,IAAAA,IAAI,EAAE;EAF2B,KAG9BzL,KAH8B;EAAA,CAAnC;;EAMA,IAAMiM,0BAA0B,GAAG,SAA7BA,0BAA6B,CAACjM,KAAD;EAAA,MAAUgM,KAAV,SAAUA,KAAV;EAAA;EACjC/C,IAAAA,GAAG,mBAAiB+C;EADa,KAE9BhM,KAF8B;EAAA,CAAnC;;EAKA,IAAMkM,kBAAkB,GAAG,SAArBA,kBAAqB,CAAClM,KAAD;EAAA,MAAUiG,GAAV,SAAUA,GAAV;EAAA;EACzBgD,IAAAA,GAAG,WAAShD,GAAG,CAACL,EADS;EAEzB6F,IAAAA,IAAI,EAAE;EAFmB,KAGtBzL,KAHsB;EAAA,CAA3B;;EAMA,IAAMmM,mBAAmB,GAAG,SAAtBA,mBAAsB,CAACnM,KAAD;EAAA,MAAUoM,IAAV,SAAUA,IAAV;EAAA;EAC1BnD,IAAAA,GAAG,YAAUmD,IAAI,CAACnG,GAAL,CAASL,EAAnB,SAAyBwG,IAAI,CAAC/H,MAAL,CAAYuB,EADd;EAE1B6F,IAAAA,IAAI,EAAE;EAFoB,KAGvBzL,KAHuB;EAAA,CAA5B;;AAMA,EAAe,SAASqM,sBAAT,GAAkC;EAC/C,SAAO;EACLC,IAAAA,UAAU,EAAE,EADP;EAELC,IAAAA,aAAa,EAAE,EAFV;EAGLC,IAAAA,kBAAkB,EAAE,EAHf;EAILnH,IAAAA,OAAO,EAAE,EAJJ;EAKLoH,IAAAA,WAAW,EAAE,EALR;EAML/F,IAAAA,UAAU,EAAE,EANP;EAOLgG,IAAAA,cAAc,EAAE,EAPX;EAQLC,IAAAA,WAAW,EAAE,EARR;EASLC,IAAAA,mBAAmB,EAAE,EAThB;EAULC,IAAAA,uBAAuB,EAAE,EAVpB;EAWLC,IAAAA,oBAAoB,EAAE,EAXjB;EAYLC,IAAAA,cAAc,EAAE,EAZX;EAaLC,IAAAA,kBAAkB,EAAE,EAbf;EAcLpG,IAAAA,YAAY,EAAE,EAdT;EAeLqG,IAAAA,gBAAgB,EAAE,EAfb;EAgBLC,IAAAA,2BAA2B,EAAE,EAhBxB;EAiBLC,IAAAA,WAAW,EAAE,EAjBR;EAkBLC,IAAAA,UAAU,EAAE,EAlBP;EAmBLC,IAAAA,aAAa,EAAE,CAAC7B,oBAAD,CAnBV;EAoBL8B,IAAAA,iBAAiB,EAAE,CAAC5B,wBAAD,CApBd;EAqBL6B,IAAAA,mBAAmB,EAAE,CAACxB,0BAAD,CArBhB;EAsBLyB,IAAAA,mBAAmB,EAAE,CAACvB,0BAAD,CAtBhB;EAuBLwB,IAAAA,cAAc,EAAE,CAAC9B,qBAAD,CAvBX;EAwBL+B,IAAAA,cAAc,EAAE,CAAC5B,qBAAD,CAxBX;EAyBL6B,IAAAA,WAAW,EAAE,CAACzB,kBAAD,CAzBR;EA0BL0B,IAAAA,YAAY,EAAE,CAACzB,mBAAD,CA1BT;EA2BL0B,IAAAA,gBAAgB,EAAE;EA3Bb,GAAP;EA6BD;;EClED5O,OAAO,CAAC6O,kBAAR,GAA6B,oBAA7B;EACA7O,OAAO,CAAC8O,gBAAR,GAA2B,kBAA3B;EACA9O,OAAO,CAAC+O,gBAAR,GAA2B,kBAA3B;EACA/O,OAAO,CAACgP,oBAAR,GAA+B,sBAA/B;AAEA,EAAO,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAsB,CAAAtN,KAAK,EAAI;EAC1CA,EAAAA,KAAK,CAACuN,oBAAN,GAA6B,CAACC,2BAAD,CAA7B;EACAxN,EAAAA,KAAK,CAACyN,4BAAN,GAAqC,CAACC,mCAAD,CAArC;EAEA1N,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,OAAzB;EACA3N,EAAAA,KAAK,CAACsM,2BAAN,CAAkCvF,IAAlC,CAAuCuF,2BAAvC;EACAtM,EAAAA,KAAK,CAACqM,gBAAN,CAAuBtF,IAAvB,CAA4B,UAACxE,IAAD;EAAA,QAASiB,QAAT,QAASA,QAAT;EAAA,qBACvBjB,IADuB,GAE1BiB,QAAQ,CAACoK,KAAT,CAAeC,aAFW;EAAA,GAA5B;EAIA7N,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,WAAvB;EACD,CAXM;EAaPe,mBAAmB,CAACtM,UAApB,GAAiC,qBAAjC;;EAEA,IAAMwM,2BAA2B,GAAG,SAA9BA,2BAA8B,CAACpO,KAAD;EAAA,MAAUqE,MAAV,SAAUA,MAAV;EAAA,SAAuB,CACzDrE,KADyD,EAEzD;EACE0O,IAAAA,QAAQ,EAAE,kBAAA/F,CAAC,EAAI;EACbtE,MAAAA,MAAM,CAACsK,YAAP,CAAoB,CAAChG,CAAC,CAACiG,MAAF,CAASC,OAA9B;EACD,KAHH;EAIE3O,IAAAA,KAAK,EAAE;EACLuI,MAAAA,MAAM,EAAE;EADH,KAJT;EAOEoG,IAAAA,OAAO,EAAExK,MAAM,CAACyK,SAPlB;EAQEC,IAAAA,KAAK,EAAE;EART,GAFyD,CAAvB;EAAA,CAApC;;EAcA,IAAMT,mCAAmC,GAAG,SAAtCA,mCAAsC,CAACtO,KAAD;EAAA,MAAUoE,QAAV,SAAUA,QAAV;EAAA,SAAyB,CACnEpE,KADmE,EAEnE;EACE0O,IAAAA,QAAQ,EAAE,kBAAA/F,CAAC,EAAI;EACbvE,MAAAA,QAAQ,CAAC6J,oBAAT,CAA8B,CAACtF,CAAC,CAACiG,MAAF,CAASC,OAAxC;EACD,KAHH;EAIE3O,IAAAA,KAAK,EAAE;EACLuI,MAAAA,MAAM,EAAE;EADH,KAJT;EAOEoG,IAAAA,OAAO,EAAE,CAACzK,QAAQ,CAAC4K,gBAAV,IAA8B,CAAC5K,QAAQ,CAACoK,KAAT,CAAeC,aAAf,CAA6B7G,MAPvE;EAQEmH,IAAAA,KAAK,EAAE,2BART;EASEE,IAAAA,aAAa,EACX,CAAC7K,QAAQ,CAAC4K,gBAAV,IAA8B5K,QAAQ,CAACoK,KAAT,CAAeC,aAAf,CAA6B7G;EAV/D,GAFmE,CAAzB;EAAA,CAA5C;;EAgBA,SAAS2G,OAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EACvD,MAAI8K,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACEuP,MAAAA,aAAa,EAAE;EADjB,OAEKD,KAFL;EAID;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC6O,kBAA5B,EAAgD;EAC9C,wBACKU,KADL;EAEEC,MAAAA,aAAa,EAAErK,QAAQ,CAACgL,YAAT,CAAsBX,aAAtB,IAAuC;EAFxD;EAID;;EAED,MAAIS,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC8O,gBAA5B,EAA8C;EAC5C,QAAMsB,MAAM,GACV,OAAOH,MAAM,CAAC9P,KAAd,KAAwB,WAAxB,GACI8P,MAAM,CAAC9P,KADX,GAEI,CAACoP,KAAK,CAACC,aAAN,CAAoBvJ,QAApB,CAA6BgK,MAAM,CAACI,QAApC,CAHP;EAKA,QAAMb,aAAa,GAAGY,MAAM,aACpBb,KAAK,CAACC,aADc,GACCS,MAAM,CAACI,QADR,KAExBd,KAAK,CAACC,aAAN,CAAoBxE,MAApB,CAA2B,UAAA5C,CAAC;EAAA,aAAIA,CAAC,KAAK6H,MAAM,CAACI,QAAjB;EAAA,KAA5B,CAFJ;EAIA,wBACKd,KADL;EAEEC,MAAAA,aAAa,EAAbA;EAFF;EAID;;EAED,MAAIS,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC+O,gBAA5B,EAA8C;EAC5C,wBACKQ,KADL;EAEEC,MAAAA,aAAa,EAAEtM,gBAAgB,CAAC+M,MAAM,CAAC9P,KAAR,EAAeoP,KAAK,CAACC,aAArB;EAFjC;EAID;;EAED,MAAIS,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACgP,oBAA5B,EAAkD;EAChD,QAAMsB,SAAS,GACb,OAAOL,MAAM,CAAC9P,KAAd,KAAwB,WAAxB,GACI8P,MAAM,CAAC9P,KADX,GAEI,CAACoP,KAAK,CAACC,aAAN,CAAoB7G,MAH3B;EAKA,wBACK4G,KADL;EAEEC,MAAAA,aAAa,EAAEc,SAAS,GAAGnL,QAAQ,CAACsC,UAAT,CAAoBlB,GAApB,CAAwB,UAAA6B,CAAC;EAAA,eAAIA,CAAC,CAACzB,EAAN;EAAA,OAAzB,CAAH,GAAwC;EAFlE;EAID;EACF;;EAED,SAASsH,2BAAT,CAAqC9I,QAArC,EAA+C;EAAA,MAE3C6C,OAF2C,GAIzC7C,QAJyC,CAE3C6C,OAF2C;EAAA,MAGlCwH,aAHkC,GAIzCrK,QAJyC,CAG3CoK,KAH2C,CAGlCC,aAHkC;EAM7C,MAAMe,YAAY,GAAG/M,KAAK,CAACC,MAAN,CAAa,KAAb,CAArB;;EAEA,MAAI,CAAC8M,YAAY,CAAC7M,OAAlB,EAA2B;;EAG3B,MAAM8M,YAAY,GAAG,SAAfA,YAAe,CAACpL,MAAD,EAASqL,aAAT,EAA2B;EAC9CrL,IAAAA,MAAM,CAACyK,SAAP,GAAmBY,aAAa,IAAI,CAACjB,aAAa,CAACvJ,QAAd,CAAuBb,MAAM,CAACuB,EAA9B,CAArC;EAEA,QAAIiG,uBAAuB,GAAG,CAA9B;;EAEA,QAAIxH,MAAM,CAAC4C,OAAP,IAAkB5C,MAAM,CAAC4C,OAAP,CAAeW,MAArC,EAA6C;EAC3CvD,MAAAA,MAAM,CAAC4C,OAAP,CAAe1F,OAAf,CACE,UAAAoO,SAAS;EAAA,eACN9D,uBAAuB,IAAI4D,YAAY,CAACE,SAAD,EAAYtL,MAAM,CAACyK,SAAnB,CADjC;EAAA,OADX;EAID,KALD,MAKO;EACLjD,MAAAA,uBAAuB,GAAGxH,MAAM,CAACyK,SAAP,GAAmB,CAAnB,GAAuB,CAAjD;EACD;;EAEDzK,IAAAA,MAAM,CAACwH,uBAAP,GAAiCA,uBAAjC;EAEA,WAAOA,uBAAP;EACD,GAjBD;;EAmBA,MAAIA,uBAAuB,GAAG,CAA9B;EAEA5E,EAAAA,OAAO,CAAC1F,OAAR,CACE,UAAAqO,SAAS;EAAA,WAAK/D,uBAAuB,IAAI4D,YAAY,CAACG,SAAD,EAAY,IAAZ,CAA5C;EAAA,GADX;EAGD;;EAED,SAASzC,WAAT,CAAqB/I,QAArB,EAA+B;EAAA,MAE3BiB,OAF2B,GASzBjB,QATyB,CAE3BiB,OAF2B;EAAA,MAG3BwK,WAH2B,GASzBzL,QATyB,CAG3ByL,WAH2B;EAAA,MAI3BC,QAJ2B,GASzB1L,QATyB,CAI3B0L,QAJ2B;EAAA,MAK3BpJ,UAL2B,GASzBtC,QATyB,CAK3BsC,UAL2B;EAAA,MAM3BqJ,QAN2B,GASzB3L,QATyB,CAM3B2L,QAN2B;EAAA,MAOlBtB,aAPkB,GASzBrK,QATyB,CAO3BoK,KAP2B,CAOlBC,aAPkB;EAAA,8BASzBrK,QATyB,CAQ3B4L,sBAR2B;EAAA,MAQ3BA,sBAR2B,sCAQF,IARE;EAW7B,MAAMC,WAAW,GAAG3N,YAAY,CAAC8B,QAAD,CAAhC;EAEA,MAAM4K,gBAAgB,GAAGtI,UAAU,CAACkB,MAAX,KAAsB6G,aAAa,CAAC7G,MAA7D;EAEA,MAAMmG,gBAAgB,GAAGtL,KAAK,CAACG,WAAN,CACvB,UAAC0M,QAAD,EAAWlQ,KAAX;EAAA,WACE0Q,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAAC8O,gBAAhB;EAAkCuB,MAAAA,QAAQ,EAARA,QAAlC;EAA4ClQ,MAAAA,KAAK,EAALA;EAA5C,KAAD,CADV;EAAA,GADuB,EAGvB,CAAC0Q,QAAD,CAHuB,CAAzB;EAMA,MAAM9B,gBAAgB,GAAGvL,KAAK,CAACG,WAAN,CACvB,UAAAxD,KAAK;EAAA,WAAI0Q,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAAC+O,gBAAhB;EAAkC5O,MAAAA,KAAK,EAALA;EAAlC,KAAD,CAAZ;EAAA,GADkB,EAEvB,CAAC0Q,QAAD,CAFuB,CAAzB;EAKA,MAAM7B,oBAAoB,GAAGxL,KAAK,CAACG,WAAN,CAC3B,UAAAxD,KAAK;EAAA,WAAI0Q,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACgP,oBAAhB;EAAsC7O,MAAAA,KAAK,EAALA;EAAtC,KAAD,CAAZ;EAAA,GADsB,EAE3B,CAAC0Q,QAAD,CAF2B,CAA7B;EAKA,MAAMzB,4BAA4B,GAAG1N,cAAc,CACjDoP,QAAQ,GAAG1B,4BADsC,EAEjD;EAAEjK,IAAAA,QAAQ,EAAE6L,WAAW;EAAvB,GAFiD,CAAnD;EAKAJ,EAAAA,WAAW,CAACtO,OAAZ,CAAoB,UAAA8C,MAAM,EAAI;EAC5BA,IAAAA,MAAM,CAACsK,YAAP,GAAsB,UAAAvP,KAAK,EAAI;EAC7B0Q,MAAAA,QAAQ,CAAC;EACPxL,QAAAA,IAAI,EAAErF,OAAO,CAAC8O,gBADP;EAEPuB,QAAAA,QAAQ,EAAEjL,MAAM,CAACuB,EAFV;EAGPxG,QAAAA,KAAK,EAALA;EAHO,OAAD,CAAR;EAKD,KAND;;EAQAiF,IAAAA,MAAM,CAAC8J,oBAAP,GAA8BxN,cAAc,CAC1CoP,QAAQ,GAAG5B,oBAD+B,EAE1C;EACE/J,MAAAA,QAAQ,EAAE6L,WAAW,EADvB;EAEE5L,MAAAA,MAAM,EAANA;EAFF,KAF0C,CAA5C;EAOD,GAhBD;EAkBA,MAAM6L,yBAAyB,GAAG5N,YAAY,CAAC0N,sBAAD,CAA9C;EAEA/M,EAAAA,sBAAsB,CAAC,YAAM;EAC3B,QAAIiN,yBAAyB,EAA7B,EAAiC;EAC/BJ,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAAC6O;EAAhB,OAAD,CAAR;EACD;EACF,GAJqB,EAInB,CAACgC,QAAD,EAAWzK,OAAX,CAJmB,CAAtB;EAMAP,EAAAA,MAAM,CAACsB,MAAP,CAAchC,QAAd,EAAwB;EACtB4K,IAAAA,gBAAgB,EAAhBA,gBADsB;EAEtBjB,IAAAA,gBAAgB,EAAhBA,gBAFsB;EAGtBC,IAAAA,gBAAgB,EAAhBA,gBAHsB;EAItBC,IAAAA,oBAAoB,EAApBA,oBAJsB;EAKtBI,IAAAA,4BAA4B,EAA5BA;EALsB,GAAxB;EAOD;;EC/LD,IAAM8B,mBAAmB,GAAG,EAA5B;EACA,IAAMC,qBAAqB,GAAG,EAA9B;;EACA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAAC7B,KAAD,EAAQU,MAAR,EAAgBoB,SAAhB;EAAA,SAA8B9B,KAA9B;EAAA,CAAvB;;EACA,IAAM+B,iBAAiB,GAAG,SAApBA,iBAAoB,CAACtK,GAAD,EAAM+F,KAAN;EAAA,SAAgB/F,GAAG,CAAC8D,OAAJ,IAAe,EAA/B;EAAA,CAA1B;;EACA,IAAMyG,eAAe,GAAG,SAAlBA,eAAkB,CAACvK,GAAD,EAAM+F,KAAN,EAAa1G,MAAb;EAAA,eACnBA,MAAM,GAAG,CAACA,MAAM,CAACM,EAAR,EAAYoG,KAAZ,EAAmBV,IAAnB,CAAwB,GAAxB,CAAH,GAAkCU,KADrB;EAAA,CAAxB;;EAEA,IAAMyE,yBAAyB,GAAG,SAA5BA,yBAA4B,CAAApJ,CAAC;EAAA,SAAIA,CAAJ;EAAA,CAAnC;;EAEA,SAASqJ,aAAT,CAAuB1Q,KAAvB,EAA8B;EAAA,4BASxBA,KATwB,CAE1BoP,YAF0B;EAAA,MAE1BA,YAF0B,oCAEXe,mBAFW;EAAA,6BASxBnQ,KATwB,CAG1BV,aAH0B;EAAA,MAG1BA,aAH0B,qCAGV8Q,qBAHU;EAAA,0BASxBpQ,KATwB,CAI1B2Q,UAJ0B;EAAA,MAI1BA,UAJ0B,kCAIbJ,iBAJa;EAAA,wBASxBvQ,KATwB,CAK1B4Q,QAL0B;EAAA,MAK1BA,QAL0B,gCAKfJ,eALe;EAAA,4BASxBxQ,KATwB,CAM1B6Q,YAN0B;EAAA,MAM1BA,YAN0B,oCAMXR,cANW;EAAA,8BASxBrQ,KATwB,CAO1BwM,kBAP0B;EAAA,MAO1BA,kBAP0B,sCAOLiE,yBAPK;EAAA,MAQvBrQ,IARuB,iCASxBJ,KATwB;;EAW5B,sBACKI,IADL;EAEEgP,IAAAA,YAAY,EAAZA,YAFF;EAGE9P,IAAAA,aAAa,EAAbA,aAHF;EAIEqR,IAAAA,UAAU,EAAVA,UAJF;EAKEC,IAAAA,QAAQ,EAARA,QALF;EAMEC,IAAAA,YAAY,EAAZA,YANF;EAOErE,IAAAA,kBAAkB,EAAlBA;EAPF;EASD;;AAED,MAAasE,QAAQ,GAAG,SAAXA,QAAW,CAAC9Q,KAAD,EAAuB;EAAA,oCAAZ0B,OAAY;EAAZA,IAAAA,OAAY;EAAA;;EAC7C;EACA1B,EAAAA,KAAK,GAAG0Q,aAAa,CAAC1Q,KAAD,CAArB,CAF6C;;EAK7C0B,EAAAA,OAAO,IAAIwM,mBAAJ,SAA4BxM,OAA5B,CAAP,CAL6C;;EAQ7C,MAAIqP,WAAW,GAAGtO,KAAK,CAACC,MAAN,CAAa,EAAb,CAAlB,CAR6C;;EAW7C,MAAMuN,WAAW,GAAG3N,YAAY,CAACyO,WAAW,CAACpO,OAAb,CAAhC,CAX6C;;EAc7CmC,EAAAA,MAAM,CAACsB,MAAP,CAAc6J,WAAW,EAAzB,eACKjQ,KADL;EAEE0B,IAAAA,OAAO,EAAPA,OAFF;EAGEd,IAAAA,KAAK,EAAEyL,sBAAsB;EAH/B,MAd6C;;EAqB7C3K,EAAAA,OAAO,CAACuI,MAAR,CAAe+G,OAAf,EAAwBzP,OAAxB,CAAgC,UAAAS,MAAM,EAAI;EACxCA,IAAAA,MAAM,CAACiO,WAAW,GAAGrP,KAAf,CAAN;EACD,GAFD,EArB6C;;EA0B7C,MAAMmP,QAAQ,GAAGzN,YAAY,CAAC2N,WAAW,GAAGrP,KAAf,CAA7B;EACAqP,EAAAA,WAAW,GAAGF,QAAd,GAAyBA,QAAzB;EACA,SAAOE,WAAW,GAAGrP,KAArB,CA5B6C;;EA+B7CkE,EAAAA,MAAM,CAACsB,MAAP,CACE6J,WAAW,EADb,EAEEnP,WAAW,CAACiP,QAAQ,GAAGzD,UAAZ,EAAwBoE,aAAa,CAAC1Q,KAAD,CAArC,CAFb;;EA/B6C,qBA6CzCiQ,WAAW,EA7C8B;EAAA,MAqC3CgB,IArC2C,gBAqC3CA,IArC2C;EAAA,MAsClCC,WAtCkC,gBAsC3C7L,OAtC2C;EAAA,MAuC3C+J,YAvC2C,gBAuC3CA,YAvC2C;EAAA,MAwC3C9P,aAxC2C,gBAwC3CA,aAxC2C;EAAA,MAyC3CqR,UAzC2C,gBAyC3CA,UAzC2C;EAAA,MA0C3CC,QA1C2C,gBA0C3CA,QA1C2C;EAAA,MA2C3CC,YA3C2C,gBA2C3CA,YA3C2C;EAAA,MA4C3CrE,kBA5C2C,gBA4C3CA,kBA5C2C;;;EAgD7C,MAAM2E,eAAe,GAAG7O,YAAY,CAACuO,YAAD,CAApC,CAhD6C;;EAmD7C,MAAMtC,OAAO,GAAG9L,KAAK,CAACG,WAAN,CACd,UAAC4L,KAAD,EAAQU,MAAR,EAAmB;EACjB;EACA,QAAI,CAACA,MAAM,CAAC5K,IAAZ,EAAkB;EAChBpD,MAAAA,OAAO,CAACC,IAAR,CAAa;EAAE+N,QAAAA,MAAM,EAANA;EAAF,OAAb;EACA,YAAM,IAAI9N,KAAJ,CAAU,mBAAV,CAAN;EACD,KALgB;;;EAQjB,WAAO,UACF2O,QAAQ,GAAGxD,aADT,EAGD9L,KAAK,CAACC,OAAN,CAAcyQ,eAAe,EAA7B,IACAA,eAAe,EADf,GAEA,CAACA,eAAe,EAAhB,CALC,EAMLpR,MANK,CAOL,UAACqR,CAAD,EAAIC,OAAJ;EAAA,aAAgBA,OAAO,CAACD,CAAD,EAAIlC,MAAJ,EAAYV,KAAZ,EAAmByB,WAAW,EAA9B,CAAP,IAA4CmB,CAA5D;EAAA,KAPK,EAQL5C,KARK,CAAP;EAUD,GAnBa,EAoBd,CAACuB,QAAD,EAAWoB,eAAX,EAA4BlB,WAA5B,CApBc,CAAhB,CAnD6C;;EAAA,0BA2EZxN,KAAK,CAAC6O,UAAN,CAAiB/C,OAAjB,EAA0BgD,SAA1B,EAAqC;EAAA,WACpEhD,OAAO,CAACa,YAAD,EAAe;EAAE9K,MAAAA,IAAI,EAAErF,OAAO,CAACC;EAAhB,KAAf,CAD6D;EAAA,GAArC,CA3EY;EAAA,MA2EtCsS,YA3EsC;EAAA,MA2ExB1B,QA3EwB;;;EAgF7C,MAAMtB,KAAK,GAAG1N,WAAW,WACnBiP,QAAQ,GAAGvD,kBADQ,GACYA,kBADZ,IAEvBgF,YAFuB,EAGvB;EAAEpN,IAAAA,QAAQ,EAAE6L,WAAW;EAAvB,GAHuB,CAAzB;EAMAnL,EAAAA,MAAM,CAACsB,MAAP,CAAc6J,WAAW,EAAzB,EAA6B;EAC3BzB,IAAAA,KAAK,EAALA,KAD2B;EAE3BsB,IAAAA,QAAQ,EAARA;EAF2B,GAA7B,EAtF6C;;EA4F7C,MAAMzK,OAAO,GAAG5C,KAAK,CAACgP,OAAN,CACd;EAAA,WACErM,mBAAmB,CACjBtE,WAAW,CAACiP,QAAQ,GAAG1K,OAAZ,EAAqB6L,WAArB,EAAkC;EAC3C9M,MAAAA,QAAQ,EAAE6L,WAAW;EADsB,KAAlC,CADM,CADrB;EAAA,GADc,GAQZF,QARY,EASZE,WATY,EAUZiB,WAVY,SAYTpQ,WAAW,CAACiP,QAAQ,GAAGtD,WAAZ,EAAyB,EAAzB,EAA6B;EAAErI,IAAAA,QAAQ,EAAE6L,WAAW;EAAvB,GAA7B,CAZF,EAAhB;EAeAA,EAAAA,WAAW,GAAG5K,OAAd,GAAwBA,OAAxB,CA3G6C;EA8G7C;;EACA,MAAIqB,UAAU,GAAGjE,KAAK,CAACgP,OAAN,CACf;EAAA,WACE3Q,WAAW,CAACiP,QAAQ,GAAGrJ,UAAZ,EAAwBhB,cAAc,CAACL,OAAD,CAAtC,EAAiD;EAC1DjB,MAAAA,QAAQ,EAAE6L,WAAW;EADqC,KAAjD,CAAX,CAEGzK,GAFH,CAEOC,oBAFP,CADF;EAAA,GADe,GAMbJ,OANa,EAOb0K,QAPa,EAQbE,WARa,SAUVnP,WAAW,CAACiP,QAAQ,GAAGrD,cAAZ,EAA4B,EAA5B,EAAgC;EAC5CtI,IAAAA,QAAQ,EAAE6L,WAAW;EADuB,GAAhC,CAVD,EAAjB;EAeAA,EAAAA,WAAW,GAAGvJ,UAAd,GAA2BA,UAA3B,CA9H6C;;EAAA,uBAiIVjE,KAAK,CAACgP,OAAN,CAAc,YAAM;EACrD,QAAIpI,IAAI,GAAG,EAAX;EACA,QAAIqI,QAAQ,GAAG,EAAf;EACA,QAAMC,QAAQ,GAAG,EAAjB;EAEA,QAAMC,eAAe,aAAOlL,UAAP,CAArB;;EAEA,WAAOkL,eAAe,CAAChK,MAAvB,EAA+B;EAC7B,UAAMvD,MAAM,GAAGuN,eAAe,CAACC,KAAhB,EAAf;EACAC,MAAAA,mBAAmB,CAAC;EAClBb,QAAAA,IAAI,EAAJA,IADkB;EAElB5H,QAAAA,IAAI,EAAJA,IAFkB;EAGlBqI,QAAAA,QAAQ,EAARA,QAHkB;EAIlBC,QAAAA,QAAQ,EAARA,QAJkB;EAKlBtN,QAAAA,MAAM,EAANA,MALkB;EAMlBuM,QAAAA,QAAQ,EAARA,QANkB;EAOlBD,QAAAA,UAAU,EAAVA,UAPkB;EAQlBoB,QAAAA,gBAAgB,EAAEhC,QAAQ,GAAGpD,WARX;EASlBsD,QAAAA,WAAW,EAAXA;EATkB,OAAD,CAAnB;EAWD;;EAED,WAAO,CAAC5G,IAAD,EAAOqI,QAAP,EAAiBC,QAAjB,CAAP;EACD,GAvBkC,EAuBhC,CAACjL,UAAD,EAAauK,IAAb,EAAmBL,QAAnB,EAA6BD,UAA7B,EAAyCZ,QAAzC,EAAmDE,WAAnD,CAvBgC,CAjIU;EAAA,MAiItC5G,IAjIsC;EAAA,MAiIhCqI,QAjIgC;EAAA,MAiItBC,QAjIsB;;EA0J7C7M,EAAAA,MAAM,CAACsB,MAAP,CAAc6J,WAAW,EAAzB,EAA6B;EAC3B5G,IAAAA,IAAI,EAAJA,IAD2B;EAE3B2I,IAAAA,WAAW,YAAM3I,IAAN,CAFgB;EAG3BqI,IAAAA,QAAQ,EAARA,QAH2B;EAI3BC,IAAAA,QAAQ,EAARA,QAJ2B;;EAAA,GAA7B;EAQAtQ,EAAAA,SAAS,CAAC0O,QAAQ,GAAGjD,oBAAZ,EAAkCmD,WAAW,EAA7C,CAAT,CAlK6C;EAqK7C;EACA;;EACA,MAAIlD,cAAc,GAAGtK,KAAK,CAACgP,OAAN,CACnB;EAAA,WACE3Q,WAAW,CAACiP,QAAQ,GAAGhD,cAAZ,EAA4BrG,UAA5B,EAAwC;EACjDtC,MAAAA,QAAQ,EAAE6L,WAAW;EAD4B,KAAxC,CAAX,CAEGzK,GAFH,CAEO,UAAA6B,CAAC;EAAA,aAAIhB,cAAc,CAACgB,CAAD,EAAI/H,aAAJ,CAAlB;EAAA,KAFR,CADF;EAAA,GADmB,GAMjByQ,QANiB,EAOjBrJ,UAPiB,EAQjBuJ,WARiB,EASjB3Q,aATiB,SAWdwB,WAAW,CAACiP,QAAQ,GAAG/C,kBAAZ,EAAgC,EAAhC,EAAoC;EAChD5I,IAAAA,QAAQ,EAAE6L,WAAW;EAD2B,GAApC,CAXG,EAArB,CAvK6C;;EAyL7CvJ,EAAAA,UAAU,GAAGjE,KAAK,CAACgP,OAAN,CAAc,YAAM;EAC/B,QAAMpM,OAAO,aAAO0H,cAAP,CAAb;EAEArG,IAAAA,UAAU,CAACnF,OAAX,CAAmB,UAAA8C,MAAM,EAAI;EAC3B,UAAI,CAACgB,OAAO,CAAC4M,IAAR,CAAa,UAAA5K,CAAC;EAAA,eAAIA,CAAC,CAACzB,EAAF,KAASvB,MAAM,CAACuB,EAApB;EAAA,OAAd,CAAL,EAA4C;EAC1CP,QAAAA,OAAO,CAACsC,IAAR,CAAatD,MAAb;EACD;EACF,KAJD;EAMA,WAAOgB,OAAP;EACD,GAVY,EAUV,CAACqB,UAAD,EAAaqG,cAAb,CAVU,CAAb;EAWAkD,EAAAA,WAAW,GAAGvJ,UAAd,GAA2BA,UAA3B;;EAEA,EAA2C;EACzC,QAAMwL,gBAAgB,GAAGxL,UAAU,CAACuD,MAAX,CAAkB,UAAC5F,MAAD,EAASwE,CAAT,EAAe;EACxD,aAAOnC,UAAU,CAAC3E,SAAX,CAAqB,UAAAsF,CAAC;EAAA,eAAIA,CAAC,CAACzB,EAAF,KAASvB,MAAM,CAACuB,EAApB;EAAA,OAAtB,MAAkDiD,CAAzD;EACD,KAFwB,CAAzB;;EAIA,QAAIqJ,gBAAgB,CAACtK,MAArB,EAA6B;EAC3B1G,MAAAA,OAAO,CAACC,IAAR,CAAauF,UAAb;EACA,YAAM,IAAItF,KAAJ,+CACuC8Q,gBAAgB,CACxD1M,GADwC,CACpC,UAAA6B,CAAC;EAAA,eAAIA,CAAC,CAACzB,EAAN;EAAA,OADmC,EAExC0F,IAFwC,CAEnC,IAFmC,CADvC,mCAAN;EAKD;EACF,GAnN4C;;;EAsN7C,MAAM1E,YAAY,GAAGnE,KAAK,CAACgP,OAAN,CACnB;EAAA,WACE3Q,WAAW,CACTiP,QAAQ,GAAGnJ,YADF,EAETH,gBAAgB,CAACsG,cAAD,EAAiBzN,aAAjB,CAFP,EAGT2Q,WAAW,EAHF,CADb;EAAA,GADmB,GAQjBF,QARiB,EASjBhD,cATiB,EAUjBzN,aAViB,EAWjB2Q,WAXiB,SAadnP,WAAW,CAACiP,QAAQ,GAAG9C,gBAAZ,EAA8B,EAA9B,EAAkC;EAC9C7I,IAAAA,QAAQ,EAAE6L,WAAW;EADyB,GAAlC,CAbG,EAArB;EAkBAA,EAAAA,WAAW,GAAGrJ,YAAd,GAA6BA,YAA7B,CAxO6C;;EA2O7C,MAAMK,OAAO,GAAGxE,KAAK,CAACgP,OAAN,CACd;EAAA,WAAO7K,YAAY,CAACgB,MAAb,GAAsBhB,YAAY,CAAC,CAAD,CAAZ,CAAgBK,OAAtC,GAAgD,EAAvD;EAAA,GADc,EAEd,CAACL,YAAD,CAFc,CAAhB;EAIAqJ,EAAAA,WAAW,GAAGhJ,OAAd,GAAwBA,OAAxB,CA/O6C;;EAkP7CgJ,EAAAA,WAAW,GAAGJ,WAAd,GAA4BjJ,YAAY,CAAC7G,MAAb,CAC1B,UAACoS,GAAD,EAAMnL,WAAN;EAAA,qBAA0BmL,GAA1B,EAAkCnL,WAAW,CAACC,OAA9C;EAAA,GAD0B,EAE1B,EAF0B,CAA5B;EAKA5F,EAAAA,SAAS,CAAC0O,QAAQ,GAAG7C,2BAAZ,EAAyC+C,WAAW,EAApD,CAAT,CAvP6C;;EA0P7C,MAAMmC,iBAAiB,GAAGrF,cAAc,CACrC9C,MADuB,CAChB,UAAA5C,CAAC;EAAA,WAAIA,CAAC,CAACyH,SAAN;EAAA,GADe,EAEvBtJ,GAFuB,CAEnB,UAAA6B,CAAC;EAAA,WAAIA,CAAC,CAACzB,EAAN;EAAA,GAFkB,EAGvByM,IAHuB,GAIvB/G,IAJuB,CAIlB,GAJkB,CAA1B;EAMAyB,EAAAA,cAAc,GAAGtK,KAAK,CAACgP,OAAN,CACf;EAAA,WAAM1E,cAAc,CAAC9C,MAAf,CAAsB,UAAA5C,CAAC;EAAA,aAAIA,CAAC,CAACyH,SAAN;EAAA,KAAvB,CAAN;EAAA,GADe;EAGf,GAAC/B,cAAD,EAAiBqF,iBAAjB,CAHe,CAAjB;EAKAnC,EAAAA,WAAW,GAAGlD,cAAd,GAA+BA,cAA/B,CArQ6C;;EAAA,8BA4QzCuF,qBAAqB,CAACrL,OAAD,CA5QoB;EAAA,MAyQ3CsL,oBAzQ2C;EAAA,MA0Q3CC,iBA1Q2C;EAAA,MA2Q3CC,oBA3Q2C;;EA8Q7CxC,EAAAA,WAAW,GAAGsC,oBAAd,GAAqCA,oBAArC;EACAtC,EAAAA,WAAW,GAAGuC,iBAAd,GAAkCA,iBAAlC;EACAvC,EAAAA,WAAW,GAAGwC,oBAAd,GAAqCA,oBAArC;EAEApR,EAAAA,SAAS,CAAC0O,QAAQ,GAAG5C,WAAZ,EAAyB8C,WAAW,EAApC,CAAT;EAGA;EAHA;EAIC,YAAIA,WAAW,GAAGJ,WAAlB,EAAkCI,WAAW,GAAGvJ,UAAhD,EAA4DnF,OAA5D,CACC,UAAA8C,MAAM,EAAI;EACR;EACAA,IAAAA,MAAM,CAACqO,MAAP,GAAgBvO,YAAY,CAAC8L,WAAW,EAAZ,EAAgB5L,MAAhB,CAA5B,CAFQ;;EAKRA,IAAAA,MAAM,CAACoJ,cAAP,GAAwB9M,cAAc,CAACoP,QAAQ,GAAGtC,cAAZ,EAA4B;EAChErJ,MAAAA,QAAQ,EAAE6L,WAAW,EAD2C;EAEhE5L,MAAAA,MAAM,EAANA;EAFgE,KAA5B,CAAtC,CALQ;;EAWRA,IAAAA,MAAM,CAACqJ,cAAP,GAAwB/M,cAAc,CAACoP,QAAQ,GAAGrC,cAAZ,EAA4B;EAChEtJ,MAAAA,QAAQ,EAAE6L,WAAW,EAD2C;EAEhE5L,MAAAA,MAAM,EAANA;EAFgE,KAA5B,CAAtC;EAID,GAhBF;EAmBD4L,EAAAA,WAAW,GAAGrJ,YAAd,GAA6BnE,KAAK,CAACgP,OAAN,CAC3B;EAAA,WACE7K,YAAY,CAACqD,MAAb,CAAoB,UAACjD,WAAD,EAAc6B,CAAd,EAAoB;EACtC;EACA7B,MAAAA,WAAW,CAACC,OAAZ,GAAsBD,WAAW,CAACC,OAAZ,CAAoBgD,MAApB,CAA2B,UAAA5F,MAAM,EAAI;EACzD,YAAM8E,OAAO,GAAG,SAAVA,OAAU,CAAAlC,OAAO;EAAA,iBACrBA,OAAO,CAACgD,MAAR,CAAe,UAAA5F,MAAM,EAAI;EACvB,gBAAIA,MAAM,CAAC4C,OAAX,EAAoB;EAClB,qBAAOkC,OAAO,CAAC9E,MAAM,CAAC4C,OAAR,CAAd;EACD;;EACD,mBAAO5C,MAAM,CAACyK,SAAd;EACD,WALD,EAKGlH,MANkB;EAAA,SAAvB;;EAOA,YAAIvD,MAAM,CAAC4C,OAAX,EAAoB;EAClB,iBAAOkC,OAAO,CAAC9E,MAAM,CAAC4C,OAAR,CAAd;EACD;;EACD,eAAO5C,MAAM,CAACyK,SAAd;EACD,OAZqB,CAAtB,CAFsC;;EAiBtC,UAAI9H,WAAW,CAACC,OAAZ,CAAoBW,MAAxB,EAAgC;EAC9BZ,QAAAA,WAAW,CAACuG,mBAAZ,GAAkC5M,cAAc,CAC9CoP,QAAQ,GAAGxC,mBADmC,EAE9C;EAAEnJ,UAAAA,QAAQ,EAAE6L,WAAW,EAAvB;EAA2BjJ,UAAAA,WAAW,EAAXA,WAA3B;EAAwCgF,UAAAA,KAAK,EAAEnD;EAA/C,SAF8C,CAAhD;EAKA7B,QAAAA,WAAW,CAACwG,mBAAZ,GAAkC7M,cAAc,CAC9CoP,QAAQ,GAAGvC,mBADmC,EAE9C;EAAEpJ,UAAAA,QAAQ,EAAE6L,WAAW,EAAvB;EAA2BjJ,UAAAA,WAAW,EAAXA,WAA3B;EAAwCgF,UAAAA,KAAK,EAAEnD;EAA/C,SAF8C,CAAhD;EAKA,eAAO,IAAP;EACD;;EAED,aAAO,KAAP;EACD,KAhCD,CADF;EAAA,GAD2B,EAmC3B,CAACjC,YAAD,EAAeqJ,WAAf,EAA4BF,QAA5B,CAnC2B,CAA7B;EAsCAE,EAAAA,WAAW,GAAG0C,YAAd,GAA6B,UAAI1C,WAAW,GAAGrJ,YAAlB,EAAgCW,OAAhC,EAA7B,CA/U6C;EAkV7C;;EAEA0I,EAAAA,WAAW,GAAG7C,UAAd,GAA2B3K,KAAK,CAACG,WAAN,CACzB,UAAAqD,GAAG,EAAI;EACLA,IAAAA,GAAG,CAAC0H,WAAJ,GAAkBhN,cAAc,CAACoP,QAAQ,GAAGpC,WAAZ,EAAyB;EACvDvJ,MAAAA,QAAQ,EAAE6L,WAAW,EADkC;EAEvDhK,MAAAA,GAAG,EAAHA;EAFuD,KAAzB,CAAhC,CADK;;EAOLA,IAAAA,GAAG,CAAC2M,QAAJ,GAAelM,UAAU,CAAClB,GAAX,CAAe,UAAAnB,MAAM,EAAI;EACtC,UAAMjF,KAAK,GAAG6G,GAAG,CAAC4M,MAAJ,CAAWxO,MAAM,CAACuB,EAAlB,CAAd;EAEA,UAAMwG,IAAI,GAAG;EACX/H,QAAAA,MAAM,EAANA,MADW;EAEX4B,QAAAA,GAAG,EAAHA,GAFW;EAGX7G,QAAAA,KAAK,EAALA;EAHW,OAAb,CAHsC;;EAUtCgN,MAAAA,IAAI,CAACwB,YAAL,GAAoBjN,cAAc,CAACoP,QAAQ,GAAGnC,YAAZ,EAA0B;EAC1DxJ,QAAAA,QAAQ,EAAE6L,WAAW,EADqC;EAE1D7D,QAAAA,IAAI,EAAJA;EAF0D,OAA1B,CAAlC,CAVsC;;EAgBtCA,MAAAA,IAAI,CAACsG,MAAL,GAAcvO,YAAY,CAAC8L,WAAW,EAAZ,EAAgB5L,MAAhB,EAAwB;EAChD4B,QAAAA,GAAG,EAAHA,GADgD;EAEhDmG,QAAAA,IAAI,EAAJA,IAFgD;EAGhDhN,QAAAA,KAAK,EAALA;EAHgD,OAAxB,CAA1B;EAMA,aAAOgN,IAAP;EACD,KAvBc,CAAf;EAyBAnG,IAAAA,GAAG,CAAC6M,KAAJ,GAAY/F,cAAc,CAACvH,GAAf,CAAmB,UAAAnB,MAAM;EAAA,aACnC4B,GAAG,CAAC2M,QAAJ,CAAaX,IAAb,CAAkB,UAAA7F,IAAI;EAAA,eAAIA,IAAI,CAAC/H,MAAL,CAAYuB,EAAZ,KAAmBvB,MAAM,CAACuB,EAA9B;EAAA,OAAtB,CADmC;EAAA,KAAzB,CAAZ,CAhCK;;EAqCLvE,IAAAA,SAAS,CAAC0O,QAAQ,GAAG3C,UAAZ,EAAwBnH,GAAxB,EAA6B;EAAE7B,MAAAA,QAAQ,EAAE6L,WAAW;EAAvB,KAA7B,CAAT;EACD,GAvCwB,EAwCzB,CAACF,QAAD,EAAWE,WAAX,EAAwBvJ,UAAxB,EAAoCqG,cAApC,CAxCyB,CAA3B;EA2CAkD,EAAAA,WAAW,GAAG5C,aAAd,GAA8B1M,cAAc,CAACoP,QAAQ,GAAG1C,aAAZ,EAA2B;EACrEjJ,IAAAA,QAAQ,EAAE6L,WAAW;EADgD,GAA3B,CAA5C;EAIAA,EAAAA,WAAW,GAAG3C,iBAAd,GAAkC3M,cAAc,CAC9CoP,QAAQ,GAAGzC,iBADmC,EAE9C;EACElJ,IAAAA,QAAQ,EAAE6L,WAAW;EADvB,GAF8C,CAAhD;EAOA5O,EAAAA,SAAS,CAAC0O,QAAQ,GAAGlC,gBAAZ,EAA8BoC,WAAW,EAAzC,CAAT;EAEA,SAAOA,WAAW,EAAlB;EACD,CA7YM;;EA+YP,SAASqC,qBAAT,CAA+BrL,OAA/B,EAAwC8L,IAAxC,EAAkD;EAAA,MAAVA,IAAU;EAAVA,IAAAA,IAAU,GAAH,CAAG;EAAA;;EAChD,MAAIC,gBAAgB,GAAG,CAAvB;EACA,MAAIC,aAAa,GAAG,CAApB;EACA,MAAIC,gBAAgB,GAAG,CAAvB;EACA,MAAIC,iBAAiB,GAAG,CAAxB;EAEAlM,EAAAA,OAAO,CAAC1F,OAAR,CAAgB,UAAA6R,MAAM,EAAI;EAAA,QACTC,UADS,GACMD,MADN,CAClBnM,OADkB;EAGxBmM,IAAAA,MAAM,CAACE,SAAP,GAAmBP,IAAnB;;EAEA,QAAIM,UAAU,IAAIA,UAAU,CAACzL,MAA7B,EAAqC;EAAA,mCAM/B0K,qBAAqB,CAACe,UAAD,EAAaN,IAAb,CANU;EAAA,UAEjCQ,aAFiC;EAAA,UAGjCC,UAHiC;EAAA,UAIjCC,aAJiC;EAAA,UAKjCC,cALiC;;EAOnCN,MAAAA,MAAM,CAACG,aAAP,GAAuBA,aAAvB;EACAH,MAAAA,MAAM,CAACI,UAAP,GAAoBA,UAApB;EACAJ,MAAAA,MAAM,CAACK,aAAP,GAAuBA,aAAvB;EACAL,MAAAA,MAAM,CAACM,cAAP,GAAwBA,cAAxB;EACD,KAXD,MAWO;EACLN,MAAAA,MAAM,CAACG,aAAP,GAAuBH,MAAM,CAAC3T,QAA9B;EACA2T,MAAAA,MAAM,CAACI,UAAP,GAAoBG,IAAI,CAACC,GAAL,CAClBD,IAAI,CAACE,GAAL,CAAST,MAAM,CAAC3T,QAAhB,EAA0B2T,MAAM,CAAC5T,KAAjC,CADkB,EAElB4T,MAAM,CAAC1T,QAFW,CAApB;EAIA0T,MAAAA,MAAM,CAACK,aAAP,GAAuBL,MAAM,CAAC1T,QAA9B;EACA0T,MAAAA,MAAM,CAACM,cAAP,GAAwBN,MAAM,CAACU,SAAP,GAAmBV,MAAM,CAACI,UAA1B,GAAuC,CAA/D;EACD;;EACD,QAAIJ,MAAM,CAACtE,SAAX,EAAsB;EACpBiE,MAAAA,IAAI,IAAIK,MAAM,CAACI,UAAf;EACAR,MAAAA,gBAAgB,IAAII,MAAM,CAACG,aAA3B;EACAN,MAAAA,aAAa,IAAIG,MAAM,CAACI,UAAxB;EACAN,MAAAA,gBAAgB,IAAIE,MAAM,CAACK,aAA3B;EACAN,MAAAA,iBAAiB,IAAIC,MAAM,CAACM,cAA5B;EACD;EACF,GAhCD;EAkCA,SAAO,CAACV,gBAAD,EAAmBC,aAAnB,EAAkCC,gBAAlC,EAAoDC,iBAApD,CAAP;EACD;;EAED,SAASrB,mBAAT,OAUG;EAAA,MATDb,IASC,QATDA,IASC;EAAA,MARD5H,IAQC,QARDA,IAQC;EAAA,MAPDqI,QAOC,QAPDA,QAOC;EAAA,MANDC,QAMC,QANDA,QAMC;EAAA,MALDtN,MAKC,QALDA,MAKC;EAAA,MAJDuM,QAIC,QAJDA,QAIC;EAAA,MAHDD,UAGC,QAHDA,UAGC;EAAA,MAFDoB,gBAEC,QAFDA,gBAEC;EAAA,MADD9B,WACC,QADDA,WACC;;EACD;EACA;EACA;EACA,MAAM8D,SAAS,GAAG,SAAZA,SAAY,CAACC,WAAD,EAAcC,QAAd,EAAwB1O,KAAxB,EAAmCD,MAAnC,EAA2C4O,UAA3C,EAA0D;EAAA,QAAlC3O,KAAkC;EAAlCA,MAAAA,KAAkC,GAA1B,CAA0B;EAAA;;EAC1E;EACA,QAAMsE,QAAQ,GAAGmK,WAAjB;EAEA,QAAMpO,EAAE,GAAGgL,QAAQ,CAACoD,WAAD,EAAcC,QAAd,EAAwB3O,MAAxB,CAAnB;EAEA,QAAIW,GAAG,GAAG0L,QAAQ,CAAC/L,EAAD,CAAlB,CAN0E;;EAS1E,QAAI,CAACK,GAAL,EAAU;EACRA,MAAAA,GAAG,GAAG;EACJL,QAAAA,EAAE,EAAFA,EADI;EAEJiE,QAAAA,QAAQ,EAARA,QAFI;EAGJmC,QAAAA,KAAK,EAAEiI,QAHH;EAIJ1O,QAAAA,KAAK,EAALA,KAJI;EAKJuN,QAAAA,KAAK,EAAE,CAAC,EAAD,CALH;;EAAA,OAAN,CADQ;EAUR;;EACA7M,MAAAA,GAAG,CAAC6M,KAAJ,CAAUtN,GAAV,GAAgB+E,uBAAhB;EACAtE,MAAAA,GAAG,CAAC6M,KAAJ,CAAU7I,MAAV,GAAmBM,uBAAnB;EACAtE,MAAAA,GAAG,CAAC6M,KAAJ,CAAUvR,OAAV,GAAoBgJ,uBAApB;EACAtE,MAAAA,GAAG,CAAC6M,KAAJ,CAAU,CAAV,EAAalF,YAAb,GAA4BrD,uBAA5B,CAdQ;;EAiBRtE,MAAAA,GAAG,CAAC4M,MAAJ,GAAa,EAAb,CAjBQ;;EAoBRqB,MAAAA,UAAU,CAACvM,IAAX,CAAgB1B,GAAhB,EApBQ;;EAsBRyL,MAAAA,QAAQ,CAAC/J,IAAT,CAAc1B,GAAd,EAtBQ;;EAwBR0L,MAAAA,QAAQ,CAAC/L,EAAD,CAAR,GAAeK,GAAf,CAxBQ;;EA2BRA,MAAAA,GAAG,CAACkO,eAAJ,GAAsBxD,UAAU,CAACqD,WAAD,EAAcC,QAAd,CAAhC,CA3BQ;;EA8BR,UAAIhO,GAAG,CAACkO,eAAR,EAAyB;EACvB,YAAMpK,OAAO,GAAG,EAAhB;EACA9D,QAAAA,GAAG,CAACkO,eAAJ,CAAoB5S,OAApB,CAA4B,UAAC8F,CAAD,EAAIwB,CAAJ;EAAA,iBAC1BkL,SAAS,CAAC1M,CAAD,EAAIwB,CAAJ,EAAOtD,KAAK,GAAG,CAAf,EAAkBU,GAAlB,EAAuB8D,OAAvB,CADiB;EAAA,SAA5B,EAFuB;;EAMvB9D,QAAAA,GAAG,CAAC8D,OAAJ,GAAcA,OAAd;EACD;EACF,KAtCD,MAsCO,IAAI9D,GAAG,CAAC8D,OAAR,EAAiB;EACtB;EACA;EACA;EACA9D,MAAAA,GAAG,CAACkO,eAAJ,CAAoB5S,OAApB,CAA4B,UAAC8F,CAAD,EAAIwB,CAAJ;EAAA,eAAUkL,SAAS,CAAC1M,CAAD,EAAIwB,CAAJ,EAAOtD,KAAK,GAAG,CAAf,EAAkBU,GAAlB,CAAnB;EAAA,OAA5B;EACD,KApDyE;;;EAuD1E,QAAI5B,MAAM,CAACwB,QAAX,EAAqB;EACnBI,MAAAA,GAAG,CAAC4M,MAAJ,CAAWxO,MAAM,CAACuB,EAAlB,IAAwBvB,MAAM,CAACwB,QAAP,CACtBmO,WADsB,EAEtBC,QAFsB,EAGtBhO,GAHsB,EAItBiO,UAJsB,EAKtBjD,IALsB,CAAxB;EAOD,KA/DyE;;;EAkE1EhL,IAAAA,GAAG,CAAC4M,MAAJ,CAAWxO,MAAM,CAACuB,EAAlB,IAAwB9E,WAAW,CACjCiR,gBADiC,EAEjC9L,GAAG,CAAC4M,MAAJ,CAAWxO,MAAM,CAACuB,EAAlB,CAFiC,EAGjC;EACEK,MAAAA,GAAG,EAAHA,GADF;EAEE5B,MAAAA,MAAM,EAANA,MAFF;EAGED,MAAAA,QAAQ,EAAE6L,WAAW;EAHvB,KAHiC,EAQjC,IARiC,CAAnC;EAUD,GA5ED;;EA8EAgB,EAAAA,IAAI,CAAC1P,OAAL,CAAa,UAACyS,WAAD,EAAcC,QAAd;EAAA,WACXF,SAAS,CAACC,WAAD,EAAcC,QAAd,EAAwB,CAAxB,EAA2B1C,SAA3B,EAAsClI,IAAtC,CADE;EAAA,GAAb;EAGD;;ECpkBDpK,OAAO,CAACmV,aAAR,GAAwB,eAAxB;EACAnV,OAAO,CAACoV,iBAAR,GAA4B,mBAA5B;EACApV,OAAO,CAACqV,qBAAR,GAAgC,uBAAhC;AAEA,MAAaC,WAAW,GAAG,SAAdA,WAAc,CAAA3T,KAAK,EAAI;EAClCA,EAAAA,KAAK,CAAC4T,6BAAN,GAAsC,CAACC,oCAAD,CAAtC;EACA7T,EAAAA,KAAK,CAAC8T,yBAAN,GAAkC,CAACC,gCAAD,CAAlC;EACA/T,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,aAAvB;EACAvM,EAAAA,KAAK,CAACwM,UAAN,CAAiBzF,IAAjB,CAAsByF,UAAtB;EACD,CANM;EAQPmH,WAAW,CAAC3S,UAAZ,GAAyB,aAAzB;;EAEA,IAAM6S,oCAAoC,GAAG,SAAvCA,oCAAuC,CAACzU,KAAD;EAAA,MAAUoE,QAAV,QAAUA,QAAV;EAAA,SAAyB,CACpEpE,KADoE,EAEpE;EACE4U,IAAAA,OAAO,EAAE,iBAAAjM,CAAC,EAAI;EACZvE,MAAAA,QAAQ,CAACkQ,qBAAT;EACD,KAHH;EAIEpU,IAAAA,KAAK,EAAE;EACLuI,MAAAA,MAAM,EAAE;EADH,KAJT;EAOEsG,IAAAA,KAAK,EAAE;EAPT,GAFoE,CAAzB;EAAA,CAA7C;;EAaA,IAAM4F,gCAAgC,GAAG,SAAnCA,gCAAmC,CAAC3U,KAAD;EAAA,MAAUiG,GAAV,SAAUA,GAAV;EAAA,SAAoB,CAC3DjG,KAD2D,EAE3D;EACE4U,IAAAA,OAAO,EAAE,mBAAM;EACb3O,MAAAA,GAAG,CAACoO,iBAAJ;EACD,KAHH;EAIEnU,IAAAA,KAAK,EAAE;EACLuI,MAAAA,MAAM,EAAE;EADH,KAJT;EAOEsG,IAAAA,KAAK,EAAE;EAPT,GAF2D,CAApB;EAAA,CAAzC;;;EAcA,SAASR,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EACvD,MAAI8K,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACEqK,MAAAA,QAAQ,EAAE;EADZ,OAEKiF,KAFL;EAID;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACmV,aAA5B,EAA2C;EACzC,wBACK5F,KADL;EAEEjF,MAAAA,QAAQ,EAAEnF,QAAQ,CAACgL,YAAT,CAAsB7F,QAAtB,IAAkC;EAF9C;EAID;;EAED,MAAI2F,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACqV,qBAA5B,EAAmD;EAAA,QACzClV,KADyC,GAC/B8P,MAD+B,CACzC9P,KADyC;EAAA,QAEzCuS,QAFyC,GAE5BvN,QAF4B,CAEzCuN,QAFyC;EAIjD,QAAMkD,iBAAiB,GACrB/P,MAAM,CAACgQ,IAAP,CAAYnD,QAAZ,EAAsB/J,MAAtB,KAAiC9C,MAAM,CAACgQ,IAAP,CAAYtG,KAAK,CAACjF,QAAlB,EAA4B3B,MAD/D;EAGA,QAAMmN,SAAS,GAAG,OAAO3V,KAAP,KAAiB,WAAjB,GAA+BA,KAA/B,GAAuC,CAACyV,iBAA1D;;EAEA,QAAIE,SAAJ,EAAe;EACb,UAAMxL,QAAQ,GAAG,EAAjB;EAEAzE,MAAAA,MAAM,CAACgQ,IAAP,CAAYnD,QAAZ,EAAsBpQ,OAAtB,CAA8B,UAAAyT,KAAK,EAAI;EACrCzL,QAAAA,QAAQ,CAACyL,KAAD,CAAR,GAAkB,IAAlB;EACD,OAFD;EAIA,0BACKxG,KADL;EAEEjF,QAAAA,QAAQ,EAARA;EAFF;EAID;;EAED,wBACKiF,KADL;EAEEjF,MAAAA,QAAQ,EAAE;EAFZ;EAID;;EAED,MAAI2F,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACoV,iBAA5B,EAA+C;EAAA,QACrCzO,EADqC,GACVsJ,MADU,CACrCtJ,EADqC;EAAA,QAC1BqP,WAD0B,GACV/F,MADU,CACjC9P,KADiC;EAE7C,QAAM8V,MAAM,GAAG1G,KAAK,CAACjF,QAAN,CAAe3D,EAAf,CAAf;EAEA,QAAMuP,WAAW,GACf,OAAOF,WAAP,KAAuB,WAAvB,GAAqCA,WAArC,GAAmD,CAACC,MADtD;;EAGA,QAAI,CAACA,MAAD,IAAWC,WAAf,EAA4B;EAAA;;EAC1B,0BACK3G,KADL;EAEEjF,QAAAA,QAAQ,eACHiF,KAAK,CAACjF,QADH,6BAEL3D,EAFK,IAEA,IAFA;EAFV;EAOD,KARD,MAQO,IAAIsP,MAAM,IAAI,CAACC,WAAf,EAA4B;EAAA,4BACJ3G,KAAK,CAACjF,QADF;EAAA,UACnB6L,CADmB,mBACxBxP,EADwB;EAAA,UACbxF,IADa,mDACxBwF,EADwB;;EAEjC,0BACK4I,KADL;EAEEjF,QAAAA,QAAQ,EAAEnJ;EAFZ;EAID,KANM,MAMA;EACL,aAAOoO,KAAP;EACD;EACF;EACF;;EAED,SAASrB,aAAT,CAAqB/I,QAArB,EAA+B;EAAA,MAE3B6M,IAF2B,GAazB7M,QAbyB,CAE3B6M,IAF2B;EAAA,MAG3B5H,IAH2B,GAazBjF,QAbyB,CAG3BiF,IAH2B;EAAA,MAI3BsI,QAJ2B,GAazBvN,QAbyB,CAI3BuN,QAJ2B;EAAA,8BAazBvN,QAbyB,CAK3BkF,iBAL2B;EAAA,MAK3BA,iBAL2B,sCAKP,UALO;EAAA,8BAazBlF,QAbyB,CAM3BiR,oBAN2B;EAAA,MAM3BA,oBAN2B,sCAMJ,IANI;EAAA,8BAazBjR,QAbyB,CAO3BoF,aAP2B;EAAA,MAO3BA,aAP2B,sCAOX,IAPW;EAAA,8BAazBpF,QAbyB,CAQ3BkR,iBAR2B;EAAA,MAQ3BA,iBAR2B,sCAQP,IARO;EAAA,MAS3BvF,QAT2B,GAazB3L,QAbyB,CAS3B2L,QAT2B;EAAA,MAU3BrO,OAV2B,GAazB0C,QAbyB,CAU3B1C,OAV2B;EAAA,MAWlB6H,QAXkB,GAazBnF,QAbyB,CAW3BoK,KAX2B,CAWlBjF,QAXkB;EAAA,MAY3BuG,QAZ2B,GAazB1L,QAbyB,CAY3B0L,QAZ2B;EAe7BrO,EAAAA,iBAAiB,CACfC,OADe,EAEf,CAAC,WAAD,EAAc,YAAd,EAA4B,iBAA5B,EAA+C,iBAA/C,CAFe,EAGf,aAHe,CAAjB;EAMA,MAAM6T,oBAAoB,GAAGjT,YAAY,CAACgT,iBAAD,CAAzC;EAEA,MAAIT,iBAAiB,GAAG7D,OAAO,CAC7BlM,MAAM,CAACgQ,IAAP,CAAYnD,QAAZ,EAAsB/J,MAAtB,IAAgC9C,MAAM,CAACgQ,IAAP,CAAYvL,QAAZ,EAAsB3B,MADzB,CAA/B;;EAIA,MAAIiN,iBAAJ,EAAuB;EACrB,QAAI/P,MAAM,CAACgQ,IAAP,CAAYnD,QAAZ,EAAsBvK,IAAtB,CAA2B,UAAAxB,EAAE;EAAA,aAAI,CAAC2D,QAAQ,CAAC3D,EAAD,CAAb;EAAA,KAA7B,CAAJ,EAAqD;EACnDiP,MAAAA,iBAAiB,GAAG,KAApB;EACD;EACF,GA/B4B;;;EAkC7B5R,EAAAA,sBAAsB,CAAC,YAAM;EAC3B,QAAIsS,oBAAoB,EAAxB,EAA4B;EAC1BzF,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAACmV;EAAhB,OAAD,CAAR;EACD;EACF,GAJqB,EAInB,CAACtE,QAAD,EAAWmB,IAAX,CAJmB,CAAtB;EAMA,MAAMoD,iBAAiB,GAAG5R,KAAK,CAACG,WAAN,CACxB,UAACgD,EAAD,EAAKxG,KAAL,EAAe;EACb0Q,IAAAA,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACoV,iBAAhB;EAAmCzO,MAAAA,EAAE,EAAFA,EAAnC;EAAuCxG,MAAAA,KAAK,EAALA;EAAvC,KAAD,CAAR;EACD,GAHuB,EAIxB,CAAC0Q,QAAD,CAJwB,CAA1B;EAOA,MAAMwE,qBAAqB,GAAG7R,KAAK,CAACG,WAAN,CAC5B,UAAAxD,KAAK;EAAA,WAAI0Q,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACqV,qBAAhB;EAAuClV,MAAAA,KAAK,EAALA;EAAvC,KAAD,CAAZ;EAAA,GADuB,EAE5B,CAAC0Q,QAAD,CAF4B,CAA9B;EAKA,MAAMrG,YAAY,GAAGhH,KAAK,CAACgP,OAAN,CAAc,YAAM;EACvC,QAAI4D,oBAAJ,EAA0B;EACxB,aAAOjM,UAAU,CAACC,IAAD,EAAO;EAAEC,QAAAA,iBAAiB,EAAjBA,iBAAF;EAAqBC,QAAAA,QAAQ,EAARA,QAArB;EAA+BC,QAAAA,aAAa,EAAbA;EAA/B,OAAP,CAAjB;EACD;;EAED,WAAOH,IAAP;EACD,GANoB,EAMlB,CAACgM,oBAAD,EAAuBhM,IAAvB,EAA6BC,iBAA7B,EAAgDC,QAAhD,EAA0DC,aAA1D,CANkB,CAArB;EAQA,MAAMgM,aAAa,GAAG/S,KAAK,CAACgP,OAAN,CAAc;EAAA,WAAMgE,iBAAiB,CAAClM,QAAD,CAAvB;EAAA,GAAd,EAAiD,CACrEA,QADqE,CAAjD,CAAtB;EAIA,MAAM0G,WAAW,GAAG3N,YAAY,CAAC8B,QAAD,CAAhC;EAEA,MAAMoQ,6BAA6B,GAAG7T,cAAc,CAClDoP,QAAQ,GAAGyE,6BADuC,EAElD;EAAEpQ,IAAAA,QAAQ,EAAE6L,WAAW;EAAvB,GAFkD,CAApD;EAKAnL,EAAAA,MAAM,CAACsB,MAAP,CAAchC,QAAd,EAAwB;EACtBsR,IAAAA,eAAe,EAAErM,IADK;EAEtBI,IAAAA,YAAY,EAAZA,YAFsB;EAGtBJ,IAAAA,IAAI,EAAEI,YAHgB;EAItB+L,IAAAA,aAAa,EAAbA,aAJsB;EAKtBX,IAAAA,iBAAiB,EAAjBA,iBALsB;EAMtBR,IAAAA,iBAAiB,EAAjBA,iBANsB;EAOtBC,IAAAA,qBAAqB,EAArBA,qBAPsB;EAQtBE,IAAAA,6BAA6B,EAA7BA;EARsB,GAAxB;EAUD;;EAED,SAASpH,UAAT,CAAoBnH,GAApB,SAA+D;EAAA,MAAxB8J,QAAwB,SAApC3L,QAAoC,CAAxB2L,QAAwB;EAAA,MAAZ3L,QAAY,SAAZA,QAAY;;EAC7D6B,EAAAA,GAAG,CAACoO,iBAAJ,GAAwB,UAAA9L,GAAG;EAAA,WAAInE,QAAQ,CAACiQ,iBAAT,CAA2BpO,GAAG,CAACL,EAA/B,EAAmC2C,GAAnC,CAAJ;EAAA,GAA3B;;EAEAtC,EAAAA,GAAG,CAACyO,yBAAJ,GAAgC/T,cAAc,CAC5CoP,QAAQ,GAAG2E,yBADiC,EAE5C;EACEtQ,IAAAA,QAAQ,EAARA,QADF;EAEE6B,IAAAA,GAAG,EAAHA;EAFF,GAF4C,CAA9C;EAOD;;EAED,SAASwP,iBAAT,CAA2BlM,QAA3B,EAAqC;EACnC,MAAIoM,QAAQ,GAAG,CAAf;EAEA7Q,EAAAA,MAAM,CAACgQ,IAAP,CAAYvL,QAAZ,EAAsBhI,OAAtB,CAA8B,UAAAqE,EAAE,EAAI;EAClC,QAAMgQ,OAAO,GAAGhQ,EAAE,CAACI,KAAH,CAAS,GAAT,CAAhB;EACA2P,IAAAA,QAAQ,GAAGhC,IAAI,CAACE,GAAL,CAAS8B,QAAT,EAAmBC,OAAO,CAAChO,MAA3B,CAAX;EACD,GAHD;EAKA,SAAO+N,QAAP;EACD;;ECpOM,IAAMvL,IAAI,GAAG,SAAPA,IAAO,CAACf,IAAD,EAAOwM,GAAP,EAAYC,WAAZ,EAA4B;EAC9CzM,EAAAA,IAAI,GAAGA,IAAI,CAACY,MAAL,CAAY,UAAAhE,GAAG,EAAI;EACxB,WAAO4P,GAAG,CAACzO,IAAJ,CAAS,UAAAxB,EAAE,EAAI;EACpB,UAAMmQ,QAAQ,GAAG9P,GAAG,CAAC4M,MAAJ,CAAWjN,EAAX,CAAjB;EACA,aAAOwF,MAAM,CAAC2K,QAAD,CAAN,CACJC,WADI,GAEJ9Q,QAFI,CAEKkG,MAAM,CAAC0K,WAAD,CAAN,CAAoBE,WAApB,EAFL,CAAP;EAGD,KALM,CAAP;EAMD,GAPM,CAAP;EAQA,SAAO3M,IAAP;EACD,CAVM;;EAYPe,IAAI,CAACE,UAAL,GAAkB,UAAA9B,GAAG;EAAA,SAAI,CAACA,GAAL;EAAA,CAArB;;AAEA,EAAO,IAAMyN,SAAS,GAAG,SAAZA,SAAY,CAAC5M,IAAD,EAAOwM,GAAP,EAAYC,WAAZ,EAA4B;EACnD,SAAOzM,IAAI,CAACY,MAAL,CAAY,UAAAhE,GAAG,EAAI;EACxB,WAAO4P,GAAG,CAACzO,IAAJ,CAAS,UAAAxB,EAAE,EAAI;EACpB,UAAMmQ,QAAQ,GAAG9P,GAAG,CAAC4M,MAAJ,CAAWjN,EAAX,CAAjB;EACA,aAAOmQ,QAAQ,KAAKxE,SAAb,GACHnG,MAAM,CAAC2K,QAAD,CAAN,CAAiBC,WAAjB,OAAmC5K,MAAM,CAAC0K,WAAD,CAAN,CAAoBE,WAApB,EADhC,GAEH,IAFJ;EAGD,KALM,CAAP;EAMD,GAPM,CAAP;EAQD,CATM;;EAWPC,SAAS,CAAC3L,UAAV,GAAuB,UAAA9B,GAAG;EAAA,SAAI,CAACA,GAAL;EAAA,CAA1B;;AAEA,EAAO,IAAM0N,aAAa,GAAG,SAAhBA,aAAgB,CAAC7M,IAAD,EAAOwM,GAAP,EAAYC,WAAZ,EAA4B;EACvD,SAAOzM,IAAI,CAACY,MAAL,CAAY,UAAAhE,GAAG,EAAI;EACxB,WAAO4P,GAAG,CAACzO,IAAJ,CAAS,UAAAxB,EAAE,EAAI;EACpB,UAAMmQ,QAAQ,GAAG9P,GAAG,CAAC4M,MAAJ,CAAWjN,EAAX,CAAjB;EACA,aAAOmQ,QAAQ,KAAKxE,SAAb,GACHnG,MAAM,CAAC2K,QAAD,CAAN,KAAqB3K,MAAM,CAAC0K,WAAD,CADxB,GAEH,IAFJ;EAGD,KALM,CAAP;EAMD,GAPM,CAAP;EAQD,CATM;;EAWPI,aAAa,CAAC5L,UAAd,GAA2B,UAAA9B,GAAG;EAAA,SAAI,CAACA,GAAL;EAAA,CAA9B;;AAEA,EAAO,IAAMtD,QAAQ,GAAG,SAAXA,QAAW,CAACmE,IAAD,EAAOwM,GAAP,EAAYC,WAAZ,EAA4B;EAClD,SAAOzM,IAAI,CAACY,MAAL,CAAY,UAAAhE,GAAG,EAAI;EACxB,WAAO4P,GAAG,CAACzO,IAAJ,CAAS,UAAAxB,EAAE,EAAI;EACpB,UAAMmQ,QAAQ,GAAG9P,GAAG,CAAC4M,MAAJ,CAAWjN,EAAX,CAAjB;EACA,aAAOmQ,QAAQ,CAAC7Q,QAAT,CAAkB4Q,WAAlB,CAAP;EACD,KAHM,CAAP;EAID,GALM,CAAP;EAMD,CAPM;;EASP5Q,QAAQ,CAACoF,UAAT,GAAsB,UAAA9B,GAAG;EAAA,SAAI,CAACA,GAAD,IAAQ,CAACA,GAAG,CAACZ,MAAjB;EAAA,CAAzB;;AAEA,EAAO,IAAMuO,WAAW,GAAG,SAAdA,WAAc,CAAC9M,IAAD,EAAOwM,GAAP,EAAYC,WAAZ,EAA4B;EACrD,SAAOzM,IAAI,CAACY,MAAL,CAAY,UAAAhE,GAAG,EAAI;EACxB,WAAO4P,GAAG,CAACzO,IAAJ,CAAS,UAAAxB,EAAE,EAAI;EACpB,UAAMmQ,QAAQ,GAAG9P,GAAG,CAAC4M,MAAJ,CAAWjN,EAAX,CAAjB;EACA,aACEmQ,QAAQ,IACRA,QAAQ,CAACnO,MADT,IAEAkO,WAAW,CAACM,KAAZ,CAAkB,UAAA5N,GAAG;EAAA,eAAIuN,QAAQ,CAAC7Q,QAAT,CAAkBsD,GAAlB,CAAJ;EAAA,OAArB,CAHF;EAKD,KAPM,CAAP;EAQD,GATM,CAAP;EAUD,CAXM;;EAaP2N,WAAW,CAAC7L,UAAZ,GAAyB,UAAA9B,GAAG;EAAA,SAAI,CAACA,GAAD,IAAQ,CAACA,GAAG,CAACZ,MAAjB;EAAA,CAA5B;;AAEA,EAAO,IAAMyO,YAAY,GAAG,SAAfA,YAAe,CAAChN,IAAD,EAAOwM,GAAP,EAAYC,WAAZ,EAA4B;EACtD,SAAOzM,IAAI,CAACY,MAAL,CAAY,UAAAhE,GAAG,EAAI;EACxB,WAAO4P,GAAG,CAACzO,IAAJ,CAAS,UAAAxB,EAAE,EAAI;EACpB,UAAMmQ,QAAQ,GAAG9P,GAAG,CAAC4M,MAAJ,CAAWjN,EAAX,CAAjB;EACA,aACEmQ,QAAQ,IACRA,QAAQ,CAACnO,MADT,IAEAkO,WAAW,CAAC1O,IAAZ,CAAiB,UAAAoB,GAAG;EAAA,eAAIuN,QAAQ,CAAC7Q,QAAT,CAAkBsD,GAAlB,CAAJ;EAAA,OAApB,CAHF;EAKD,KAPM,CAAP;EAQD,GATM,CAAP;EAUD,CAXM;;EAaP6N,YAAY,CAAC/L,UAAb,GAA0B,UAAA9B,GAAG;EAAA,SAAI,CAACA,GAAD,IAAQ,CAACA,GAAG,CAACZ,MAAjB;EAAA,CAA7B;;AAEA,EAAO,IAAM0O,aAAa,GAAG,SAAhBA,aAAgB,CAACjN,IAAD,EAAOwM,GAAP,EAAYC,WAAZ,EAA4B;EACvD,SAAOzM,IAAI,CAACY,MAAL,CAAY,UAAAhE,GAAG,EAAI;EACxB,WAAO4P,GAAG,CAACzO,IAAJ,CAAS,UAAAxB,EAAE,EAAI;EACpB,UAAMmQ,QAAQ,GAAG9P,GAAG,CAAC4M,MAAJ,CAAWjN,EAAX,CAAjB;EACA,aAAOkQ,WAAW,CAAC5Q,QAAZ,CAAqB6Q,QAArB,CAAP;EACD,KAHM,CAAP;EAID,GALM,CAAP;EAMD,CAPM;;EASPO,aAAa,CAAChM,UAAd,GAA2B,UAAA9B,GAAG;EAAA,SAAI,CAACA,GAAD,IAAQ,CAACA,GAAG,CAACZ,MAAjB;EAAA,CAA9B;;AAEA,EAAO,IAAM2O,KAAK,GAAG,SAARA,KAAQ,CAAClN,IAAD,EAAOwM,GAAP,EAAYC,WAAZ,EAA4B;EAC/C,SAAOzM,IAAI,CAACY,MAAL,CAAY,UAAAhE,GAAG,EAAI;EACxB,WAAO4P,GAAG,CAACzO,IAAJ,CAAS,UAAAxB,EAAE,EAAI;EACpB,UAAMmQ,QAAQ,GAAG9P,GAAG,CAAC4M,MAAJ,CAAWjN,EAAX,CAAjB;EACA,aAAOmQ,QAAQ,KAAKD,WAApB;EACD,KAHM,CAAP;EAID,GALM,CAAP;EAMD,CAPM;;EASPS,KAAK,CAACjM,UAAN,GAAmB,UAAA9B,GAAG;EAAA,SAAI,OAAOA,GAAP,KAAe,WAAnB;EAAA,CAAtB;;AAEA,EAAO,IAAMgO,MAAM,GAAG,SAATA,MAAS,CAACnN,IAAD,EAAOwM,GAAP,EAAYC,WAAZ,EAA4B;EAChD,SAAOzM,IAAI,CAACY,MAAL,CAAY,UAAAhE,GAAG,EAAI;EACxB,WAAO4P,GAAG,CAACzO,IAAJ,CAAS,UAAAxB,EAAE,EAAI;EACpB,UAAMmQ,QAAQ,GAAG9P,GAAG,CAAC4M,MAAJ,CAAWjN,EAAX,CAAjB,CADoB;;EAGpB,aAAOmQ,QAAQ,IAAID,WAAnB;EACD,KAJM,CAAP;EAKD,GANM,CAAP;EAOD,CARM;;EAUPU,MAAM,CAAClM,UAAP,GAAoB,UAAA9B,GAAG;EAAA,SAAIA,GAAG,IAAI,IAAX;EAAA,CAAvB;;AAEA,EAAO,IAAMiO,OAAO,GAAG,SAAVA,OAAU,CAACpN,IAAD,EAAOwM,GAAP,EAAYC,WAAZ,EAA4B;EAAA,aAChCA,WAAW,IAAI,EADiB;EAAA,MAC5ClC,GAD4C;EAAA,MACvCC,GADuC;;EAGjDD,EAAAA,GAAG,GAAG,OAAOA,GAAP,KAAe,QAAf,GAA0BA,GAA1B,GAAgC,CAAC8C,QAAvC;EACA7C,EAAAA,GAAG,GAAG,OAAOA,GAAP,KAAe,QAAf,GAA0BA,GAA1B,GAAgC6C,QAAtC;;EAEA,MAAI9C,GAAG,GAAGC,GAAV,EAAe;EACb,QAAM8C,IAAI,GAAG/C,GAAb;EACAA,IAAAA,GAAG,GAAGC,GAAN;EACAA,IAAAA,GAAG,GAAG8C,IAAN;EACD;;EAED,SAAOtN,IAAI,CAACY,MAAL,CAAY,UAAAhE,GAAG,EAAI;EACxB,WAAO4P,GAAG,CAACzO,IAAJ,CAAS,UAAAxB,EAAE,EAAI;EACpB,UAAMmQ,QAAQ,GAAG9P,GAAG,CAAC4M,MAAJ,CAAWjN,EAAX,CAAjB;EACA,aAAOmQ,QAAQ,IAAInC,GAAZ,IAAmBmC,QAAQ,IAAIlC,GAAtC;EACD,KAHM,CAAP;EAID,GALM,CAAP;EAMD,CAlBM;;EAoBP4C,OAAO,CAACnM,UAAR,GAAqB,UAAA9B,GAAG;EAAA,SACtB,CAACA,GAAD,IAAS,OAAOA,GAAG,CAAC,CAAD,CAAV,KAAkB,QAAlB,IAA8B,OAAOA,GAAG,CAAC,CAAD,CAAV,KAAkB,QADnC;EAAA,CAAxB;;;;;;;;;;;;;;;;ECrHAvJ,OAAO,CAAC2X,YAAR,GAAuB,cAAvB;EACA3X,OAAO,CAAC4X,SAAR,GAAoB,WAApB;EACA5X,OAAO,CAAC6X,aAAR,GAAwB,eAAxB;AAEA,MAAaC,UAAU,GAAG,SAAbA,UAAa,CAAAnW,KAAK,EAAI;EACjCA,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,aAAvB;EACD,CAHM;EAKP4J,UAAU,CAACnV,UAAX,GAAwB,YAAxB;;EAEA,SAAS2M,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EACvD,MAAI8K,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACE8X,MAAAA,OAAO,EAAE;EADX,OAEKxI,KAFL;EAID;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC2X,YAA5B,EAA0C;EACxC,wBACKpI,KADL;EAEEwI,MAAAA,OAAO,EAAE5S,QAAQ,CAACgL,YAAT,CAAsB4H,OAAtB,IAAiC;EAF5C;EAID;;EAED,MAAI9H,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC4X,SAA5B,EAAuC;EAAA,QAC7BvH,QAD6B,GACHJ,MADG,CAC7BI,QAD6B;EAAA,QACnBwG,WADmB,GACH5G,MADG,CACnB4G,WADmB;EAAA,QAE7BpP,UAF6B,GAEgBtC,QAFhB,CAE7BsC,UAF6B;EAAA,QAEJwD,eAFI,GAEgB9F,QAFhB,CAEjB+F,WAFiB;EAIrC,QAAM9F,MAAM,GAAGqC,UAAU,CAACuL,IAAX,CAAgB,UAAA5K,CAAC;EAAA,aAAIA,CAAC,CAACzB,EAAF,KAAS0J,QAAb;EAAA,KAAjB,CAAf;;EAEA,QAAI,CAACjL,MAAL,EAAa;EACX,YAAM,IAAIjD,KAAJ,oDAC6CkO,QAD7C,CAAN;EAGD;;EAED,QAAM2H,YAAY,GAAGjN,eAAe,CAClC3F,MAAM,CAAC4F,MAD2B,EAElCC,eAAe,IAAI,EAFe,EAGlCC,WAHkC,CAApC;EAMA,QAAM+M,cAAc,GAAG1I,KAAK,CAACwI,OAAN,CAAc/E,IAAd,CAAmB,UAAA5K,CAAC;EAAA,aAAIA,CAAC,CAACzB,EAAF,KAAS0J,QAAb;EAAA,KAApB,CAAvB;EAEA,QAAM6H,SAAS,GAAGhV,gBAAgB,CAChC2T,WADgC,EAEhCoB,cAAc,IAAIA,cAAc,CAAC9X,KAFD,CAAlC,CApBqC;;EA0BrC,QAAIiL,sBAAsB,CAAC4M,YAAY,CAAC3M,UAAd,EAA0B6M,SAA1B,EAAqC9S,MAArC,CAA1B,EAAwE;EACtE,0BACKmK,KADL;EAEEwI,QAAAA,OAAO,EAAExI,KAAK,CAACwI,OAAN,CAAc/M,MAAd,CAAqB,UAAA5C,CAAC;EAAA,iBAAIA,CAAC,CAACzB,EAAF,KAAS0J,QAAb;EAAA,SAAtB;EAFX;EAID;;EAED,QAAI4H,cAAJ,EAAoB;EAClB,0BACK1I,KADL;EAEEwI,QAAAA,OAAO,EAAExI,KAAK,CAACwI,OAAN,CAAcxR,GAAd,CAAkB,UAAA6B,CAAC,EAAI;EAC9B,cAAIA,CAAC,CAACzB,EAAF,KAAS0J,QAAb,EAAuB;EACrB,mBAAO;EAAE1J,cAAAA,EAAE,EAAE0J,QAAN;EAAgBlQ,cAAAA,KAAK,EAAE+X;EAAvB,aAAP;EACD;;EACD,iBAAO9P,CAAP;EACD,SALQ;EAFX;EASD;;EAED,wBACKmH,KADL;EAEEwI,MAAAA,OAAO,YAAMxI,KAAK,CAACwI,OAAZ,GAAqB;EAAEpR,QAAAA,EAAE,EAAE0J,QAAN;EAAgBlQ,QAAAA,KAAK,EAAE+X;EAAvB,OAArB;EAFT;EAID;;EAED,MAAIjI,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC6X,aAA5B,EAA2C;EAAA,QACjCE,OADiC,GACrB9H,MADqB,CACjC8H,OADiC;EAAA,QAEjCtQ,WAFiC,GAEYtC,QAFZ,CAEjCsC,UAFiC;EAAA,QAERwD,gBAFQ,GAEY9F,QAFZ,CAErB+F,WAFqB;EAIzC,wBACKqE,KADL;EAEE;EACAwI,MAAAA,OAAO,EAAE7U,gBAAgB,CAAC6U,OAAD,EAAUxI,KAAK,CAACwI,OAAhB,CAAhB,CAAyC/M,MAAzC,CAAgD,UAAAA,MAAM,EAAI;EACjE,YAAM5F,MAAM,GAAGqC,WAAU,CAACuL,IAAX,CAAgB,UAAA5K,CAAC;EAAA,iBAAIA,CAAC,CAACzB,EAAF,KAASqE,MAAM,CAACrE,EAApB;EAAA,SAAjB,CAAf;;EACA,YAAMqR,YAAY,GAAGjN,eAAe,CAClC3F,MAAM,CAAC4F,MAD2B,EAElCC,gBAAe,IAAI,EAFe,EAGlCC,WAHkC,CAApC;;EAMA,YACEE,sBAAsB,CAAC4M,YAAY,CAAC3M,UAAd,EAA0BL,MAAM,CAAC7K,KAAjC,EAAwCiF,MAAxC,CADxB,EAEE;EACA,iBAAO,KAAP;EACD;;EACD,eAAO,IAAP;EACD,OAdQ;EAHX;EAmBD;EACF;;EAED,SAAS8I,aAAT,CAAqB/I,QAArB,EAA+B;EAAA,MAE3B6M,IAF2B,GAczB7M,QAdyB,CAE3B6M,IAF2B;EAAA,MAG3B5H,IAH2B,GAczBjF,QAdyB,CAG3BiF,IAH2B;EAAA,MAI3BqI,QAJ2B,GAczBtN,QAdyB,CAI3BsN,QAJ2B;EAAA,MAK3BC,QAL2B,GAczBvN,QAdyB,CAK3BuN,QAL2B;EAAA,MAM3BjL,UAN2B,GAczBtC,QAdyB,CAM3BsC,UAN2B;EAAA,MAOdwD,eAPc,GAczB9F,QAdyB,CAO3B+F,WAP2B;EAAA,MAQ3BiN,aAR2B,GAczBhT,QAdyB,CAQ3BgT,aAR2B;EAAA,8BAczBhT,QAdyB,CAS3BiT,gBAT2B;EAAA,MAS3BA,gBAT2B,sCASR,KATQ;EAAA,MAU3BC,cAV2B,GAczBlT,QAdyB,CAU3BkT,cAV2B;EAAA,MAWlBN,OAXkB,GAczB5S,QAdyB,CAW3BoK,KAX2B,CAWlBwI,OAXkB;EAAA,MAY3BlH,QAZ2B,GAczB1L,QAdyB,CAY3B0L,QAZ2B;EAAA,8BAczB1L,QAdyB,CAa3BmT,gBAb2B;EAAA,MAa3BA,gBAb2B,sCAaR,IAbQ;EAgB7B,MAAMV,SAAS,GAAGpU,KAAK,CAACG,WAAN,CAChB,UAAC0M,QAAD,EAAWwG,WAAX,EAA2B;EACzBhG,IAAAA,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAAC4X,SAAhB;EAA2BvH,MAAAA,QAAQ,EAARA,QAA3B;EAAqCwG,MAAAA,WAAW,EAAXA;EAArC,KAAD,CAAR;EACD,GAHe,EAIhB,CAAChG,QAAD,CAJgB,CAAlB;EAOA,MAAMgH,aAAa,GAAGrU,KAAK,CAACG,WAAN,CACpB,UAAAoU,OAAO,EAAI;EACTlH,IAAAA,QAAQ,CAAC;EACPxL,MAAAA,IAAI,EAAErF,OAAO,CAAC6X,aADP;EAEPE,MAAAA,OAAO,EAAPA;EAFO,KAAD,CAAR;EAID,GANmB,EAOpB,CAAClH,QAAD,CAPoB,CAAtB;EAUApJ,EAAAA,UAAU,CAACnF,OAAX,CAAmB,UAAA8C,MAAM,EAAI;EAAA,QAEzBuB,EAFyB,GAMvBvB,MANuB,CAEzBuB,EAFyB;EAAA,QAGzBC,QAHyB,GAMvBxB,MANuB,CAGzBwB,QAHyB;EAAA,QAIP2R,sBAJO,GAMvBnT,MANuB,CAIzBgT,gBAJyB;EAAA,QAKTI,oBALS,GAMvBpT,MANuB,CAKzBiT,cALyB;;EAS3BjT,IAAAA,MAAM,CAACqT,SAAP,GAAmB7R,QAAQ,GACvB+C,eAAe,CACb6O,oBAAoB,KAAK,IAAzB,GAAgC,KAAhC,GAAwClG,SAD3B,EAEb+F,cAAc,KAAK,IAAnB,GAA0B,KAA1B,GAAkC/F,SAFrB,EAGb,IAHa,CADQ,GAMvB3I,eAAe,CAAC4O,sBAAD,EAAyBH,gBAAzB,EAA2C,KAA3C,CANnB,CAT2B;;EAkB3BhT,IAAAA,MAAM,CAACwS,SAAP,GAAmB,UAAArO,GAAG;EAAA,aAAIqO,SAAS,CAACxS,MAAM,CAACuB,EAAR,EAAY4C,GAAZ,CAAb;EAAA,KAAtB,CAlB2B;EAqB3B;;;EACA,QAAMmP,KAAK,GAAGX,OAAO,CAAC/E,IAAR,CAAa,UAAA5K,CAAC;EAAA,aAAIA,CAAC,CAACzB,EAAF,KAASA,EAAb;EAAA,KAAd,CAAd;EACAvB,IAAAA,MAAM,CAACyR,WAAP,GAAqB6B,KAAK,IAAIA,KAAK,CAACvY,KAApC;EACD,GAxBD;;EAjC6B,uBA+DzBqD,KAAK,CAACgP,OAAN,CAAc,YAAM;EACtB,QAAI2F,aAAa,IAAI,CAACJ,OAAO,CAACpP,MAA9B,EAAsC;EACpC,aAAO,CAACyB,IAAD,EAAOqI,QAAP,EAAiBC,QAAjB,CAAP;EACD;;EAED,QAAMiG,gBAAgB,GAAG,EAAzB;EACA,QAAMC,gBAAgB,GAAG,EAAzB,CANsB;;EAStB,QAAMC,UAAU,GAAG,SAAbA,UAAa,CAACzO,IAAD,EAAO9D,KAAP,EAAqB;EAAA,UAAdA,KAAc;EAAdA,QAAAA,KAAc,GAAN,CAAM;EAAA;;EACtC,UAAIwS,YAAY,GAAG1O,IAAnB;EAEA0O,MAAAA,YAAY,GAAGf,OAAO,CAACjX,MAAR,CACb,UAACiY,aAAD,QAAyD;EAAA,YAAnC1I,QAAmC,QAAvC1J,EAAuC;EAAA,YAAlBkQ,WAAkB,QAAzB1W,KAAyB;EACvD;EACA,YAAMiF,MAAM,GAAGqC,UAAU,CAACuL,IAAX,CAAgB,UAAA5K,CAAC;EAAA,iBAAIA,CAAC,CAACzB,EAAF,KAAS0J,QAAb;EAAA,SAAjB,CAAf;;EAEA,YAAI,CAACjL,MAAL,EAAa;EACX,iBAAO2T,aAAP;EACD;;EAED,YAAIzS,KAAK,KAAK,CAAd,EAAiB;EACflB,UAAAA,MAAM,CAAC4T,eAAP,GAAyBD,aAAzB;EACD;;EAED,YAAMf,YAAY,GAAGjN,eAAe,CAClC3F,MAAM,CAAC4F,MAD2B,EAElCC,eAAe,IAAI,EAFe,EAGlCC,WAHkC,CAApC;;EAMA,YAAI,CAAC8M,YAAL,EAAmB;EACjB/V,UAAAA,OAAO,CAACgX,IAAR,qEACoE7T,MAAM,CAACuB,EAD3E;EAGA,iBAAOoS,aAAP;EACD,SAvBsD;EA0BvD;;;EACA3T,QAAAA,MAAM,CAAC0T,YAAP,GAAsBd,YAAY,CAChCe,aADgC,EAEhC,CAAC1I,QAAD,CAFgC,EAGhCwG,WAHgC,CAAlC;EAMA,eAAOzR,MAAM,CAAC0T,YAAd;EACD,OAnCY,EAoCb1O,IApCa,CAAf,CAHsC;EA2CtC;EACA;EACA;;EACA0O,MAAAA,YAAY,CAACxW,OAAb,CAAqB,UAAA0E,GAAG,EAAI;EAC1B2R,QAAAA,gBAAgB,CAACjQ,IAAjB,CAAsB1B,GAAtB;EACA4R,QAAAA,gBAAgB,CAAC5R,GAAG,CAACL,EAAL,CAAhB,GAA2BK,GAA3B;;EACA,YAAI,CAACA,GAAG,CAAC8D,OAAT,EAAkB;EAChB;EACD;;EAED9D,QAAAA,GAAG,CAAC8D,OAAJ,GACE9D,GAAG,CAAC8D,OAAJ,IAAe9D,GAAG,CAAC8D,OAAJ,CAAYnC,MAAZ,GAAqB,CAApC,GACIkQ,UAAU,CAAC7R,GAAG,CAAC8D,OAAL,EAAcxE,KAAK,GAAG,CAAtB,CADd,GAEIU,GAAG,CAAC8D,OAHV;EAID,OAXD;EAaA,aAAOgO,YAAP;EACD,KA5DD;;EA8DA,WAAO,CAACD,UAAU,CAACzO,IAAD,CAAX,EAAmBuO,gBAAnB,EAAqCC,gBAArC,CAAP;EACD,GAxEG,EAwED,CACDT,aADC,EAEDJ,OAFC,EAGD3N,IAHC,EAIDqI,QAJC,EAKDC,QALC,EAMDjL,UANC,EAODwD,eAPC,CAxEC,CA/DyB;EAAA,MA4D3B6N,YA5D2B;EAAA,MA6D3BH,gBA7D2B;EAAA,MA8D3BC,gBA9D2B;;EAiJ7BpV,EAAAA,KAAK,CAACgP,OAAN,CAAc,YAAM;EAClB;EACA;EACA,QAAM0G,kBAAkB,GAAGzR,UAAU,CAACuD,MAAX,CACzB,UAAA5F,MAAM;EAAA,aAAI,CAAC2S,OAAO,CAAC/E,IAAR,CAAa,UAAA5K,CAAC;EAAA,eAAIA,CAAC,CAACzB,EAAF,KAASvB,MAAM,CAACuB,EAApB;EAAA,OAAd,CAAL;EAAA,KADmB,CAA3B,CAHkB;EAQlB;;EACAuS,IAAAA,kBAAkB,CAAC5W,OAAnB,CAA2B,UAAA8C,MAAM,EAAI;EACnCA,MAAAA,MAAM,CAAC4T,eAAP,GAAyBF,YAAzB;EACA1T,MAAAA,MAAM,CAAC0T,YAAP,GAAsBA,YAAtB;EACD,KAHD;EAID,GAbD,EAaG,CAACA,YAAD,EAAef,OAAf,EAAwBtQ,UAAxB,CAbH;EAeA,MAAM0R,mBAAmB,GAAG9V,YAAY,CAACiV,gBAAD,CAAxC;EAEAtU,EAAAA,sBAAsB,CAAC,YAAM;EAC3B,QAAImV,mBAAmB,EAAvB,EAA2B;EACzBtI,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAAC2X;EAAhB,OAAD,CAAR;EACD;EACF,GAJqB,EAInB,CAAC9G,QAAD,EAAWsH,aAAa,GAAG,IAAH,GAAUnG,IAAlC,CAJmB,CAAtB;EAMAnM,EAAAA,MAAM,CAACsB,MAAP,CAAchC,QAAd,EAAwB;EACtB6T,IAAAA,eAAe,EAAE5O,IADK;EAEtBgP,IAAAA,mBAAmB,EAAE3G,QAFC;EAGtB4G,IAAAA,mBAAmB,EAAE3G,QAHC;EAItBoG,IAAAA,YAAY,EAAZA,YAJsB;EAKtBH,IAAAA,gBAAgB,EAAhBA,gBALsB;EAMtBC,IAAAA,gBAAgB,EAAhBA,gBANsB;EAOtBxO,IAAAA,IAAI,EAAE0O,YAPgB;EAQtBrG,IAAAA,QAAQ,EAAEkG,gBARY;EAStBjG,IAAAA,QAAQ,EAAEkG,gBATY;EAUtBhB,IAAAA,SAAS,EAATA,SAVsB;EAWtBC,IAAAA,aAAa,EAAbA;EAXsB,GAAxB;EAaD;;EC5RD7X,OAAO,CAACsZ,iBAAR,GAA4B,mBAA5B;EACAtZ,OAAO,CAACuZ,eAAR,GAA0B,iBAA1B;AAEA,MAAaC,eAAe,GAAG,SAAlBA,eAAkB,CAAA7X,KAAK,EAAI;EACtCA,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,aAAvB;EACD,CAHM;EAKPsL,eAAe,CAAC7W,UAAhB,GAA6B,iBAA7B;;EAEA,SAAS2M,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EACvD,MAAI8K,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACsZ,iBAA5B,EAA+C;EAC7C,wBACK/J,KADL;EAEEkK,MAAAA,YAAY,EAAEtU,QAAQ,CAACgL,YAAT,CAAsBsJ,YAAtB,IAAsCnH;EAFtD;EAID;;EAED,MAAIrC,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACuZ,eAA5B,EAA6C;EAAA,QACnC1C,WADmC,GACnB5G,MADmB,CACnC4G,WADmC;EAAA,QAEnC5L,eAFmC,GAEf9F,QAFe,CAEnC8F,eAFmC;EAI3C,QAAM+M,YAAY,GAAGjN,eAAe,CAClC5F,QAAQ,CAACsU,YADyB,EAElCxO,eAAe,IAAI,EAFe,EAGlCC,WAHkC,CAApC;EAMA,QAAMgN,SAAS,GAAGhV,gBAAgB,CAAC2T,WAAD,EAActH,KAAK,CAACkK,YAApB,CAAlC,CAV2C;;EAa3C,QAAIrO,sBAAsB,CAAC4M,YAAY,CAAC3M,UAAd,EAA0B6M,SAA1B,CAA1B,EAAgE;EAAA,UACtDuB,YADsD,GACRlK,KADQ,CACtDkK,YADsD;EAAA,UACrCC,wBADqC,iCACRnK,KADQ;;EAE9D,aAAOmK,wBAAP;EACD;;EAED,wBACKnK,KADL;EAEEkK,MAAAA,YAAY,EAAEvB;EAFhB;EAID;EACF;;EAED,SAAShK,aAAT,CAAqB/I,QAArB,EAA+B;EAAA,MAE3B6M,IAF2B,GAczB7M,QAdyB,CAE3B6M,IAF2B;EAAA,MAG3B5H,IAH2B,GAczBjF,QAdyB,CAG3BiF,IAH2B;EAAA,MAI3BqI,QAJ2B,GAczBtN,QAdyB,CAI3BsN,QAJ2B;EAAA,MAK3BC,QAL2B,GAczBvN,QAdyB,CAK3BuN,QAL2B;EAAA,MAM3BjL,UAN2B,GAczBtC,QAdyB,CAM3BsC,UAN2B;EAAA,MAOdwD,eAPc,GAczB9F,QAdyB,CAO3B+F,WAP2B;EAAA,MAQ3BuO,YAR2B,GAczBtU,QAdyB,CAQ3BsU,YAR2B;EAAA,MAS3BE,kBAT2B,GAczBxU,QAdyB,CAS3BwU,kBAT2B;EAAA,MAUJC,iBAVI,GAczBzU,QAdyB,CAU3BoK,KAV2B,CAUlBkK,YAVkB;EAAA,MAW3B5I,QAX2B,GAczB1L,QAdyB,CAW3B0L,QAX2B;EAAA,8BAczB1L,QAdyB,CAY3B0U,qBAZ2B;EAAA,MAY3BA,qBAZ2B,sCAYH,IAZG;EAAA,MAa3BC,mBAb2B,GAczB3U,QAdyB,CAa3B2U,mBAb2B;EAgB7B,MAAMP,eAAe,GAAG/V,KAAK,CAACG,WAAN,CACtB,UAAAkT,WAAW,EAAI;EACbhG,IAAAA,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACuZ,eAAhB;EAAiC1C,MAAAA,WAAW,EAAXA;EAAjC,KAAD,CAAR;EACD,GAHqB,EAItB,CAAChG,QAAD,CAJsB,CAAxB,CAhB6B;EAwB7B;EACA;EACA;;EA1B6B,uBAgCzBrN,KAAK,CAACgP,OAAN,CAAc,YAAM;EACtB,QAAImH,kBAAkB,IAAI,OAAOC,iBAAP,KAA6B,WAAvD,EAAoE;EAClE,aAAO,CAACxP,IAAD,EAAOqI,QAAP,EAAiBC,QAAjB,CAAP;EACD;;EAED,QAAMiG,gBAAgB,GAAG,EAAzB;EACA,QAAMC,gBAAgB,GAAG,EAAzB;EAEA,QAAMZ,YAAY,GAAGjN,eAAe,CAClC0O,YADkC,EAElCxO,eAAe,IAAI,EAFe,EAGlCC,WAHkC,CAApC;;EAMA,QAAI,CAAC8M,YAAL,EAAmB;EACjB/V,MAAAA,OAAO,CAACgX,IAAR;EACA,aAAO7O,IAAP;EACD;;EAED3C,IAAAA,UAAU,CAACnF,OAAX,CAAmB,UAAA8C,MAAM,EAAI;EAAA,UACE2U,yBADF,GACgC3U,MADhC,CACnB0U,mBADmB;EAG3B1U,MAAAA,MAAM,CAACqT,SAAP,GAAmB9O,eAAe,CAChCoQ,yBAAyB,KAAK,IAA9B,GAAqC,KAArC,GAA6CzH,SADb,EAEhCwH,mBAAmB,KAAK,IAAxB,GAA+B,KAA/B,GAAuCxH,SAFP,EAGhC,IAHgC,CAAlC;EAKD,KARD;EAUA,QAAM0H,iBAAiB,GAAGvS,UAAU,CAACuD,MAAX,CAAkB,UAAAiP,CAAC;EAAA,aAAIA,CAAC,CAACxB,SAAF,KAAgB,IAApB;EAAA,KAAnB,CAA1B,CA7BsB;;EAgCtB,QAAMI,UAAU,GAAG,SAAbA,UAAa,CAAAC,YAAY,EAAI;EACjCA,MAAAA,YAAY,GAAGd,YAAY,CACzBc,YADyB,EAEzBkB,iBAAiB,CAACzT,GAAlB,CAAsB,UAAA6B,CAAC;EAAA,eAAIA,CAAC,CAACzB,EAAN;EAAA,OAAvB,CAFyB,EAGzBiT,iBAHyB,CAA3B;EAMAd,MAAAA,YAAY,CAACxW,OAAb,CAAqB,UAAA0E,GAAG,EAAI;EAC1B2R,QAAAA,gBAAgB,CAACjQ,IAAjB,CAAsB1B,GAAtB;EACA4R,QAAAA,gBAAgB,CAAC5R,GAAG,CAACL,EAAL,CAAhB,GAA2BK,GAA3B;EAEAA,QAAAA,GAAG,CAAC8D,OAAJ,GACE9D,GAAG,CAAC8D,OAAJ,IAAe9D,GAAG,CAAC8D,OAAJ,CAAYnC,MAA3B,GACIkQ,UAAU,CAAC7R,GAAG,CAAC8D,OAAL,CADd,GAEI9D,GAAG,CAAC8D,OAHV;EAID,OARD;EAUA,aAAOgO,YAAP;EACD,KAlBD;;EAoBA,WAAO,CAACD,UAAU,CAACzO,IAAD,CAAX,EAAmBuO,gBAAnB,EAAqCC,gBAArC,CAAP;EACD,GArDG,EAqDD,CACDe,kBADC,EAEDC,iBAFC,EAGDH,YAHC,EAIDxO,eAJC,EAKDxD,UALC,EAMD2C,IANC,EAODqI,QAPC,EAQDC,QARC,EASDoH,mBATC,CArDC,CAhCyB;EAAA,MA6B3BI,kBA7B2B;EAAA,MA8B3BC,sBA9B2B;EAAA,MA+B3BC,sBA/B2B;;EAiG7B,MAAMC,wBAAwB,GAAGhX,YAAY,CAACwW,qBAAD,CAA7C;EAEA7V,EAAAA,sBAAsB,CAAC,YAAM;EAC3B,QAAIqW,wBAAwB,EAA5B,EAAgC;EAC9BxJ,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAACsZ;EAAhB,OAAD,CAAR;EACD;EACF,GAJqB,EAInB,CAACzI,QAAD,EAAW8I,kBAAkB,GAAG,IAAH,GAAU3H,IAAvC,CAJmB,CAAtB;EAMAnM,EAAAA,MAAM,CAACsB,MAAP,CAAchC,QAAd,EAAwB;EACtBmV,IAAAA,qBAAqB,EAAElQ,IADD;EAEtBmQ,IAAAA,yBAAyB,EAAE9H,QAFL;EAGtB+H,IAAAA,yBAAyB,EAAE9H,QAHL;EAItBwH,IAAAA,kBAAkB,EAAlBA,kBAJsB;EAKtBC,IAAAA,sBAAsB,EAAtBA,sBALsB;EAMtBC,IAAAA,sBAAsB,EAAtBA,sBANsB;EAOtBhQ,IAAAA,IAAI,EAAE8P,kBAPgB;EAQtBzH,IAAAA,QAAQ,EAAE0H,sBARY;EAStBzH,IAAAA,QAAQ,EAAE0H,sBATY;EAUtBb,IAAAA,eAAe,EAAfA,eAVsB;EAWtBO,IAAAA,mBAAmB,EAAnBA;EAXsB,GAAxB;EAaD;;ECnLM,SAASW,GAAT,CAAa7G,MAAb,EAAqB8G,gBAArB,EAAuC;EAC5C;EACA;EACA,SAAOA,gBAAgB,CAAC5Z,MAAjB,CACL,UAAC2Z,GAAD,EAAMzZ,IAAN;EAAA,WAAeyZ,GAAG,IAAI,OAAOzZ,IAAP,KAAgB,QAAhB,GAA2BA,IAA3B,GAAkC,CAAtC,CAAlB;EAAA,GADK,EAEL,CAFK,CAAP;EAID;AAED,EAAO,SAAS2T,GAAT,CAAaf,MAAb,EAAqB;EAC1B,MAAIe,GAAG,GAAGf,MAAM,CAAC,CAAD,CAAN,IAAa,CAAvB;EAEAA,EAAAA,MAAM,CAACtR,OAAP,CAAe,UAAAnC,KAAK,EAAI;EACtB,QAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;EAC7BwU,MAAAA,GAAG,GAAGD,IAAI,CAACC,GAAL,CAASA,GAAT,EAAcxU,KAAd,CAAN;EACD;EACF,GAJD;EAMA,SAAOwU,GAAP;EACD;AAED,EAAO,SAASC,GAAT,CAAahB,MAAb,EAAqB;EAC1B,MAAIgB,GAAG,GAAGhB,MAAM,CAAC,CAAD,CAAN,IAAa,CAAvB;EAEAA,EAAAA,MAAM,CAACtR,OAAP,CAAe,UAAAnC,KAAK,EAAI;EACtB,QAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;EAC7ByU,MAAAA,GAAG,GAAGF,IAAI,CAACE,GAAL,CAASA,GAAT,EAAczU,KAAd,CAAN;EACD;EACF,GAJD;EAMA,SAAOyU,GAAP;EACD;AAED,EAAO,SAAS+F,MAAT,CAAgB/G,MAAhB,EAAwB;EAC7B,MAAIe,GAAG,GAAGf,MAAM,CAAC,CAAD,CAAN,IAAa,CAAvB;EACA,MAAIgB,GAAG,GAAGhB,MAAM,CAAC,CAAD,CAAN,IAAa,CAAvB;EAEAA,EAAAA,MAAM,CAACtR,OAAP,CAAe,UAAAnC,KAAK,EAAI;EACtB,QAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;EAC7BwU,MAAAA,GAAG,GAAGD,IAAI,CAACC,GAAL,CAASA,GAAT,EAAcxU,KAAd,CAAN;EACAyU,MAAAA,GAAG,GAAGF,IAAI,CAACE,GAAL,CAASA,GAAT,EAAczU,KAAd,CAAN;EACD;EACF,GALD;EAOA,SAAUwU,GAAV,UAAkBC,GAAlB;EACD;AAED,EAAO,SAASgG,OAAT,CAAiBhH,MAAjB,EAAyB;EAC9B,SAAO6G,GAAG,CAAC,IAAD,EAAO7G,MAAP,CAAH,GAAoBA,MAAM,CAACjL,MAAlC;EACD;AAED,EAAO,SAASkS,MAAT,CAAgBjH,MAAhB,EAAwB;EAC7B,MAAI,CAACA,MAAM,CAACjL,MAAZ,EAAoB;EAClB,WAAO,IAAP;EACD;;EAED,MAAMmS,GAAG,GAAGpG,IAAI,CAACqG,KAAL,CAAWnH,MAAM,CAACjL,MAAP,GAAgB,CAA3B,CAAZ;EACA,MAAMqS,IAAI,GAAG,UAAIpH,MAAJ,EAAYR,IAAZ,CAAiB,UAACtJ,CAAD,EAAImR,CAAJ;EAAA,WAAUnR,CAAC,GAAGmR,CAAd;EAAA,GAAjB,CAAb;EACA,SAAOrH,MAAM,CAACjL,MAAP,GAAgB,CAAhB,KAAsB,CAAtB,GAA0BqS,IAAI,CAACF,GAAD,CAA9B,GAAsC,CAACE,IAAI,CAACF,GAAG,GAAG,CAAP,CAAJ,GAAgBE,IAAI,CAACF,GAAD,CAArB,IAA8B,CAA3E;EACD;AAED,EAAO,SAASI,MAAT,CAAgBtH,MAAhB,EAAwB;EAC7B,SAAOpS,KAAK,CAAC2Z,IAAN,CAAW,IAAIC,GAAJ,CAAQxH,MAAR,EAAgBA,MAAhB,EAAX,CAAP;EACD;AAED,EAAO,SAASyH,WAAT,CAAqBzH,MAArB,EAA6B;EAClC,SAAO,IAAIwH,GAAJ,CAAQxH,MAAR,EAAgB0H,IAAvB;EACD;AAED,EAAO,SAASC,KAAT,CAAe3H,MAAf,EAAuB;EAC5B,SAAOA,MAAM,CAACjL,MAAd;EACD;;;;;;;;;;;;;;;ECzDD,IAAM6S,UAAU,GAAG,EAAnB;EACA,IAAMC,WAAW,GAAG,EAApB;;EAGAzb,OAAO,CAAC0b,YAAR,GAAuB,cAAvB;EACA1b,OAAO,CAAC2b,UAAR,GAAqB,YAArB;EACA3b,OAAO,CAAC4b,aAAR,GAAwB,eAAxB;AAEA,MAAaC,UAAU,GAAG,SAAbA,UAAa,CAAAla,KAAK,EAAI;EACjCA,EAAAA,KAAK,CAACma,qBAAN,GAA8B,CAACC,4BAAD,CAA9B;EACApa,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACoM,kBAAN,CAAyBrF,IAAzB,CAA8B,UAACxE,IAAD;EAAA,QAASiB,QAAT,QAASA,QAAT;EAAA,qBACzBjB,IADyB,GAE5BiB,QAAQ,CAACoK,KAAT,CAAeyM,OAFa;EAAA,GAA9B;EAIAra,EAAAA,KAAK,CAACmM,cAAN,CAAqBpF,IAArB,CAA0BoF,cAA1B;EACAnM,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,aAAvB;EACAvM,EAAAA,KAAK,CAACwM,UAAN,CAAiBzF,IAAjB,CAAsByF,YAAtB;EACD,CAVM;EAYP0N,UAAU,CAAClZ,UAAX,GAAwB,YAAxB;;EAEA,IAAMoZ,4BAA4B,GAAG,SAA/BA,4BAA+B,CAAChb,KAAD;EAAA,MAAUoT,MAAV,SAAUA,MAAV;EAAA,SAAuB,CAC1DpT,KAD0D,EAE1D;EACE4U,IAAAA,OAAO,EAAExB,MAAM,CAAC8H,UAAP,GACL,UAAAvS,CAAC,EAAI;EACHA,MAAAA,CAAC,CAACwS,OAAF;EACA/H,MAAAA,MAAM,CAACyH,aAAP;EACD,KAJI,GAKLtJ,SANN;EAOErR,IAAAA,KAAK,EAAE;EACLuI,MAAAA,MAAM,EAAE2K,MAAM,CAAC8H,UAAP,GAAoB,SAApB,GAAgC3J;EADnC,KAPT;EAUExC,IAAAA,KAAK,EAAE;EAVT,GAF0D,CAAvB;EAAA,CAArC;;;EAiBA,SAASR,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EACvD,MAAI8K,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACE+b,MAAAA,OAAO,EAAE;EADX,OAEKzM,KAFL;EAID;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC0b,YAA5B,EAA0C;EACxC,wBACKnM,KADL;EAEEyM,MAAAA,OAAO,EAAE7W,QAAQ,CAACgL,YAAT,CAAsB6L,OAAtB,IAAiC;EAF5C;EAID;;EAED,MAAI/L,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC2b,UAA5B,EAAwC;EAAA,QAC9Bxb,KAD8B,GACpB8P,MADoB,CAC9B9P,KAD8B;EAEtC,wBACKoP,KADL;EAEEyM,MAAAA,OAAO,EAAE7b;EAFX;EAID;;EAED,MAAI8P,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC4b,aAA5B,EAA2C;EAAA,QACjCvL,QADiC,GACDJ,MADC,CACjCI,QADiC;EAAA,QAChBsL,UADgB,GACD1L,MADC,CACvB9P,KADuB;EAGzC,QAAMgc,eAAe,GACnB,OAAOR,UAAP,KAAsB,WAAtB,GACIA,UADJ,GAEI,CAACpM,KAAK,CAACyM,OAAN,CAAc/V,QAAd,CAAuBoK,QAAvB,CAHP;;EAKA,QAAI8L,eAAJ,EAAqB;EACnB,0BACK5M,KADL;EAEEyM,QAAAA,OAAO,YAAMzM,KAAK,CAACyM,OAAZ,GAAqB3L,QAArB;EAFT;EAID;;EAED,wBACKd,KADL;EAEEyM,MAAAA,OAAO,EAAEzM,KAAK,CAACyM,OAAN,CAAchR,MAAd,CAAqB,UAAA5C,CAAC;EAAA,eAAIA,CAAC,KAAKiI,QAAV;EAAA,OAAtB;EAFX;EAID;EACF;;EAED,SAASvC,cAAT,CACE1H,OADF,SAOE;EAAA,MAHa4V,OAGb,SAJE7W,QAIF,CAHIoK,KAGJ,CAHayM,OAGb;EACA;EACA;EAEA,MAAMI,cAAc,GAAGJ,OAAO,CAC3BzV,GADoB,CAChB,UAAA8V,CAAC;EAAA,WAAIjW,OAAO,CAAC4M,IAAR,CAAa,UAAAsJ,GAAG;EAAA,aAAIA,GAAG,CAAC3V,EAAJ,KAAW0V,CAAf;EAAA,KAAhB,CAAJ;EAAA,GADe,EAEpBrR,MAFoB,CAEb+G,OAFa,CAAvB;EAIA,MAAMwK,iBAAiB,GAAGnW,OAAO,CAAC4E,MAAR,CAAe,UAAAsR,GAAG;EAAA,WAAI,CAACN,OAAO,CAAC/V,QAAR,CAAiBqW,GAAG,CAAC3V,EAArB,CAAL;EAAA,GAAlB,CAA1B;EAEAP,EAAAA,OAAO,aAAOgW,cAAP,EAA0BG,iBAA1B,CAAP;EAEAnW,EAAAA,OAAO,CAAC9D,OAAR,CAAgB,UAAA8C,MAAM,EAAI;EACxBA,IAAAA,MAAM,CAACoX,SAAP,GAAmBR,OAAO,CAAC/V,QAAR,CAAiBb,MAAM,CAACuB,EAAxB,CAAnB;EACAvB,IAAAA,MAAM,CAACqX,YAAP,GAAsBT,OAAO,CAACU,OAAR,CAAgBtX,MAAM,CAACuB,EAAvB,CAAtB;EACD,GAHD;EAKA,SAAOP,OAAP;EACD;;EAED,IAAMuW,uBAAuB,GAAG,EAAhC;;EAEA,SAASzO,aAAT,CAAqB/I,QAArB,EAA+B;EAAA,MAE3B6M,IAF2B,GAkBzB7M,QAlByB,CAE3B6M,IAF2B;EAAA,MAG3B5H,IAH2B,GAkBzBjF,QAlByB,CAG3BiF,IAH2B;EAAA,MAI3BqI,QAJ2B,GAkBzBtN,QAlByB,CAI3BsN,QAJ2B;EAAA,MAK3BC,QAL2B,GAkBzBvN,QAlByB,CAK3BuN,QAL2B;EAAA,MAM3BjL,UAN2B,GAkBzBtC,QAlByB,CAM3BsC,UAN2B;EAAA,MAO3BmJ,WAP2B,GAkBzBzL,QAlByB,CAO3ByL,WAP2B;EAAA,4BAkBzBzL,QAlByB,CAQ3ByX,SAR2B;EAAA,MAQ3BA,SAR2B,oCAQfC,gBARe;EAAA,MAS3BC,aAT2B,GAkBzB3X,QAlByB,CAS3B2X,aAT2B;EAAA,8BAkBzB3X,QAlByB,CAU3B4X,YAV2B;EAAA,MAUbC,gBAVa,sCAUML,uBAVN;EAAA,MAW3Bla,OAX2B,GAkBzB0C,QAlByB,CAW3B1C,OAX2B;EAAA,MAYlBuZ,OAZkB,GAkBzB7W,QAlByB,CAY3BoK,KAZ2B,CAYlByM,OAZkB;EAAA,MAa3BnL,QAb2B,GAkBzB1L,QAlByB,CAa3B0L,QAb2B;EAAA,8BAkBzB1L,QAlByB,CAc3B8X,gBAd2B;EAAA,MAc3BA,gBAd2B,sCAcR,IAdQ;EAAA,MAe3BC,cAf2B,GAkBzB/X,QAlByB,CAe3B+X,cAf2B;EAAA,MAgB3BC,iBAhB2B,GAkBzBhY,QAlByB,CAgB3BgY,iBAhB2B;EAAA,MAiB3BrM,QAjB2B,GAkBzB3L,QAlByB,CAiB3B2L,QAjB2B;EAoB7BtO,EAAAA,iBAAiB,CAACC,OAAD,EAAU,CAAC,gBAAD,EAAmB,YAAnB,CAAV,EAA4C,YAA5C,CAAjB;EAEA,MAAMuO,WAAW,GAAG3N,YAAY,CAAC8B,QAAD,CAAhC;EAEAsC,EAAAA,UAAU,CAACnF,OAAX,CAAmB,UAAA8C,MAAM,EAAI;EAAA,QAEzBwB,QAFyB,GAKvBxB,MALuB,CAEzBwB,QAFyB;EAAA,QAGTwW,oBAHS,GAKvBhY,MALuB,CAGzBiY,cAHyB;EAAA,QAITC,oBAJS,GAKvBlY,MALuB,CAIzB8X,cAJyB;EAO3B9X,IAAAA,MAAM,CAAC6W,UAAP,GAAoBrV,QAAQ,GACxB+C,eAAe,CACbvE,MAAM,CAAC6W,UADM,EAEbqB,oBAAoB,KAAK,IAAzB,GAAgC,KAAhC,GAAwChL,SAF3B,EAGb4K,cAAc,KAAK,IAAnB,GAA0B,KAA1B,GAAkC5K,SAHrB,EAIb,IAJa,CADS,GAOxB3I,eAAe,CACbvE,MAAM,CAAC6W,UADM,EAEbmB,oBAFa,EAGbD,iBAHa,EAIb,KAJa,CAPnB;;EAcA,QAAI/X,MAAM,CAAC6W,UAAX,EAAuB;EACrB7W,MAAAA,MAAM,CAACwW,aAAP,GAAuB;EAAA,eAAMzW,QAAQ,CAACyW,aAAT,CAAuBxW,MAAM,CAACuB,EAA9B,CAAN;EAAA,OAAvB;EACD;;EAEDvB,IAAAA,MAAM,CAACmY,UAAP,GAAoBnY,MAAM,CAACmY,UAAP,IAAqBnY,MAAM,CAAC9E,IAAhD;EACD,GA1BD;EA4BA,MAAMsb,aAAa,GAAGpY,KAAK,CAACG,WAAN,CACpB,UAAC0M,QAAD,EAAWlQ,KAAX,EAAqB;EACnB0Q,IAAAA,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAAC4b,aAAhB;EAA+BvL,MAAAA,QAAQ,EAARA,QAA/B;EAAyClQ,MAAAA,KAAK,EAALA;EAAzC,KAAD,CAAR;EACD,GAHmB,EAIpB,CAAC0Q,QAAD,CAJoB,CAAtB;EAOA,MAAM8K,UAAU,GAAGnY,KAAK,CAACG,WAAN,CACjB,UAAAxD,KAAK,EAAI;EACP0Q,IAAAA,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAAC2b,UAAhB;EAA4Bxb,MAAAA,KAAK,EAALA;EAA5B,KAAD,CAAR;EACD,GAHgB,EAIjB,CAAC0Q,QAAD,CAJiB,CAAnB;EAOAD,EAAAA,WAAW,CAACtO,OAAZ,CAAoB,UAAA6R,MAAM,EAAI;EAC5BA,IAAAA,MAAM,CAAC2H,qBAAP,GAA+Bpa,cAAc,CAC3CoP,QAAQ,GAAGgL,qBADgC,EAE3C;EAAE3W,MAAAA,QAAQ,EAAE6L,WAAW,EAAvB;EAA2BmD,MAAAA,MAAM,EAANA;EAA3B,KAF2C,CAA7C;EAID,GALD;;EAlE6B,uBAiFzB3Q,KAAK,CAACgP,OAAN,CAAc,YAAM;EACtB,QAAIsK,aAAa,IAAI,CAACd,OAAO,CAACrT,MAA9B,EAAsC;EACpC,aAAO,CACLyB,IADK,EAELqI,QAFK,EAGLC,QAHK,EAIL8I,UAJK,EAKLC,WALK,EAMLhJ,QANK,EAOLC,QAPK,CAAP;EASD,KAXqB;;;EActB,QAAM8K,eAAe,GAAGxB,OAAO,CAAChR,MAAR,CAAe,UAAAqR,CAAC;EAAA,aACtC5U,UAAU,CAACuL,IAAX,CAAgB,UAAAsJ,GAAG;EAAA,eAAIA,GAAG,CAAC3V,EAAJ,KAAW0V,CAAf;EAAA,OAAnB,CADsC;EAAA,KAAhB,CAAxB,CAdsB;EAmBtB;;EACA,QAAMoB,qBAAqB,GAAG,SAAxBA,qBAAwB,CAACC,QAAD,EAAWC,WAAX,EAAwBrX,KAAxB,EAAkC;EAC9D,UAAMsN,MAAM,GAAG,EAAf;EAEAnM,MAAAA,UAAU,CAACnF,OAAX,CAAmB,UAAA8C,MAAM,EAAI;EAC3B;EACA,YAAIoY,eAAe,CAACvX,QAAhB,CAAyBb,MAAM,CAACuB,EAAhC,CAAJ,EAAyC;EACvCiN,UAAAA,MAAM,CAACxO,MAAM,CAACuB,EAAR,CAAN,GAAoBgX,WAAW,CAAC,CAAD,CAAX,GAChBA,WAAW,CAAC,CAAD,CAAX,CAAe/J,MAAf,CAAsBxO,MAAM,CAACuB,EAA7B,CADgB,GAEhB,IAFJ;EAGA;EACD,SAP0B;;;EAU3B,YAAIiX,WAAW,GACb,OAAOxY,MAAM,CAACyY,SAAd,KAA4B,UAA5B,GACIzY,MAAM,CAACyY,SADX,GAEIb,gBAAgB,CAAC5X,MAAM,CAACyY,SAAR,CAAhB,IACAd,YAAY,CAAC3X,MAAM,CAACyY,SAAR,CAJlB;;EAMA,YAAID,WAAJ,EAAiB;EACf;EACA,cAAME,aAAa,GAAGH,WAAW,CAACpX,GAAZ,CAAgB,UAAAS,GAAG;EAAA,mBAAIA,GAAG,CAAC4M,MAAJ,CAAWxO,MAAM,CAACuB,EAAlB,CAAJ;EAAA,WAAnB,CAAtB,CAFe;;EAKf,cAAMoX,UAAU,GAAGL,QAAQ,CAACnX,GAAT,CAAa,UAAAS,GAAG,EAAI;EACrC,gBAAIgX,WAAW,GAAGhX,GAAG,CAAC4M,MAAJ,CAAWxO,MAAM,CAACuB,EAAlB,CAAlB;;EAEA,gBAAI,CAACL,KAAD,IAAUlB,MAAM,CAAC6Y,cAArB,EAAqC;EACnC,kBAAMC,gBAAgB,GACpB,OAAO9Y,MAAM,CAAC6Y,cAAd,KAAiC,UAAjC,GACI7Y,MAAM,CAAC6Y,cADX,GAEIjB,gBAAgB,CAAC5X,MAAM,CAAC6Y,cAAR,CAAhB,IACAlB,YAAY,CAAC3X,MAAM,CAAC6Y,cAAR,CAJlB;;EAMA,kBAAI,CAACC,gBAAL,EAAuB;EACrBjc,gBAAAA,OAAO,CAACC,IAAR,CAAa;EAAEkD,kBAAAA,MAAM,EAANA;EAAF,iBAAb;EACA,sBAAM,IAAIjD,KAAJ,6EAAN;EAGD;;EAED6b,cAAAA,WAAW,GAAGE,gBAAgB,CAACF,WAAD,EAAchX,GAAd,EAAmB5B,MAAnB,CAA9B;EACD;;EACD,mBAAO4Y,WAAP;EACD,WApBkB,CAAnB;EAsBApK,UAAAA,MAAM,CAACxO,MAAM,CAACuB,EAAR,CAAN,GAAoBiX,WAAW,CAACG,UAAD,EAAaD,aAAb,CAA/B;EACD,SA5BD,MA4BO,IAAI1Y,MAAM,CAACyY,SAAX,EAAsB;EAC3B5b,UAAAA,OAAO,CAACC,IAAR,CAAa;EAAEkD,YAAAA,MAAM,EAANA;EAAF,WAAb;EACA,gBAAM,IAAIjD,KAAJ,wEAAN;EAGD,SALM,MAKA;EACLyR,UAAAA,MAAM,CAACxO,MAAM,CAACuB,EAAR,CAAN,GAAoB,IAApB;EACD;EACF,OApDD;EAsDA,aAAOiN,MAAP;EACD,KA1DD;;EA4DA,QAAIuK,eAAe,GAAG,EAAtB;EACA,QAAMC,eAAe,GAAG,EAAxB;EACA,QAAMC,mBAAmB,GAAG,EAA5B;EACA,QAAMC,mBAAmB,GAAG,EAA5B;EACA,QAAMC,kBAAkB,GAAG,EAA3B;EACA,QAAMC,kBAAkB,GAAG,EAA3B,CArFsB;;EAwFtB,QAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAACrU,IAAD,EAAO9D,KAAP,EAAkBoY,QAAlB,EAA+B;EAAA,UAAxBpY,KAAwB;EAAxBA,QAAAA,KAAwB,GAAhB,CAAgB;EAAA;;EACxD;EACA,UAAIA,KAAK,KAAKkX,eAAe,CAAC7U,MAA9B,EAAsC;EACpC,eAAOyB,IAAI,CAAC7D,GAAL,CAAS,UAACS,GAAD;EAAA,8BAAeA,GAAf;EAAoBV,YAAAA,KAAK,EAALA;EAApB;EAAA,SAAT,CAAP;EACD;;EAED,UAAM+J,QAAQ,GAAGmN,eAAe,CAAClX,KAAD,CAAhC,CANwD;;EASxD,UAAIqY,YAAY,GAAG/B,SAAS,CAACxS,IAAD,EAAOiG,QAAP,CAA5B,CATwD;;EAYxD,UAAMuO,qBAAqB,GAAG/Y,MAAM,CAACgZ,OAAP,CAAeF,YAAf,EAA6BpY,GAA7B,CAC5B,iBAA4BwG,KAA5B,EAAsC;EAAA,YAApC+R,UAAoC;EAAA,YAAxBnB,WAAwB;EACpC,YAAIhX,EAAE,GAAM0J,QAAN,SAAkByO,UAAxB;EACAnY,QAAAA,EAAE,GAAG+X,QAAQ,GAAMA,QAAN,SAAkB/X,EAAlB,GAAyBA,EAAtC,CAFoC;;EAKpC,YAAMmE,OAAO,GAAG2T,kBAAkB,CAACd,WAAD,EAAcrX,KAAK,GAAG,CAAtB,EAAyBK,EAAzB,CAAlC,CALoC;;EAQpC,YAAM+W,QAAQ,GAAGpX,KAAK,GAClBI,SAAS,CAACiX,WAAD,EAAc,UAAd,CADS,GAElBA,WAFJ;EAIA,YAAM/J,MAAM,GAAG6J,qBAAqB,CAACC,QAAD,EAAWC,WAAX,EAAwBrX,KAAxB,CAApC;EAEA,YAAMU,GAAG,GAAG;EACVL,UAAAA,EAAE,EAAFA,EADU;EAEV6V,UAAAA,SAAS,EAAE,IAFD;EAGVuC,UAAAA,SAAS,EAAE1O,QAHD;EAIVyO,UAAAA,UAAU,EAAVA,UAJU;EAKVlL,UAAAA,MAAM,EAANA,MALU;EAMV9I,UAAAA,OAAO,EAAPA,OANU;EAOV4S,UAAAA,QAAQ,EAARA,QAPU;EAQVpX,UAAAA,KAAK,EAALA,KARU;EASVyG,UAAAA,KAAK,EAALA;EATU,SAAZ;EAYAjC,QAAAA,OAAO,CAACxI,OAAR,CAAgB,UAAA0c,MAAM,EAAI;EACxBb,UAAAA,eAAe,CAACzV,IAAhB,CAAqBsW,MAArB;EACAZ,UAAAA,eAAe,CAACY,MAAM,CAACrY,EAAR,CAAf,GAA6BqY,MAA7B;;EACA,cAAIA,MAAM,CAACxC,SAAX,EAAsB;EACpB6B,YAAAA,mBAAmB,CAAC3V,IAApB,CAAyBsW,MAAzB;EACAV,YAAAA,mBAAmB,CAACU,MAAM,CAACrY,EAAR,CAAnB,GAAiCqY,MAAjC;EACD,WAHD,MAGO;EACLT,YAAAA,kBAAkB,CAAC7V,IAAnB,CAAwBsW,MAAxB;EACAR,YAAAA,kBAAkB,CAACQ,MAAM,CAACrY,EAAR,CAAlB,GAAgCqY,MAAhC;EACD;EACF,SAVD;EAYA,eAAOhY,GAAP;EACD,OAxC2B,CAA9B;EA2CA,aAAO4X,qBAAP;EACD,KAxDD;;EA0DA,QAAMjB,WAAW,GAAGc,kBAAkB,CAACrU,IAAD,CAAtC;EAEAuT,IAAAA,WAAW,CAACrb,OAAZ,CAAoB,UAAA0c,MAAM,EAAI;EAC5Bb,MAAAA,eAAe,CAACzV,IAAhB,CAAqBsW,MAArB;EACAZ,MAAAA,eAAe,CAACY,MAAM,CAACrY,EAAR,CAAf,GAA6BqY,MAA7B;;EACA,UAAIA,MAAM,CAACxC,SAAX,EAAsB;EACpB6B,QAAAA,mBAAmB,CAAC3V,IAApB,CAAyBsW,MAAzB;EACAV,QAAAA,mBAAmB,CAACU,MAAM,CAACrY,EAAR,CAAnB,GAAiCqY,MAAjC;EACD,OAHD,MAGO;EACLT,QAAAA,kBAAkB,CAAC7V,IAAnB,CAAwBsW,MAAxB;EACAR,QAAAA,kBAAkB,CAACQ,MAAM,CAACrY,EAAR,CAAlB,GAAgCqY,MAAhC;EACD;EACF,KAVD,EApJsB;;EAiKtB,WAAO,CACLrB,WADK,EAELQ,eAFK,EAGLC,eAHK,EAILC,mBAJK,EAKLC,mBALK,EAMLC,kBANK,EAOLC,kBAPK,CAAP;EASD,GA1KG,EA0KD,CACD1B,aADC,EAEDd,OAFC,EAGD5R,IAHC,EAIDqI,QAJC,EAKDC,QALC,EAMDjL,UANC,EAODuV,gBAPC,EAQDJ,SARC,CA1KC,CAjFyB;EAAA,MA0E3Be,WA1E2B;EAAA,MA2E3BQ,eA3E2B;EAAA,MA4E3BC,eA5E2B;EAAA,MA6E3BC,mBA7E2B;EAAA,MA8E3BC,mBA9E2B;EAAA,MA+E3BC,kBA/E2B;EAAA,MAgF3BC,kBAhF2B;;EAsQ7B,MAAMS,mBAAmB,GAAG5b,YAAY,CAAC4Z,gBAAD,CAAxC;EAEAjZ,EAAAA,sBAAsB,CAAC,YAAM;EAC3B,QAAIib,mBAAmB,EAAvB,EAA2B;EACzBpO,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAAC0b;EAAhB,OAAD,CAAR;EACD;EACF,GAJqB,EAInB,CAAC7K,QAAD,EAAWiM,aAAa,GAAG,IAAH,GAAU9K,IAAlC,CAJmB,CAAtB;EAMAnM,EAAAA,MAAM,CAACsB,MAAP,CAAchC,QAAd,EAAwB;EACtB+Z,IAAAA,cAAc,EAAE9U,IADM;EAEtB+U,IAAAA,iBAAiB,EAAE1M,QAFG;EAGtB2M,IAAAA,kBAAkB,EAAE1M,QAHE;EAItBiL,IAAAA,WAAW,EAAXA,WAJsB;EAKtBQ,IAAAA,eAAe,EAAfA,eALsB;EAMtBC,IAAAA,eAAe,EAAfA,eANsB;EAOtBC,IAAAA,mBAAmB,EAAnBA,mBAPsB;EAQtBC,IAAAA,mBAAmB,EAAnBA,mBARsB;EAStBC,IAAAA,kBAAkB,EAAlBA,kBATsB;EAUtBC,IAAAA,kBAAkB,EAAlBA,kBAVsB;EAWtBpU,IAAAA,IAAI,EAAEuT,WAXgB;EAYtBlL,IAAAA,QAAQ,EAAE0L,eAZY;EAatBzL,IAAAA,QAAQ,EAAE0L,eAbY;EActBxC,IAAAA,aAAa,EAAbA,aAdsB;EAetBD,IAAAA,UAAU,EAAVA;EAfsB,GAAxB;EAiBD;;EAED,SAASxN,YAAT,CAAoBnH,GAApB,EAAyB;EACvBA,EAAAA,GAAG,CAAC2M,QAAJ,CAAarR,OAAb,CAAqB,UAAA6K,IAAI,EAAI;EAAA;;EAC3B;EACAA,IAAAA,IAAI,CAACqP,SAAL,GAAiBrP,IAAI,CAAC/H,MAAL,CAAYoX,SAAZ,IAAyBrP,IAAI,CAAC/H,MAAL,CAAYuB,EAAZ,KAAmBK,GAAG,CAAC+X,SAAjE,CAF2B;;EAI3B5R,IAAAA,IAAI,CAACkS,aAAL,GAAqB,CAAClS,IAAI,CAACqP,SAAN,IAAmBrP,IAAI,CAAC/H,MAAL,CAAYoX,SAApD,CAJ2B;;EAM3BrP,IAAAA,IAAI,CAACmS,YAAL,GACE,CAACnS,IAAI,CAACqP,SAAN,IAAmB,CAACrP,IAAI,CAACkS,aAAzB,qBAA0CrY,GAAG,CAAC8D,OAA9C,qBAA0C,aAAanC,MAAvD,CADF;EAED,GARD;EASD;;AAED,EAAO,SAASkU,gBAAT,CAA0BzS,IAA1B,EAAgCiG,QAAhC,EAA0C;EAC/C,SAAOjG,IAAI,CAACtJ,MAAL,CAAY,UAACc,IAAD,EAAOoF,GAAP,EAAY4C,CAAZ,EAAkB;EACnC;EACA;EACA,QAAM2V,MAAM,QAAMvY,GAAG,CAAC4M,MAAJ,CAAWvD,QAAX,CAAlB;EACAzO,IAAAA,IAAI,CAAC2d,MAAD,CAAJ,GAAe/d,KAAK,CAACC,OAAN,CAAcG,IAAI,CAAC2d,MAAD,CAAlB,IAA8B3d,IAAI,CAAC2d,MAAD,CAAlC,GAA6C,EAA5D;EACA3d,IAAAA,IAAI,CAAC2d,MAAD,CAAJ,CAAa7W,IAAb,CAAkB1B,GAAlB;EACA,WAAOpF,IAAP;EACD,GAPM,EAOJ,EAPI,CAAP;EAQD;;ECrbD,IAAM4d,mBAAmB,GAAG,YAA5B;EAGA;EACA;;AACA,EAAO,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAACC,IAAD,EAAOC,IAAP,EAAatP,QAAb,EAA0B;EAAA,8BACvCuP,sBAAsB,CAACF,IAAD,EAAOC,IAAP,EAAatP,QAAb,CADiB;EAAA,MAC/CvG,CAD+C;EAAA,MAC5CmR,CAD4C;;;EAIpDnR,EAAAA,CAAC,GAAG+V,QAAQ,CAAC/V,CAAD,CAAZ;EACAmR,EAAAA,CAAC,GAAG4E,QAAQ,CAAC5E,CAAD,CAAZ,CALoD;EAQpD;;EACAnR,EAAAA,CAAC,GAAGA,CAAC,CAAC/C,KAAF,CAAQyY,mBAAR,EAA6BxU,MAA7B,CAAoC+G,OAApC,CAAJ;EACAkJ,EAAAA,CAAC,GAAGA,CAAC,CAAClU,KAAF,CAAQyY,mBAAR,EAA6BxU,MAA7B,CAAoC+G,OAApC,CAAJ,CAVoD;;EAapD,SAAOjI,CAAC,CAACnB,MAAF,IAAYsS,CAAC,CAACtS,MAArB,EAA6B;EAC3B,QAAImX,EAAE,GAAGhW,CAAC,CAAC8I,KAAF,EAAT;EACA,QAAImN,EAAE,GAAG9E,CAAC,CAACrI,KAAF,EAAT;EAEA,QAAMoN,EAAE,GAAGC,QAAQ,CAACH,EAAD,EAAK,EAAL,CAAnB;EACA,QAAMI,EAAE,GAAGD,QAAQ,CAACF,EAAD,EAAK,EAAL,CAAnB;EAEA,QAAMI,KAAK,GAAG,CAACH,EAAD,EAAKE,EAAL,EAAS9M,IAAT,EAAd,CAP2B;;EAU3B,QAAIgN,KAAK,CAACD,KAAK,CAAC,CAAD,CAAN,CAAT,EAAqB;EACnB,UAAIL,EAAE,GAAGC,EAAT,EAAa;EACX,eAAO,CAAP;EACD;;EACD,UAAIA,EAAE,GAAGD,EAAT,EAAa;EACX,eAAO,CAAC,CAAR;EACD;;EACD;EACD,KAlB0B;;;EAqB3B,QAAIM,KAAK,CAACD,KAAK,CAAC,CAAD,CAAN,CAAT,EAAqB;EACnB,aAAOC,KAAK,CAACJ,EAAD,CAAL,GAAY,CAAC,CAAb,GAAiB,CAAxB;EACD,KAvB0B;;;EA0B3B,QAAIA,EAAE,GAAGE,EAAT,EAAa;EACX,aAAO,CAAP;EACD;;EACD,QAAIA,EAAE,GAAGF,EAAT,EAAa;EACX,aAAO,CAAC,CAAR;EACD;EACF;;EAED,SAAOlW,CAAC,CAACnB,MAAF,GAAWsS,CAAC,CAACtS,MAApB;EACD,CAhDM;AAiDP,EAAO,SAAS0X,QAAT,CAAkBX,IAAlB,EAAwBC,IAAxB,EAA8BtP,QAA9B,EAAwC;EAAA,+BAChCuP,sBAAsB,CAACF,IAAD,EAAOC,IAAP,EAAatP,QAAb,CADU;EAAA,MACxCvG,CADwC;EAAA,MACrCmR,CADqC;;EAG7CnR,EAAAA,CAAC,GAAGA,CAAC,CAACwW,OAAF,EAAJ;EACArF,EAAAA,CAAC,GAAGA,CAAC,CAACqF,OAAF,EAAJ;EAEA,SAAOC,YAAY,CAACzW,CAAD,EAAImR,CAAJ,CAAnB;EACD;AAED,EAAO,SAASuF,KAAT,CAAed,IAAf,EAAqBC,IAArB,EAA2BtP,QAA3B,EAAqC;EAAA,+BAC7BuP,sBAAsB,CAACF,IAAD,EAAOC,IAAP,EAAatP,QAAb,CADO;EAAA,MACrCvG,CADqC;EAAA,MAClCmR,CADkC;;EAG1C,SAAOsF,YAAY,CAACzW,CAAD,EAAImR,CAAJ,CAAnB;EACD;AAED,EAAO,SAASwF,MAAT,CAAgBf,IAAhB,EAAsBC,IAAtB,EAA4BtP,QAA5B,EAAsC;EAAA,+BAC9BuP,sBAAsB,CAACF,IAAD,EAAOC,IAAP,EAAatP,QAAb,CADQ;EAAA,MACtCvG,CADsC;EAAA,MACnCmR,CADmC;;EAG3CnR,EAAAA,CAAC,GAAGA,CAAC,CAAC/C,KAAF,CAAQ,EAAR,EAAYiE,MAAZ,CAAmB+G,OAAnB,CAAJ;EACAkJ,EAAAA,CAAC,GAAGA,CAAC,CAAClU,KAAF,CAAQ,EAAR,EAAYiE,MAAZ,CAAmB+G,OAAnB,CAAJ;;EAEA,SAAOjI,CAAC,CAACnB,MAAF,IAAYsS,CAAC,CAACtS,MAArB,EAA6B;EAC3B,QAAImX,EAAE,GAAGhW,CAAC,CAAC8I,KAAF,EAAT;EACA,QAAImN,EAAE,GAAG9E,CAAC,CAACrI,KAAF,EAAT;EAEA,QAAI8N,MAAM,GAAGZ,EAAE,CAAC/I,WAAH,EAAb;EACA,QAAI4J,MAAM,GAAGZ,EAAE,CAAChJ,WAAH,EAAb,CAL2B;;EAQ3B,QAAI2J,MAAM,GAAGC,MAAb,EAAqB;EACnB,aAAO,CAAP;EACD;;EACD,QAAIA,MAAM,GAAGD,MAAb,EAAqB;EACnB,aAAO,CAAC,CAAR;EACD,KAb0B;;;EAe3B,QAAIZ,EAAE,GAAGC,EAAT,EAAa;EACX,aAAO,CAAP;EACD;;EACD,QAAIA,EAAE,GAAGD,EAAT,EAAa;EACX,aAAO,CAAC,CAAR;EACD;;EACD;EACD;;EAED,SAAOhW,CAAC,CAACnB,MAAF,GAAWsS,CAAC,CAACtS,MAApB;EACD;AAED,EAAO,SAASiY,MAAT,CAAgBlB,IAAhB,EAAsBC,IAAtB,EAA4BtP,QAA5B,EAAsC;EAAA,+BAC9BuP,sBAAsB,CAACF,IAAD,EAAOC,IAAP,EAAatP,QAAb,CADQ;EAAA,MACtCvG,CADsC;EAAA,MACnCmR,CADmC;;EAG3C,MAAM4F,iBAAiB,GAAG,WAA1B;EAEA/W,EAAAA,CAAC,GAAGpJ,MAAM,CAACyL,MAAM,CAACrC,CAAD,CAAN,CAAUsC,OAAV,CAAkByU,iBAAlB,EAAqC,EAArC,CAAD,CAAV;EACA5F,EAAAA,CAAC,GAAGva,MAAM,CAACyL,MAAM,CAAC8O,CAAD,CAAN,CAAU7O,OAAV,CAAkByU,iBAAlB,EAAqC,EAArC,CAAD,CAAV;EAEA,SAAON,YAAY,CAACzW,CAAD,EAAImR,CAAJ,CAAnB;EACD;;EAID,SAASsF,YAAT,CAAsBzW,CAAtB,EAAyBmR,CAAzB,EAA4B;EAC1B,SAAOnR,CAAC,KAAKmR,CAAN,GAAU,CAAV,GAAcnR,CAAC,GAAGmR,CAAJ,GAAQ,CAAR,GAAY,CAAC,CAAlC;EACD;;EAED,SAAS2E,sBAAT,CAAgCkB,IAAhC,EAAsCC,IAAtC,EAA4C1Q,QAA5C,EAAsD;EACpD,SAAO,CAACyQ,IAAI,CAAClN,MAAL,CAAYvD,QAAZ,CAAD,EAAwB0Q,IAAI,CAACnN,MAAL,CAAYvD,QAAZ,CAAxB,CAAP;EACD;;EAED,SAASwP,QAAT,CAAkB/V,CAAlB,EAAqB;EACnB,MAAI,OAAOA,CAAP,KAAa,QAAjB,EAA2B;EACzB,QAAIsW,KAAK,CAACtW,CAAD,CAAL,IAAYA,CAAC,KAAK2N,QAAlB,IAA8B3N,CAAC,KAAK,CAAC2N,QAAzC,EAAmD;EACjD,aAAO,EAAP;EACD;;EACD,WAAOtL,MAAM,CAACrC,CAAD,CAAb;EACD;;EACD,MAAI,OAAOA,CAAP,KAAa,QAAjB,EAA2B;EACzB,WAAOA,CAAP;EACD;;EACD,SAAO,EAAP;EACD;;;;;;;;;;;ECtHD9J,OAAO,CAACghB,WAAR,GAAsB,aAAtB;EACAhhB,OAAO,CAACihB,SAAR,GAAoB,WAApB;EACAjhB,OAAO,CAACkhB,YAAR,GAAuB,cAAvB;EACAlhB,OAAO,CAACmhB,WAAR,GAAsB,aAAtB;EAEA9gB,aAAa,CAAC+gB,QAAd,GAAyB,cAAzB;EACA/gB,aAAa,CAACghB,aAAd,GAA8B,KAA9B;AAEA,MAAaC,SAAS,GAAG,SAAZA,SAAY,CAAA3f,KAAK,EAAI;EAChCA,EAAAA,KAAK,CAAC4f,oBAAN,GAA6B,CAACC,2BAAD,CAA7B;EACA7f,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,aAAvB;EACD,CAJM;EAMPoT,SAAS,CAAC3e,UAAV,GAAuB,WAAvB;;EAEA,IAAM6e,2BAA2B,GAAG,SAA9BA,2BAA8B,CAACzgB,KAAD,QAAiC;EAAA,MAAvBoE,QAAuB,QAAvBA,QAAuB;EAAA,MAAbC,MAAa,QAAbA,MAAa;EAAA,8BACpBD,QADoB,CAC3Dsc,gBAD2D;EAAA,MAC3DA,gBAD2D,sCACxC,UAAA/X,CAAC;EAAA,WAAIA,CAAC,CAACgY,QAAN;EAAA,GADuC;EAGnE,SAAO,CACL3gB,KADK,EAEL;EACE4U,IAAAA,OAAO,EAAEvQ,MAAM,CAACuc,OAAP,GACL,UAAAjY,CAAC,EAAI;EACHA,MAAAA,CAAC,CAACwS,OAAF;EACA9W,MAAAA,MAAM,CAAC8b,YAAP,CACE5O,SADF,EAEE,CAACnN,QAAQ,CAACyc,gBAAV,IAA8BH,gBAAgB,CAAC/X,CAAD,CAFhD;EAID,KAPI,GAQL4I,SATN;EAUErR,IAAAA,KAAK,EAAE;EACLuI,MAAAA,MAAM,EAAEpE,MAAM,CAACuc,OAAP,GAAiB,SAAjB,GAA6BrP;EADhC,KAVT;EAaExC,IAAAA,KAAK,EAAE1K,MAAM,CAACuc,OAAP,GAAiB,eAAjB,GAAmCrP;EAb5C,GAFK,CAAP;EAkBD,CArBD;;;EAwBA,SAAShD,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EACvD,MAAI8K,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACE4hB,MAAAA,MAAM,EAAE;EADV,OAEKtS,KAFL;EAID;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACghB,WAA5B,EAAyC;EACvC,wBACKzR,KADL;EAEEsS,MAAAA,MAAM,EAAE1c,QAAQ,CAACgL,YAAT,CAAsB0R,MAAtB,IAAgC;EAF1C;EAID;;EAED,MAAI5R,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACmhB,WAA5B,EAAyC;EAAA,QAC/BU,MAD+B,GACpBtS,KADoB,CAC/BsS,MAD+B;EAEvC,QAAMC,SAAS,GAAGD,MAAM,CAAC7W,MAAP,CAAc,UAAA5C,CAAC;EAAA,aAAIA,CAAC,CAACzB,EAAF,KAASsJ,MAAM,CAACI,QAApB;EAAA,KAAf,CAAlB;EAEA,wBACKd,KADL;EAEEsS,MAAAA,MAAM,EAAEC;EAFV;EAID;;EAED,MAAI7R,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACihB,SAA5B,EAAuC;EAAA,QAC7BY,OAD6B,GAClB5R,MADkB,CAC7B4R,MAD6B;EAErC,wBACKtS,KADL;EAEEsS,MAAAA,MAAM,EAANA;EAFF;EAID;;EAED,MAAI5R,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACkhB,YAA5B,EAA0C;EAAA,QAChC7Q,QADgC,GACNJ,MADM,CAChCI,QADgC;EAAA,QACtB0R,IADsB,GACN9R,MADM,CACtB8R,IADsB;EAAA,QAChBC,KADgB,GACN/R,MADM,CAChB+R,KADgB;EAAA,QAItCva,UAJsC,GASpCtC,QAToC,CAItCsC,UAJsC;EAAA,QAKtCma,gBALsC,GASpCzc,QAToC,CAKtCyc,gBALsC;EAAA,QAMtCK,iBANsC,GASpC9c,QAToC,CAMtC8c,iBANsC;EAAA,QAOtCC,kBAPsC,GASpC/c,QAToC,CAOtC+c,kBAPsC;EAAA,gCASpC/c,QAToC,CAQtCgd,oBARsC;EAAA,QAQtCA,oBARsC,sCAQfzhB,MAAM,CAACC,gBARQ;EAAA,QAWhCkhB,QAXgC,GAWrBtS,KAXqB,CAWhCsS,MAXgC;;EAcxC,QAAMzc,MAAM,GAAGqC,UAAU,CAACuL,IAAX,CAAgB,UAAA5K,CAAC;EAAA,aAAIA,CAAC,CAACzB,EAAF,KAAS0J,QAAb;EAAA,KAAjB,CAAf;EAdwC,QAehCgR,aAfgC,GAedjc,MAfc,CAehCic,aAfgC;;EAkBxC,QAAMe,cAAc,GAAGP,QAAM,CAAC7O,IAAP,CAAY,UAAA5K,CAAC;EAAA,aAAIA,CAAC,CAACzB,EAAF,KAAS0J,QAAb;EAAA,KAAb,CAAvB;;EACA,QAAMgS,aAAa,GAAGR,QAAM,CAAC/e,SAAP,CAAiB,UAAAsF,CAAC;EAAA,aAAIA,CAAC,CAACzB,EAAF,KAAS0J,QAAb;EAAA,KAAlB,CAAtB;;EACA,QAAMiS,cAAc,GAAG,OAAOP,IAAP,KAAgB,WAAhB,IAA+BA,IAAI,KAAK,IAA/D;EAEA,QAAID,UAAS,GAAG,EAAhB,CAtBwC;;EAyBxC,QAAIS,UAAJ;;EAEA,QAAI,CAACX,gBAAD,IAAqBI,KAAzB,EAAgC;EAC9B,UAAII,cAAJ,EAAoB;EAClBG,QAAAA,UAAU,GAAG,QAAb;EACD,OAFD,MAEO;EACLA,QAAAA,UAAU,GAAG,KAAb;EACD;EACF,KAND,MAMO;EACL;EACA,UAAIF,aAAa,KAAKR,QAAM,CAAClZ,MAAP,GAAgB,CAAlC,IAAuCkZ,QAAM,CAAClZ,MAAP,KAAkB,CAA7D,EAAgE;EAC9D4Z,QAAAA,UAAU,GAAG,SAAb;EACD,OAFD,MAEO,IAAIH,cAAJ,EAAoB;EACzBG,QAAAA,UAAU,GAAG,QAAb;EACD,OAFM,MAEA;EACLA,QAAAA,UAAU,GAAG,SAAb;EACD;EACF,KA1CuC;;;EA6CxC,QACEA,UAAU,KAAK,QAAf;EACA,KAACN,iBADD;EAEA,KAACK,cAFD;EAGCN,IAAAA,KAAK,GAAG,CAACE,kBAAJ,GAAyB,IAH/B;EAIEE,IAAAA,cAAc;EACdA,IAAAA,cAAc,CAACL,IADf,IAEA,CAACV,aAFF,IAGE,CAACe,cAAc,CAACL,IAAhB,IAAwBV,aAP3B,CADF,EASE;EACAkB,MAAAA,UAAU,GAAG,QAAb;EACD;;EAED,QAAIA,UAAU,KAAK,SAAnB,EAA8B;EAC5BT,MAAAA,UAAS,GAAG,CACV;EACEnb,QAAAA,EAAE,EAAE0J,QADN;EAEE0R,QAAAA,IAAI,EAAEO,cAAc,GAAGP,IAAH,GAAUV;EAFhC,OADU,CAAZ;EAMD,KAPD,MAOO,IAAIkB,UAAU,KAAK,KAAnB,EAA0B;EAC/BT,MAAAA,UAAS,aACJD,QADI,GAEP;EACElb,QAAAA,EAAE,EAAE0J,QADN;EAEE0R,QAAAA,IAAI,EAAEO,cAAc,GAAGP,IAAH,GAAUV;EAFhC,OAFO,EAAT,CAD+B;;EAS/BS,MAAAA,UAAS,CAACU,MAAV,CAAiB,CAAjB,EAAoBV,UAAS,CAACnZ,MAAV,GAAmBwZ,oBAAvC;EACD,KAVM,MAUA,IAAII,UAAU,KAAK,QAAnB,EAA6B;EAClC;EACAT,MAAAA,UAAS,GAAGD,QAAM,CAACtb,GAAP,CAAW,UAAA6B,CAAC,EAAI;EAC1B,YAAIA,CAAC,CAACzB,EAAF,KAAS0J,QAAb,EAAuB;EACrB,8BACKjI,CADL;EAEE2Z,YAAAA,IAAI,EAAEO,cAAc,GAAGP,IAAH,GAAU,CAACK,cAAc,CAACL;EAFhD;EAID;;EACD,eAAO3Z,CAAP;EACD,OARW,CAAZ;EASD,KAXM,MAWA,IAAIma,UAAU,KAAK,QAAnB,EAA6B;EAClCT,MAAAA,UAAS,GAAGD,QAAM,CAAC7W,MAAP,CAAc,UAAA5C,CAAC;EAAA,eAAIA,CAAC,CAACzB,EAAF,KAAS0J,QAAb;EAAA,OAAf,CAAZ;EACD;;EAED,wBACKd,KADL;EAEEsS,MAAAA,MAAM,EAAEC;EAFV;EAID;EACF;;EAED,SAAS5T,aAAT,CAAqB/I,QAArB,EAA+B;EAAA,MAE3B6M,IAF2B,GAiBzB7M,QAjByB,CAE3B6M,IAF2B;EAAA,MAG3B5H,IAH2B,GAiBzBjF,QAjByB,CAG3BiF,IAH2B;EAAA,MAI3BqI,QAJ2B,GAiBzBtN,QAjByB,CAI3BsN,QAJ2B;EAAA,MAK3BhL,UAL2B,GAiBzBtC,QAjByB,CAK3BsC,UAL2B;EAAA,4BAiBzBtC,QAjByB,CAM3Bsd,SAN2B;EAAA,MAM3BA,SAN2B,oCAMfC,gBANe;EAAA,MAOhBC,aAPgB,GAiBzBxd,QAjByB,CAO3Byd,SAP2B;EAAA,MAQ3BC,YAR2B,GAiBzB1d,QAjByB,CAQ3B0d,YAR2B;EAAA,MAS3BC,cAT2B,GAiBzB3d,QAjByB,CAS3B2d,cAT2B;EAAA,MAU3BC,aAV2B,GAiBzB5d,QAjByB,CAU3B4d,aAV2B;EAAA,MAW3BnS,WAX2B,GAiBzBzL,QAjByB,CAW3ByL,WAX2B;EAAA,MAYlBiR,MAZkB,GAiBzB1c,QAjByB,CAY3BoK,KAZ2B,CAYlBsS,MAZkB;EAAA,MAa3BhR,QAb2B,GAiBzB1L,QAjByB,CAa3B0L,QAb2B;EAAA,MAc3BpO,OAd2B,GAiBzB0C,QAjByB,CAc3B1C,OAd2B;EAAA,MAe3BqO,QAf2B,GAiBzB3L,QAjByB,CAe3B2L,QAf2B;EAAA,8BAiBzB3L,QAjByB,CAgB3B6d,eAhB2B;EAAA,MAgB3BA,eAhB2B,sCAgBT,IAhBS;EAmB7BxgB,EAAAA,iBAAiB,CACfC,OADe,EAEf,CAAC,YAAD,EAAe,iBAAf,EAAkC,YAAlC,EAAgD,iBAAhD,CAFe,EAGf,WAHe,CAAjB;EAMA,MAAMwe,SAAS,GAAGzd,KAAK,CAACG,WAAN,CAChB,UAAAke,MAAM,EAAI;EACRhR,IAAAA,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACihB,SAAhB;EAA2BY,MAAAA,MAAM,EAANA;EAA3B,KAAD,CAAR;EACD,GAHe,EAIhB,CAAChR,QAAD,CAJgB,CAAlB,CAzB6B;;EAiC7B,MAAMqQ,YAAY,GAAG1d,KAAK,CAACG,WAAN,CACnB,UAAC0M,QAAD,EAAW0R,IAAX,EAAiBC,KAAjB,EAA2B;EACzBnR,IAAAA,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACkhB,YAAhB;EAA8B7Q,MAAAA,QAAQ,EAARA,QAA9B;EAAwC0R,MAAAA,IAAI,EAAJA,IAAxC;EAA8CC,MAAAA,KAAK,EAALA;EAA9C,KAAD,CAAR;EACD,GAHkB,EAInB,CAACnR,QAAD,CAJmB,CAArB,CAjC6B;;EAyC7B,MAAMG,WAAW,GAAG3N,YAAY,CAAC8B,QAAD,CAAhC,CAzC6B;;EA4C7ByL,EAAAA,WAAW,CAACtO,OAAZ,CAAoB,UAAA8C,MAAM,EAAI;EAAA,QAE1BwB,QAF0B,GAMxBxB,MANwB,CAE1BwB,QAF0B;EAAA,QAGjBqc,oBAHiB,GAMxB7d,MANwB,CAG1Buc,OAH0B;EAAA,QAIXuB,mBAJW,GAMxB9d,MANwB,CAI1B2d,aAJ0B;EAAA,QAK1Bpc,EAL0B,GAMxBvB,MANwB,CAK1BuB,EAL0B;EAQ5B,QAAMgb,OAAO,GAAG/a,QAAQ,GACpB+C,eAAe,CACbuZ,mBAAmB,KAAK,IAAxB,GAA+B,KAA/B,GAAuC5Q,SAD1B,EAEbyQ,aAAa,KAAK,IAAlB,GAAyB,KAAzB,GAAiCzQ,SAFpB,EAGb,IAHa,CADK,GAMpB3I,eAAe,CAACmZ,cAAD,EAAiBG,oBAAjB,EAAuC,KAAvC,CANnB;EAQA7d,IAAAA,MAAM,CAACuc,OAAP,GAAiBA,OAAjB;;EAEA,QAAIvc,MAAM,CAACuc,OAAX,EAAoB;EAClBvc,MAAAA,MAAM,CAAC8b,YAAP,GAAsB,UAACa,IAAD,EAAOC,KAAP;EAAA,eACpBd,YAAY,CAAC9b,MAAM,CAACuB,EAAR,EAAYob,IAAZ,EAAkBC,KAAlB,CADQ;EAAA,OAAtB;;EAGA5c,MAAAA,MAAM,CAAC+b,WAAP,GAAqB,YAAM;EACzBtQ,QAAAA,QAAQ,CAAC;EAAExL,UAAAA,IAAI,EAAErF,OAAO,CAACmhB,WAAhB;EAA6B9Q,UAAAA,QAAQ,EAAEjL,MAAM,CAACuB;EAA9C,SAAD,CAAR;EACD,OAFD;EAGD;;EAEDvB,IAAAA,MAAM,CAACmc,oBAAP,GAA8B7f,cAAc,CAC1CoP,QAAQ,GAAGyQ,oBAD+B,EAE1C;EACEpc,MAAAA,QAAQ,EAAE6L,WAAW,EADvB;EAEE5L,MAAAA,MAAM,EAANA;EAFF,KAF0C,CAA5C;EAQA,QAAM+d,UAAU,GAAGtB,MAAM,CAAC7O,IAAP,CAAY,UAAA5K,CAAC;EAAA,aAAIA,CAAC,CAACzB,EAAF,KAASA,EAAb;EAAA,KAAb,CAAnB;EACAvB,IAAAA,MAAM,CAACge,QAAP,GAAkB,CAAC,CAACD,UAApB;EACA/d,IAAAA,MAAM,CAACie,WAAP,GAAqBxB,MAAM,CAAC/e,SAAP,CAAiB,UAAAsF,CAAC;EAAA,aAAIA,CAAC,CAACzB,EAAF,KAASA,EAAb;EAAA,KAAlB,CAArB;EACAvB,IAAAA,MAAM,CAACke,YAAP,GAAsBle,MAAM,CAACge,QAAP,GAAkBD,UAAU,CAACpB,IAA7B,GAAoCzP,SAA1D;EACD,GAvCD;;EA5C6B,uBAqFQ9O,KAAK,CAACgP,OAAN,CAAc,YAAM;EACvD,QAAIqQ,YAAY,IAAI,CAAChB,MAAM,CAAClZ,MAA5B,EAAoC;EAClC,aAAO,CAACyB,IAAD,EAAOqI,QAAP,CAAP;EACD;;EAED,QAAM8Q,cAAc,GAAG,EAAvB,CALuD;;EAQvD,QAAMC,eAAe,GAAG3B,MAAM,CAAC7W,MAAP,CAAc,UAAAoI,IAAI;EAAA,aACxC3L,UAAU,CAACuL,IAAX,CAAgB,UAAAsJ,GAAG;EAAA,eAAIA,GAAG,CAAC3V,EAAJ,KAAWyM,IAAI,CAACzM,EAApB;EAAA,OAAnB,CADwC;EAAA,KAAlB,CAAxB;;EAIA,QAAM8c,QAAQ,GAAG,SAAXA,QAAW,CAAArZ,IAAI,EAAI;EACvB;EACA;EACA;EACA,UAAMsZ,UAAU,GAAGjB,SAAS,CAC1BrY,IAD0B,EAE1BoZ,eAAe,CAACjd,GAAhB,CAAoB,UAAA6M,IAAI,EAAI;EAC1B;EACA,YAAMhO,MAAM,GAAGqC,UAAU,CAACuL,IAAX,CAAgB,UAAA5K,CAAC;EAAA,iBAAIA,CAAC,CAACzB,EAAF,KAASyM,IAAI,CAACzM,EAAlB;EAAA,SAAjB,CAAf;;EAEA,YAAI,CAACvB,MAAL,EAAa;EACX,gBAAM,IAAIjD,KAAJ,oDAC6CiR,IAAI,CAACzM,EADlD,oBAAN;EAGD;;EARyB,YAUlBya,QAVkB,GAULhc,MAVK,CAUlBgc,QAVkB;EAa1B;EACA;EACA;EACA;EACA;EACA;;EACA,YAAMuC,UAAU,GACd9Z,UAAU,CAACuX,QAAD,CAAV,IACA,CAACuB,aAAa,IAAI,EAAlB,EAAsBvB,QAAtB,CADA,IAEAwB,SAAS,CAACxB,QAAD,CAHX;;EAKA,YAAI,CAACuC,UAAL,EAAiB;EACf,gBAAM,IAAIxhB,KAAJ,uDACgDif,QADhD,sBACyEhO,IAAI,CAACzM,EAD9E,QAAN;EAGD,SA5ByB;EA+B1B;;;EACA,eAAO,UAACmD,CAAD,EAAImR,CAAJ;EAAA,iBAAU0I,UAAU,CAAC7Z,CAAD,EAAImR,CAAJ,EAAO7H,IAAI,CAACzM,EAAZ,EAAgByM,IAAI,CAAC2O,IAArB,CAApB;EAAA,SAAP;EACD,OAjCD,CAF0B;EAqC1ByB,MAAAA,eAAe,CAACjd,GAAhB,CAAoB,UAAA6M,IAAI,EAAI;EAC1B;EACA,YAAMhO,MAAM,GAAGqC,UAAU,CAACuL,IAAX,CAAgB,UAAA5K,CAAC;EAAA,iBAAIA,CAAC,CAACzB,EAAF,KAASyM,IAAI,CAACzM,EAAlB;EAAA,SAAjB,CAAf;;EAEA,YAAIvB,MAAM,IAAIA,MAAM,CAACwe,YAArB,EAAmC;EACjC,iBAAOxQ,IAAI,CAAC2O,IAAZ;EACD;;EAED,eAAO,CAAC3O,IAAI,CAAC2O,IAAb;EACD,OATD,CArC0B,CAA5B,CAJuB;;EAsDvB2B,MAAAA,UAAU,CAACphB,OAAX,CAAmB,UAAA0E,GAAG,EAAI;EACxBuc,QAAAA,cAAc,CAAC7a,IAAf,CAAoB1B,GAApB;;EACA,YAAI,CAACA,GAAG,CAAC8D,OAAL,IAAgB9D,GAAG,CAAC8D,OAAJ,CAAYnC,MAAZ,KAAuB,CAA3C,EAA8C;EAC5C;EACD;;EACD3B,QAAAA,GAAG,CAAC8D,OAAJ,GAAc2Y,QAAQ,CAACzc,GAAG,CAAC8D,OAAL,CAAtB;EACD,OAND;EAQA,aAAO4Y,UAAP;EACD,KA/DD;;EAiEA,WAAO,CAACD,QAAQ,CAACrZ,IAAD,CAAT,EAAiBmZ,cAAjB,CAAP;EACD,GA9EoC,EA8ElC,CACDV,YADC,EAEDhB,MAFC,EAGDzX,IAHC,EAIDqI,QAJC,EAKDhL,UALC,EAMDgb,SANC,EAODE,aAPC,CA9EkC,CArFR;EAAA,MAqFtBkB,UArFsB;EAAA,MAqFVN,cArFU;;EA6K7B,MAAMO,kBAAkB,GAAGzgB,YAAY,CAAC2f,eAAD,CAAvC;EAEAhf,EAAAA,sBAAsB,CAAC,YAAM;EAC3B,QAAI8f,kBAAkB,EAAtB,EAA0B;EACxBjT,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAACghB;EAAhB,OAAD,CAAR;EACD;EACF,GAJqB,EAInB,CAAC6B,YAAY,GAAG,IAAH,GAAU7Q,IAAvB,CAJmB,CAAtB;EAMAnM,EAAAA,MAAM,CAACsB,MAAP,CAAchC,QAAd,EAAwB;EACtB4e,IAAAA,aAAa,EAAE3Z,IADO;EAEtB4Z,IAAAA,iBAAiB,EAAEvR,QAFG;EAGtBoR,IAAAA,UAAU,EAAVA,UAHsB;EAItBN,IAAAA,cAAc,EAAdA,cAJsB;EAKtBnZ,IAAAA,IAAI,EAAEyZ,UALgB;EAMtBpR,IAAAA,QAAQ,EAAE8Q,cANY;EAOtBtC,IAAAA,SAAS,EAATA,SAPsB;EAQtBC,IAAAA,YAAY,EAAZA;EARsB,GAAxB;EAUD;;AAED,EAAO,SAASwB,gBAAT,CAA0B3Y,GAA1B,EAA+Bka,KAA/B,EAAsCC,IAAtC,EAA4C;EACjD,SAAO,UAAIna,GAAJ,EAASqJ,IAAT,CAAc,UAACsM,IAAD,EAAOC,IAAP,EAAgB;EACnC,SAAK,IAAI/V,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqa,KAAK,CAACtb,MAA1B,EAAkCiB,CAAC,IAAI,CAAvC,EAA0C;EACxC,UAAMua,MAAM,GAAGF,KAAK,CAACra,CAAD,CAApB;EACA,UAAMmY,IAAI,GAAGmC,IAAI,CAACta,CAAD,CAAJ,KAAY,KAAZ,IAAqBsa,IAAI,CAACta,CAAD,CAAJ,KAAY,MAA9C;EACA,UAAMwa,OAAO,GAAGD,MAAM,CAACzE,IAAD,EAAOC,IAAP,CAAtB;;EACA,UAAIyE,OAAO,KAAK,CAAhB,EAAmB;EACjB,eAAOrC,IAAI,GAAG,CAACqC,OAAJ,GAAcA,OAAzB;EACD;EACF;;EACD,WAAOF,IAAI,CAAC,CAAD,CAAJ,GAAUxE,IAAI,CAAC3S,KAAL,GAAa4S,IAAI,CAAC5S,KAA5B,GAAoC4S,IAAI,CAAC5S,KAAL,GAAa2S,IAAI,CAAC3S,KAA7D;EACD,GAVM,CAAP;EAWD;;ECzXD,IAAMpK,UAAU,GAAG,eAAnB;;EAGA3C,OAAO,CAACqkB,SAAR,GAAoB,WAApB;EACArkB,OAAO,CAACskB,QAAR,GAAmB,UAAnB;EACAtkB,OAAO,CAACukB,WAAR,GAAsB,aAAtB;AAEA,MAAaC,aAAa,GAAG,SAAhBA,aAAgB,CAAA7iB,KAAK,EAAI;EACpCA,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,aAAvB;EACD,CAHM;EAKPsW,aAAa,CAAC7hB,UAAd,GAA2BA,UAA3B;;EAEA,SAAS2M,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EACvD,MAAI8K,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACEwkB,MAAAA,QAAQ,EAAE,EADZ;EAEEC,MAAAA,SAAS,EAAE;EAFb,OAGKnV,KAHL;EAKD;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACqkB,SAA5B,EAAuC;EACrC,wBACK9U,KADL;EAEEmV,MAAAA,SAAS,EAAEvf,QAAQ,CAACgL,YAAT,CAAsBuU,SAAtB,IAAmC;EAFhD;EAID;;EAED,MAAIzU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACskB,QAA5B,EAAsC;EAAA,QAC5BK,SAD4B,GACRxf,QADQ,CAC5Bwf,SAD4B;EAAA,QACjBC,IADiB,GACRzf,QADQ,CACjByf,IADiB;EAEpC,QAAMC,YAAY,GAAG3hB,gBAAgB,CAAC+M,MAAM,CAACyU,SAAR,EAAmBnV,KAAK,CAACmV,SAAzB,CAArC;EACA,QAAII,WAAW,GAAG,KAAlB;;EAEA,QAAID,YAAY,GAAGtV,KAAK,CAACmV,SAAzB,EAAoC;EAClC;EACAI,MAAAA,WAAW,GACTH,SAAS,KAAK,CAAC,CAAf,GACIC,IAAI,CAACjc,MAAL,IAAe4G,KAAK,CAACkV,QADzB,GAEII,YAAY,GAAGF,SAHrB;EAID,KAND,MAMO,IAAIE,YAAY,GAAGtV,KAAK,CAACmV,SAAzB,EAAoC;EACzC;EACAI,MAAAA,WAAW,GAAGD,YAAY,GAAG,CAAC,CAA9B;EACD;;EAED,QAAI,CAACC,WAAL,EAAkB;EAChB,aAAOvV,KAAP;EACD;;EAED,wBACKA,KADL;EAEEmV,MAAAA,SAAS,EAAEG;EAFb;EAID;;EAED,MAAI5U,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACukB,WAA5B,EAAyC;EAAA,QAC/BE,QAD+B,GAClBxU,MADkB,CAC/BwU,QAD+B;EAEvC,QAAMM,WAAW,GAAGxV,KAAK,CAACkV,QAAN,GAAiBlV,KAAK,CAACmV,SAA3C;EACA,QAAMA,SAAS,GAAGhQ,IAAI,CAACqG,KAAL,CAAWgK,WAAW,GAAGN,QAAzB,CAAlB;EAEA,wBACKlV,KADL;EAEEmV,MAAAA,SAAS,EAATA,SAFF;EAGED,MAAAA,QAAQ,EAARA;EAHF;EAKD;EACF;;EAED,SAASvW,aAAT,CAAqB/I,QAArB,EAA+B;EAAA,MAE3BiF,IAF2B,GAqBzBjF,QArByB,CAE3BiF,IAF2B;EAAA,8BAqBzBjF,QArByB,CAG3B6f,aAH2B;EAAA,MAG3BA,aAH2B,sCAGX,IAHW;EAAA,8BAqBzB7f,QArByB,CAI3BkF,iBAJ2B;EAAA,MAI3BA,iBAJ2B,sCAIP,UAJO;EAAA,MAK3B5H,OAL2B,GAqBzB0C,QArByB,CAK3B1C,OAL2B;EAAA,MAMhBwiB,aANgB,GAqBzB9f,QArByB,CAM3Bwf,SAN2B;EAAA,8BAqBzBxf,QArByB,CAO3BiR,oBAP2B;EAAA,MAO3BA,oBAP2B,sCAOJ,IAPI;EAAA,8BAqBzBjR,QArByB,CAQ3BoF,aAR2B;EAAA,MAQ3BA,aAR2B,sCAQX,IARW;EAAA,wBAqBzBpF,QArByB,CAS3BoK,KAT2B;EAAA,MAUzBkV,QAVyB,mBAUzBA,QAVyB;EAAA,MAWzBC,SAXyB,mBAWzBA,SAXyB;EAAA,MAYzBpa,QAZyB,mBAYzBA,QAZyB;EAAA,MAazBmP,YAbyB,mBAazBA,YAbyB;EAAA,MAczB1B,OAdyB,mBAczBA,OAdyB;EAAA,MAezBiE,OAfyB,mBAezBA,OAfyB;EAAA,MAgBzB6F,MAhByB,mBAgBzBA,MAhByB;EAAA,MAkB3BhR,QAlB2B,GAqBzB1L,QArByB,CAkB3B0L,QAlB2B;EAAA,MAmB3BmB,IAnB2B,GAqBzB7M,QArByB,CAmB3B6M,IAnB2B;EAAA,MAoB3BkT,gBApB2B,GAqBzB/f,QArByB,CAoB3B+f,gBApB2B;EAuB7B1iB,EAAAA,iBAAiB,CACfC,OADe,EAEf,CAAC,iBAAD,EAAoB,YAApB,EAAkC,YAAlC,EAAgD,WAAhD,EAA6D,aAA7D,CAFe,EAGf,eAHe,CAAjB;EAMA,MAAM0iB,gBAAgB,GAAG9hB,YAAY,CAAC2hB,aAAD,CAArC;EAEAhhB,EAAAA,sBAAsB,CAAC,YAAM;EAC3B,QAAImhB,gBAAgB,EAApB,EAAwB;EACtBtU,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAACqkB;EAAhB,OAAD,CAAR;EACD;EACF,GAJqB,EAInB,CACDxT,QADC,EAEDqU,gBAAgB,GAAG,IAAH,GAAUlT,IAFzB,EAGDyH,YAHC,EAID1B,OAJC,EAKDiE,OALC,EAMD6F,MANC,CAJmB,CAAtB;EAaA,MAAM8C,SAAS,GAAGO,gBAAgB,GAC9BD,aAD8B,GAE9BvQ,IAAI,CAAC0Q,IAAL,CAAUhb,IAAI,CAACzB,MAAL,GAAc8b,QAAxB,CAFJ;EAIA,MAAMY,WAAW,GAAG7hB,KAAK,CAACgP,OAAN,CAClB;EAAA,WACEmS,SAAS,GAAG,CAAZ,GACI,UAAI,IAAInjB,KAAJ,CAAUmjB,SAAV,CAAJ,EAA0BW,IAA1B,CAA+B,IAA/B,EAAqC/e,GAArC,CAAyC,UAAC6B,CAAD,EAAIwB,CAAJ;EAAA,aAAUA,CAAV;EAAA,KAAzC,CADJ,GAEI,EAHN;EAAA,GADkB,EAKlB,CAAC+a,SAAD,CALkB,CAApB;EAQA,MAAMC,IAAI,GAAGphB,KAAK,CAACgP,OAAN,CAAc,YAAM;EAC/B,QAAIoS,IAAJ;;EAEA,QAAIM,gBAAJ,EAAsB;EACpBN,MAAAA,IAAI,GAAGxa,IAAP;EACD,KAFD,MAEO;EACL,UAAMmb,SAAS,GAAGd,QAAQ,GAAGC,SAA7B;EACA,UAAMc,OAAO,GAAGD,SAAS,GAAGd,QAA5B;EAEAG,MAAAA,IAAI,GAAGxa,IAAI,CAACqb,KAAL,CAAWF,SAAX,EAAsBC,OAAtB,CAAP;EACD;;EAED,QAAIpP,oBAAJ,EAA0B;EACxB,aAAOwO,IAAP;EACD;;EAED,WAAOza,UAAU,CAACya,IAAD,EAAO;EAAEva,MAAAA,iBAAiB,EAAjBA,iBAAF;EAAqBC,MAAAA,QAAQ,EAARA,QAArB;EAA+BC,MAAAA,aAAa,EAAbA;EAA/B,KAAP,CAAjB;EACD,GAjBY,EAiBV,CACDA,aADC,EAEDD,QAFC,EAGDD,iBAHC,EAID6a,gBAJC,EAKDR,SALC,EAMDD,QANC,EAODrO,oBAPC,EAQDhM,IARC,CAjBU,CAAb;EA4BA,MAAMsb,eAAe,GAAGhB,SAAS,GAAG,CAApC;EACA,MAAMiB,WAAW,GACfhB,SAAS,KAAK,CAAC,CAAf,GAAmBC,IAAI,CAACjc,MAAL,IAAe8b,QAAlC,GAA6CC,SAAS,GAAGC,SAAS,GAAG,CADvE;EAGA,MAAML,QAAQ,GAAG9gB,KAAK,CAACG,WAAN,CACf,UAAA+gB,SAAS,EAAI;EACX7T,IAAAA,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACskB,QAAhB;EAA0BI,MAAAA,SAAS,EAATA;EAA1B,KAAD,CAAR;EACD,GAHc,EAIf,CAAC7T,QAAD,CAJe,CAAjB;EAOA,MAAM+U,YAAY,GAAGpiB,KAAK,CAACG,WAAN,CAAkB,YAAM;EAC3C,WAAO2gB,QAAQ,CAAC,UAAAlhB,GAAG;EAAA,aAAIA,GAAG,GAAG,CAAV;EAAA,KAAJ,CAAf;EACD,GAFoB,EAElB,CAACkhB,QAAD,CAFkB,CAArB;EAIA,MAAMuB,QAAQ,GAAGriB,KAAK,CAACG,WAAN,CAAkB,YAAM;EACvC,WAAO2gB,QAAQ,CAAC,UAAAlhB,GAAG;EAAA,aAAIA,GAAG,GAAG,CAAV;EAAA,KAAJ,CAAf;EACD,GAFgB,EAEd,CAACkhB,QAAD,CAFc,CAAjB;EAIA,MAAMC,WAAW,GAAG/gB,KAAK,CAACG,WAAN,CAClB,UAAA8gB,QAAQ,EAAI;EACV5T,IAAAA,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACukB,WAAhB;EAA6BE,MAAAA,QAAQ,EAARA;EAA7B,KAAD,CAAR;EACD,GAHiB,EAIlB,CAAC5T,QAAD,CAJkB,CAApB;EAOAhL,EAAAA,MAAM,CAACsB,MAAP,CAAchC,QAAd,EAAwB;EACtBkgB,IAAAA,WAAW,EAAXA,WADsB;EAEtBV,IAAAA,SAAS,EAATA,SAFsB;EAGtBC,IAAAA,IAAI,EAAJA,IAHsB;EAItBc,IAAAA,eAAe,EAAfA,eAJsB;EAKtBC,IAAAA,WAAW,EAAXA,WALsB;EAMtBrB,IAAAA,QAAQ,EAARA,QANsB;EAOtBsB,IAAAA,YAAY,EAAZA,YAPsB;EAQtBC,IAAAA,QAAQ,EAARA,QARsB;EAStBtB,IAAAA,WAAW,EAAXA;EATsB,GAAxB;EAWD;;EC/LDvkB,OAAO,CAAC8lB,UAAR,GAAqB,YAArB;EACA9lB,OAAO,CAAC+lB,WAAR,GAAsB,aAAtB;AAEA,MAAaC,yBAAyB,GAAG,SAA5BA,yBAA4B,CAAArkB,KAAK,EAAI;EAChDA,EAAAA,KAAK,CAACskB,mBAAN,GAA4B,CAACC,0BAAD,CAA5B;EACAvkB,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACkM,oBAAN,CAA2BnF,IAA3B,CAAgCmF,oBAAhC;EACAlM,EAAAA,KAAK,CAAC8F,UAAN,CAAiBiB,IAAjB,CAAsBjB,UAAtB;EACA9F,EAAAA,KAAK,CAAC+L,WAAN,CAAkBhF,IAAlB,CAAuBgF,WAAvB;EACA/L,EAAAA,KAAK,CAACgM,mBAAN,CAA0BjF,IAA1B,CAA+BiF,mBAA/B;EACAhM,EAAAA,KAAK,CAACiM,uBAAN,CAA8BlF,IAA9B,CAAmCkF,uBAAnC;EACAjM,EAAAA,KAAK,CAACmM,cAAN,CAAqBpF,IAArB,CAA0BoF,gBAA1B;EACAnM,EAAAA,KAAK,CAACoM,kBAAN,CAAyBrF,IAAzB,CAA8BqF,kBAA9B;EACApM,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,aAAvB;EACAvM,EAAAA,KAAK,CAACwM,UAAN,CAAiBzF,IAAjB,CAAsByF,YAAtB;EACD,CAZM;EAcP6X,yBAAyB,CAACrjB,UAA1B,GAAuC,iBAAvC;EAEA,IAAMwjB,mBAAmB,GAAG,EAA5B;;EAEA,IAAMD,0BAA0B,GAAG,SAA7BA,0BAA6B,CAACnlB,KAAD;EAAA,MAAUoT,MAAV,QAAUA,MAAV;EAAA,SAAuB,CACxDpT,KADwD,EAExD;EACE4U,IAAAA,OAAO,EAAExB,MAAM,CAACiS,QAAP,GACL,UAAA1c,CAAC,EAAI;EACHA,MAAAA,CAAC,CAACwS,OAAF;EACA/H,MAAAA,MAAM,CAAC4R,WAAP;EACD,KAJI,GAKLzT,SANN;EAOErR,IAAAA,KAAK,EAAE;EACLuI,MAAAA,MAAM,EAAE2K,MAAM,CAACiS,QAAP,GAAkB,SAAlB,GAA8B9T;EADjC,KAPT;EAUExC,IAAAA,KAAK,EAAE;EAVT,GAFwD,CAAvB;EAAA,CAAnC;;;EAiBA,SAASR,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EACvD,MAAI8K,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACEomB,MAAAA,YAAY,EAAEF;EADhB,OAEK5W,KAFL;EAID;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC8lB,UAA5B,EAAwC;EACtC,wBACKvW,KADL;EAEE8W,MAAAA,YAAY,EAAElhB,QAAQ,CAACgL,YAAT,CAAsBkW,YAAtB,IAAsCF;EAFtD;EAID;;EAED,MAAIlW,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC+lB,WAA5B,EAAyC;EAAA,QAC/B1V,QAD+B,GACDJ,MADC,CAC/BI,QAD+B;EAAA,QACdiW,QADc,GACDrW,MADC,CACrB9P,KADqB;EAGvC,QAAMomB,aAAa,GACjB,OAAOD,QAAP,KAAoB,WAApB,GACIA,QADJ,GAEI,CAAC/W,KAAK,CAAC8W,YAAN,CAAmBpgB,QAAnB,CAA4BoK,QAA5B,CAHP;;EAKA,QAAIkW,aAAJ,EAAmB;EACjB,0BACKhX,KADL;EAEE8W,QAAAA,YAAY,YAAM9W,KAAK,CAAC8W,YAAZ,GAA0BhW,QAA1B;EAFd;EAID;;EAED,wBACKd,KADL;EAEE8W,MAAAA,YAAY,EAAE9W,KAAK,CAAC8W,YAAN,CAAmBrb,MAAnB,CAA0B,UAAA5C,CAAC;EAAA,eAAIA,CAAC,KAAKiI,QAAV;EAAA,OAA3B;EAFhB;EAID;EACF;;EAED,SAASxC,oBAAT,CAA8B1I,QAA9B,EAAwC;EACtCA,EAAAA,QAAQ,CAACsC,UAAT,CAAoBnF,OAApB,CAA4B,UAAA8C,MAAM,EAAI;EACpCA,IAAAA,MAAM,CAACohB,aAAP,GAAuBrhB,QAAQ,CAACoK,KAAT,CAAe8W,YAAf,CAA4BpgB,QAA5B,CAAqCb,MAAM,CAACuB,EAA5C,CAAvB;EACD,GAFD;EAGD;;EAED,SAASc,UAAT,CAAoBrB,OAApB,SAA2C;EAAA,MAAZjB,QAAY,SAAZA,QAAY;EACzCiB,EAAAA,OAAO,CAAC9D,OAAR,CAAgB,UAAA8C,MAAM,EAAI;EACxBA,IAAAA,MAAM,CAACohB,aAAP,GAAuBrhB,QAAQ,CAACoK,KAAT,CAAe8W,YAAf,CAA4BpgB,QAA5B,CAAqCb,MAAM,CAACuB,EAA5C,CAAvB;EACAvB,IAAAA,MAAM,CAACqhB,YAAP,GAAsB,IAAIrL,GAAJ,EAAtB;EACD,GAHD;EAIA,SAAOhV,OAAP;EACD;;EAED,SAASsH,WAAT,CAAqBvN,KAArB,SAAwC;EAAA,MAAViF,MAAU,SAAVA,MAAU;;EACtC,MAAIA,MAAM,CAACqhB,YAAP,IAAuB,OAAOtmB,KAAP,KAAiB,WAA5C,EAAyD;EACvDiF,IAAAA,MAAM,CAACqhB,YAAP,CAAoBC,GAApB,CAAwBvmB,KAAxB;EACD;;EACD,SAAOA,KAAP;EACD;;EAED,SAASwN,mBAAT,CAA6BgZ,YAA7B,SAAyD;EAAA,MAAZxhB,QAAY,SAAZA,QAAY;EAAA,MAC/CsC,UAD+C,GACzBtC,QADyB,CAC/CsC,UAD+C;EAAA,MACnC8H,KADmC,GACzBpK,QADyB,CACnCoK,KADmC;;EAGvD,MAAI,CAACA,KAAK,CAAC8W,YAAN,CAAmB1d,MAApB,IAA8B,CAAC4G,KAAK,CAACyM,OAArC,IAAgD,CAACzM,KAAK,CAACyM,OAAN,CAAcrT,MAAnE,EAA2E;EACzE,WAAOge,YAAP;EACD;;EAED,MAAMN,YAAY,GAAG9W,KAAK,CAAC8W,YAAN,CAClB9f,GADkB,CACd,UAAAI,EAAE;EAAA,WAAIc,UAAU,CAACuL,IAAX,CAAgB,UAAA5K,CAAC;EAAA,aAAIA,CAAC,CAACzB,EAAF,KAASA,EAAb;EAAA,KAAjB,CAAJ;EAAA,GADY,EAElBqE,MAFkB,CAEX+G,OAFW,CAArB;EAIA,MAAM6U,aAAa,GAAGnf,UAAU,CAACuD,MAAX,CACpB,UAAA5C,CAAC;EAAA,WACC,CAACA,CAAC,CAACoe,aAAH,IACA,CAACjX,KAAK,CAACyM,OAAN,CAAc/V,QAAd,CAAuBmC,CAAC,CAACzB,EAAzB,CADD,IAEA,CAAC4I,KAAK,CAAC8W,YAAN,CAAmBpgB,QAAnB,CAA4BmC,CAAC,CAACzB,EAA9B,CAHF;EAAA,GADmB,CAAtB;;EAOA,MAAMkgB,iBAAiB,GAAG,SAApBA,iBAAoB,CAACvgB,KAAD,EAAYD,MAAZ,EAAoBygB,YAApB,EAA0C;EAAA,QAAzCxgB,KAAyC;EAAzCA,MAAAA,KAAyC,GAAjC,CAAiC;EAAA;;EAAA,QAAtBwgB,YAAsB;EAAtBA,MAAAA,YAAsB,GAAP,EAAO;EAAA;;EAClE,QAAMC,WAAW,GAAGV,YAAY,CAAC/f,KAAD,CAAhC;;EAEA,QAAI,CAACygB,WAAL,EAAkB;EAChB,aAAOH,aAAa,CAACrgB,GAAd,CAAkB,UAAAygB,YAAY,EAAI;EACvC;EACA;EACA;EACA;EACA;EAEA,4BACKA,YADL;EAEEZ,UAAAA,QAAQ,EAAE,KAFZ;EAGEa,UAAAA,SAAS,EAAE,IAHb;EAIE5gB,UAAAA,MAAM,EAANA,MAJF;EAKEC,UAAAA,KAAK,EAAEA,KALT;EAMEK,UAAAA,EAAE,QAAKN,MAAM,GAAMA,MAAM,CAACM,EAAb,SAAmBqgB,YAAY,CAACrgB,EAAhC,GAAuCqgB,YAAY,CAACrgB,EAA/D,CANJ;EAOEC,UAAAA,QAAQ,EAAE,kBAACmO,WAAD,EAAcnL,CAAd,EAAiB5C,GAAjB,EAAyB;EACjC,gBAAI8f,YAAY,CAAC3P,KAAb,CAAmB,UAAAnM,MAAM;EAAA,qBAAIA,MAAM,CAAChE,GAAD,CAAV;EAAA,aAAzB,CAAJ,EAA+C;EAC7C,qBAAOA,GAAG,CAAC4M,MAAJ,CAAWoT,YAAY,CAACrgB,EAAxB,CAAP;EACD;EACF;EAXH;EAaD,OApBM,CAAP;EAqBD;;EAED,QAAM8f,YAAY,GAAGjlB,KAAK,CAAC2Z,IAAN,CAAW4L,WAAW,CAACN,YAAvB,EAAqCrT,IAArC,EAArB;EAEA,WAAOqT,YAAY,CAAClgB,GAAb,CAAiB,UAAA2gB,WAAW,EAAI;EACrC,UAAMC,WAAW,gBACZJ,WADY;EAEflgB,QAAAA,MAAM,EACJkgB,WAAW,CAACK,WAAZ,IAA2B,OAAOL,WAAW,CAAC5S,MAAnB,KAA8B,QAAzD,GACO4S,WAAW,CAAClgB,MADnB,UAC8BqgB,WAD9B,GAEIA,WALS;EAMfG,QAAAA,YAAY,EAAE,IANC;EAOfhhB,QAAAA,MAAM,EAANA,MAPe;EAQfC,QAAAA,KAAK,EAALA,KARe;EASfK,QAAAA,EAAE,EAAEN,MAAM,GACHA,MAAM,CAACM,EADJ,SACUogB,WAAW,CAACpgB,EADtB,SAC4BugB,WAD5B,GAEHH,WAAW,CAACpgB,EAFT,SAEeugB,WAXV;EAYfI,QAAAA,UAAU,EAAEJ;EAZG,QAAjB;;EAeAC,MAAAA,WAAW,CAAC/gB,OAAZ,GAAsBygB,iBAAiB,CAACvgB,KAAK,GAAG,CAAT,EAAY6gB,WAAZ,YAClCL,YADkC,GAErC,UAAA9f,GAAG;EAAA,eAAIA,GAAG,CAAC4M,MAAJ,CAAWmT,WAAW,CAACpgB,EAAvB,MAA+BugB,WAAnC;EAAA,OAFkC,GAAvC;EAKA,aAAOC,WAAP;EACD,KAtBM,CAAP;EAuBD,GApDD;;EAsDA,MAAMI,eAAe,GAAG9gB,cAAc,CAACogB,iBAAiB,EAAlB,CAAtC;EAEA,mBAAWF,YAAX,EAA4BY,eAA5B;EACD;;EAED,SAAS3Z,uBAAT,CACE1J,IADF,SAOE;EAAA,mCAJEiB,QAIF,CAHIoK,KAGJ;EAAA,MAHa8W,YAGb,wBAHaA,YAGb;EAAA,MAH2BrK,OAG3B,wBAH2BA,OAG3B;EACA,mBAAW9X,IAAX,GAAiBmiB,YAAjB,EAA+BrK,OAA/B;EACD;;EAED,SAASlO,gBAAT,CAAwBA,cAAxB,SAAiE;EAAA,MAAXyB,KAAW,SAAvBpK,QAAuB,CAAXoK,KAAW;EAC/DzB,EAAAA,cAAc,GAAGA,cAAc,CAAC9C,MAAf,CAAsB,UAAA5C,CAAC;EAAA,WAAI,CAACA,CAAC,CAACoe,aAAP;EAAA,GAAvB,CAAjB;;EAEA,MAAIjX,KAAK,CAAC8W,YAAN,CAAmB1d,MAAnB,IAA6B4G,KAAK,CAACyM,OAAnC,IAA8CzM,KAAK,CAACyM,OAAN,CAAcrT,MAAhE,EAAwE;EACtEmF,IAAAA,cAAc,GAAGA,cAAc,CAAC9C,MAAf,CACf,UAAA5F,MAAM;EAAA,aAAIA,MAAM,CAACoX,SAAP,IAAoBpX,MAAM,CAAC6hB,SAA/B;EAAA,KADS,CAAjB;EAGD;;EAED,SAAOnZ,cAAP;EACD;;EAED,SAASC,kBAAT,CAA4B7J,IAA5B,SAAgD;EAAA,MAAZiB,QAAY,SAAZA,QAAY;EAC9C,mBAAWjB,IAAX,GAAiBiB,QAAQ,CAACoK,KAAT,CAAe8W,YAAhC,EAA8ClhB,QAAQ,CAACoK,KAAT,CAAeyM,OAA7D;EACD;;EAED,SAAS9N,aAAT,CAAqB/I,QAArB,EAA+B;EAAA,MAE3BiB,OAF2B,GAczBjB,QAdyB,CAE3BiB,OAF2B;EAAA,MAG3BqB,UAH2B,GAczBtC,QAdyB,CAG3BsC,UAH2B;EAAA,MAI3BmJ,WAJ2B,GAczBzL,QAdyB,CAI3ByL,WAJ2B;EAAA,MAO3BE,QAP2B,GAczB3L,QAdyB,CAO3B2L,QAP2B;EAAA,MAQ3BrO,OAR2B,GAczB0C,QAdyB,CAQ3B1C,OAR2B;EAAA,MAS3BoO,QAT2B,GAczB1L,QAdyB,CAS3B0L,QAT2B;EAAA,8BAczB1L,QAdyB,CAU3BqiB,cAV2B;EAAA,MAU3BA,cAV2B,sCAUV,IAVU;EAAA,MAW3BC,WAX2B,GAczBtiB,QAdyB,CAW3BsiB,WAX2B;EAAA,MAY3BC,YAZ2B,GAczBviB,QAdyB,CAY3BuiB,YAZ2B;EAAA,MAa3BC,eAb2B,GAczBxiB,QAdyB,CAa3BwiB,eAb2B;EAgB7BnlB,EAAAA,iBAAiB,CAACC,OAAD,EAAU,CAAC,YAAD,CAAV,EAA0B,iBAA1B,CAAjB;EAEA,MAAMuO,WAAW,GAAG3N,YAAY,CAAC8B,QAAD,CAAhC;EAEAsC,EAAAA,UAAU,CAACnF,OAAX,CAAmB,UAAA8C,MAAM,EAAI;EAAA,QAEzBwB,QAFyB,GAKvBxB,MALuB,CAEzBwB,QAFyB;EAAA,QAGXghB,kBAHW,GAKvBxiB,MALuB,CAGzByiB,YAHyB;EAAA,QAIXC,kBAJW,GAKvB1iB,MALuB,CAIzBsiB,YAJyB;EAO3BtiB,IAAAA,MAAM,CAACghB,QAAP,GAAkBxf,QAAQ,GACtB+C,eAAe,CACbvE,MAAM,CAACghB,QADM,EAEb0B,kBAAkB,KAAK,IAAvB,GAA8B,KAA9B,GAAsCxV,SAFzB,EAGboV,YAAY,KAAK,IAAjB,GAAwB,KAAxB,GAAgCpV,SAHnB,EAIb,IAJa,CADO,GAOtB3I,eAAe,CACbvE,MAAM,CAACghB,QADM,EAEbwB,kBAFa,EAGbD,eAHa,EAIb,KAJa,CAPnB;;EAcA,QAAIviB,MAAM,CAACghB,QAAX,EAAqB;EACnBhhB,MAAAA,MAAM,CAAC2gB,WAAP,GAAqB;EAAA,eAAM5gB,QAAQ,CAAC4gB,WAAT,CAAqB3gB,MAAM,CAACuB,EAA5B,CAAN;EAAA,OAArB;EACD;;EAEDvB,IAAAA,MAAM,CAACmY,UAAP,GAAoBnY,MAAM,CAACmY,UAAP,IAAqBnY,MAAM,CAAC9E,IAAhD;EACD,GA1BD;;EA4BA,MAAMylB,WAAW,GAAG,SAAdA,WAAc,CAAC1V,QAAD,EAAWlQ,KAAX,EAAqB;EACvC0Q,IAAAA,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAAC+lB,WAAhB;EAA6B1V,MAAAA,QAAQ,EAARA,QAA7B;EAAuClQ,MAAAA,KAAK,EAALA;EAAvC,KAAD,CAAR;EACD,GAFD;;EAIAyQ,EAAAA,WAAW,CAACtO,OAAZ,CAAoB,UAAA6R,MAAM,EAAI;EAC5BA,IAAAA,MAAM,CAAC8R,mBAAP,GAA6BvkB,cAAc,CACzCoP,QAAQ,GAAGmV,mBAD8B,EAEzC;EACE9gB,MAAAA,QAAQ,EAAE6L,WAAW,EADvB;EAEEmD,MAAAA,MAAM,EAANA;EAFF,KAFyC,CAA3C;EAOD,GARD;EAUA,MAAM4T,iBAAiB,GAAG1kB,YAAY,CAACmkB,cAAD,CAAtC;EAEAxjB,EAAAA,sBAAsB,CAAC,YAAM;EAC3B,QAAI+jB,iBAAiB,EAArB,EAAyB;EACvBlX,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAAC8lB;EAAhB,OAAD,CAAR;EACD;EACF,GAJqB,EAInB,CAACjV,QAAD,EAAW4W,WAAW,GAAG,IAAH,GAAUrhB,OAAhC,CAJmB,CAAtB;EAMAP,EAAAA,MAAM,CAACsB,MAAP,CAAchC,QAAd,EAAwB;EACtB4gB,IAAAA,WAAW,EAAXA;EADsB,GAAxB;EAGD;;EAED,SAAS5X,YAAT,CAAoBnH,GAApB,EAAyB;EACvBA,EAAAA,GAAG,CAAC2M,QAAJ,CAAarR,OAAb,CAAqB,UAAA6K,IAAI,EAAI;EAC3B;EACAA,IAAAA,IAAI,CAAC8Z,SAAL,GAAiB9Z,IAAI,CAAC/H,MAAL,CAAY6hB,SAA7B;EACD,GAHD;EAID;;EC3RD,IAAMtkB,YAAU,GAAG,cAAnB;;EAGA3C,OAAO,CAACgoB,iBAAR,GAA4B,mBAA5B;EACAhoB,OAAO,CAACioB,qBAAR,GAAgC,uBAAhC;EACAjoB,OAAO,CAACkoB,iBAAR,GAA4B,mBAA5B;EACAloB,OAAO,CAACmoB,yBAAR,GAAoC,2BAApC;AAEA,MAAaC,YAAY,GAAG,SAAfA,YAAe,CAAAzmB,KAAK,EAAI;EACnCA,EAAAA,KAAK,CAAC0mB,yBAAN,GAAkC,CAACC,gCAAD,CAAlC;EACA3mB,EAAAA,KAAK,CAAC4mB,6BAAN,GAAsC,CAACC,oCAAD,CAAtC;EACA7mB,EAAAA,KAAK,CAAC8mB,iCAAN,GAA0C,CACxCC,wCADwC,CAA1C;EAGA/mB,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,aAAvB;EACAvM,EAAAA,KAAK,CAACwM,UAAN,CAAiBzF,IAAjB,CAAsByF,YAAtB;EACD,CATM;EAWPia,YAAY,CAACzlB,UAAb,GAA0BA,YAA1B;;EAEA,IAAM2lB,gCAAgC,GAAG,SAAnCA,gCAAmC,CAACvnB,KAAD,QAA8B;EAAA,MAApBoE,QAAoB,QAApBA,QAAoB;EAAA,MAAV6B,GAAU,QAAVA,GAAU;EAAA,8BACrB7B,QADqB,CAC7DwjB,oBAD6D;EAAA,MAC7DA,oBAD6D,sCACtC,YADsC;EAErE,MAAI/Y,OAAO,GAAG,KAAd;;EAEA,MAAI5I,GAAG,CAAC4D,QAAJ,IAAgB5D,GAAG,CAAC4D,QAAJ,CAAa+d,oBAAb,CAApB,EAAwD;EACtD/Y,IAAAA,OAAO,GAAG,IAAV;EACD,GAFD,MAEO;EACLA,IAAAA,OAAO,GAAG5I,GAAG,CAAC4hB,UAAd;EACD;;EAED,SAAO,CACL7nB,KADK,EAEL;EACE0O,IAAAA,QAAQ,EAAE,kBAAA/F,CAAC,EAAI;EACb1C,MAAAA,GAAG,CAACkhB,iBAAJ,CAAsBxe,CAAC,CAACiG,MAAF,CAASC,OAA/B;EACD,KAHH;EAIE3O,IAAAA,KAAK,EAAE;EACLuI,MAAAA,MAAM,EAAE;EADH,KAJT;EAOEoG,IAAAA,OAAO,EAAPA,OAPF;EAQEE,IAAAA,KAAK,EAAE,qBART;EASEE,IAAAA,aAAa,EAAEhJ,GAAG,CAAC6hB;EATrB,GAFK,CAAP;EAcD,CAxBD;;EA0BA,IAAML,oCAAoC,GAAG,SAAvCA,oCAAuC,CAACznB,KAAD;EAAA,MAAUoE,QAAV,SAAUA,QAAV;EAAA,SAAyB,CACpEpE,KADoE,EAEpE;EACE0O,IAAAA,QAAQ,EAAE,kBAAA/F,CAAC,EAAI;EACbvE,MAAAA,QAAQ,CAAC8iB,qBAAT,CAA+Bve,CAAC,CAACiG,MAAF,CAASC,OAAxC;EACD,KAHH;EAIE3O,IAAAA,KAAK,EAAE;EACLuI,MAAAA,MAAM,EAAE;EADH,KAJT;EAOEoG,IAAAA,OAAO,EAAEzK,QAAQ,CAAC2jB,iBAPpB;EAQEhZ,IAAAA,KAAK,EAAE,0BART;EASEE,IAAAA,aAAa,EAAE+B,OAAO,CACpB,CAAC5M,QAAQ,CAAC2jB,iBAAV,IACEjjB,MAAM,CAACgQ,IAAP,CAAY1Q,QAAQ,CAACoK,KAAT,CAAewZ,cAA3B,EAA2CpgB,MAFzB;EATxB,GAFoE,CAAzB;EAAA,CAA7C;;EAkBA,IAAM+f,wCAAwC,GAAG,SAA3CA,wCAA2C,CAAC3nB,KAAD;EAAA,MAAUoE,QAAV,SAAUA,QAAV;EAAA,SAAyB,CACxEpE,KADwE,EAExE;EACE0O,IAAAA,QADF,oBACW/F,CADX,EACc;EACVvE,MAAAA,QAAQ,CAACgjB,yBAAT,CAAmCze,CAAC,CAACiG,MAAF,CAASC,OAA5C;EACD,KAHH;EAIE3O,IAAAA,KAAK,EAAE;EACLuI,MAAAA,MAAM,EAAE;EADH,KAJT;EAOEoG,IAAAA,OAAO,EAAEzK,QAAQ,CAAC6jB,qBAPpB;EAQElZ,IAAAA,KAAK,EAAE,uCART;EASEE,IAAAA,aAAa,EAAE+B,OAAO,CACpB,CAAC5M,QAAQ,CAAC6jB,qBAAV,IACE7jB,QAAQ,CAACyf,IAAT,CAAczc,IAAd,CAAmB;EAAA,UAAGxB,EAAH,SAAGA,EAAH;EAAA,aAAYxB,QAAQ,CAACoK,KAAT,CAAewZ,cAAf,CAA8BpiB,EAA9B,CAAZ;EAAA,KAAnB,CAFkB;EATxB,GAFwE,CAAzB;EAAA,CAAjD;;;EAmBA,SAAS2I,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EACvD,MAAI8K,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACE8oB,MAAAA,cAAc,EAAE;EADlB,OAEKxZ,KAFL;EAID;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACgoB,iBAA5B,EAA+C;EAC7C,wBACKzY,KADL;EAEEwZ,MAAAA,cAAc,EAAE5jB,QAAQ,CAACgL,YAAT,CAAsB4Y,cAAtB,IAAwC;EAF1D;EAID;;EAED,MAAI9Y,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACioB,qBAA5B,EAAmD;EAAA,QAClCgB,WADkC,GAClBhZ,MADkB,CACzC9P,KADyC;EAAA,QAG/C2oB,iBAH+C,GAM7C3jB,QAN6C,CAG/C2jB,iBAH+C;EAAA,QAI/CpW,QAJ+C,GAM7CvN,QAN6C,CAI/CuN,QAJ+C;EAAA,gCAM7CvN,QAN6C,CAK/CqZ,kBAL+C;EAAA,QAK/CA,kBAL+C,sCAK1B9L,QAL0B;EAQjD,QAAMwW,SAAS,GACb,OAAOD,WAAP,KAAuB,WAAvB,GAAqCA,WAArC,GAAmD,CAACH,iBADtD,CARiD;EAYjD;;EACA,QAAMC,cAAc,GAAGljB,MAAM,CAACsB,MAAP,CAAc,EAAd,EAAkBoI,KAAK,CAACwZ,cAAxB,CAAvB;;EAEA,QAAIG,SAAJ,EAAe;EACbrjB,MAAAA,MAAM,CAACgQ,IAAP,CAAY2I,kBAAZ,EAAgClc,OAAhC,CAAwC,UAAAyT,KAAK,EAAI;EAC/CgT,QAAAA,cAAc,CAAChT,KAAD,CAAd,GAAwB,IAAxB;EACD,OAFD;EAGD,KAJD,MAIO;EACLlQ,MAAAA,MAAM,CAACgQ,IAAP,CAAY2I,kBAAZ,EAAgClc,OAAhC,CAAwC,UAAAyT,KAAK,EAAI;EAC/C,eAAOgT,cAAc,CAAChT,KAAD,CAArB;EACD,OAFD;EAGD;;EAED,wBACKxG,KADL;EAEEwZ,MAAAA,cAAc,EAAdA;EAFF;EAID;;EAED,MAAI9Y,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACkoB,iBAA5B,EAA+C;EAAA,QACrCvhB,EADqC,GACVsJ,MADU,CACrCtJ,EADqC;EAAA,QAC1BsiB,YAD0B,GACVhZ,MADU,CACjC9P,KADiC;EAAA,QAErCuS,SAFqC,GAEUvN,QAFV,CAErCuN,QAFqC;EAAA,gCAEUvN,QAFV,CAE3BgkB,aAF2B;EAAA,QAE3BA,aAF2B,sCAEX,IAFW;EAAA,QAELzX,UAFK,GAEUvM,QAFV,CAELuM,UAFK;EAG7C,QAAMkX,UAAU,GAAGrZ,KAAK,CAACwZ,cAAN,CAAqBpiB,EAArB,CAAnB;EACA,QAAMuP,WAAW,GACf,OAAO+S,YAAP,KAAuB,WAAvB,GAAqCA,YAArC,GAAmD,CAACL,UADtD;;EAGA,QAAIA,UAAU,KAAK1S,WAAnB,EAAgC;EAC9B,aAAO3G,KAAP;EACD;;EAED,QAAM6Z,iBAAiB,gBAAQ7Z,KAAK,CAACwZ,cAAd,CAAvB;;EAEA,QAAMM,aAAa,GAAG,SAAhBA,aAAgB,CAAA1iB,EAAE,EAAI;EAC1B,UAAMK,GAAG,GAAG0L,SAAQ,CAAC/L,EAAD,CAApB;;EAEA,UAAIK,GAAJ,EAAS;EACP,YAAI,CAACA,GAAG,CAACwV,SAAT,EAAoB;EAClB,cAAItG,WAAJ,EAAiB;EACfkT,YAAAA,iBAAiB,CAACziB,EAAD,CAAjB,GAAwB,IAAxB;EACD,WAFD,MAEO;EACL,mBAAOyiB,iBAAiB,CAACziB,EAAD,CAAxB;EACD;EACF;;EAED,YAAIwiB,aAAa,IAAIzX,UAAU,CAAC1K,GAAD,CAA/B,EAAsC;EACpC,iBAAO0K,UAAU,CAAC1K,GAAD,CAAV,CAAgB1E,OAAhB,CAAwB,UAAA0E,GAAG;EAAA,mBAAIqiB,aAAa,CAACriB,GAAG,CAACL,EAAL,CAAjB;EAAA,WAA3B,CAAP;EACD;EACF;EACF,KAhBD;;EAkBA0iB,IAAAA,aAAa,CAAC1iB,EAAD,CAAb;EAEA,wBACK4I,KADL;EAEEwZ,MAAAA,cAAc,EAAEK;EAFlB;EAID;;EAED,MAAInZ,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACmoB,yBAA5B,EAAuD;EAAA,QACtCc,aADsC,GACtBhZ,MADsB,CAC7C9P,KAD6C;;EAAA,QAGnDykB,IAHmD,GAQjDzf,QARiD,CAGnDyf,IAHmD;EAAA,QAInDlS,UAJmD,GAQjDvN,QARiD,CAInDuN,QAJmD;EAAA,iCAQjDvN,QARiD,CAKnDgkB,aALmD;EAAA,QAKnDA,cALmD,uCAKnC,IALmC;EAAA,QAMnDH,qBANmD,GAQjD7jB,QARiD,CAMnD6jB,qBANmD;EAAA,QAOnDtX,WAPmD,GAQjDvM,QARiD,CAOnDuM,UAPmD;;EAUrD,QAAMwX,UAAS,GACb,OAAOD,aAAP,KAAuB,WAAvB,GAAqCA,aAArC,GAAmD,CAACD,qBADtD;;EAGA,QAAMI,kBAAiB,gBAAQ7Z,KAAK,CAACwZ,cAAd,CAAvB;;EAEA,QAAMM,cAAa,GAAG,SAAhBA,cAAgB,CAAA1iB,EAAE,EAAI;EAC1B,UAAMK,GAAG,GAAG0L,UAAQ,CAAC/L,EAAD,CAApB;;EAEA,UAAI,CAACK,GAAG,CAACwV,SAAT,EAAoB;EAClB,YAAI0M,UAAJ,EAAe;EACbE,UAAAA,kBAAiB,CAACziB,EAAD,CAAjB,GAAwB,IAAxB;EACD,SAFD,MAEO;EACL,iBAAOyiB,kBAAiB,CAACziB,EAAD,CAAxB;EACD;EACF;;EAED,UAAIwiB,cAAa,IAAIzX,WAAU,CAAC1K,GAAD,CAA/B,EAAsC;EACpC,eAAO0K,WAAU,CAAC1K,GAAD,CAAV,CAAgB1E,OAAhB,CAAwB,UAAA0E,GAAG;EAAA,iBAAIqiB,cAAa,CAACriB,GAAG,CAACL,EAAL,CAAjB;EAAA,SAA3B,CAAP;EACD;EACF,KAdD;;EAgBAie,IAAAA,IAAI,CAACtiB,OAAL,CAAa,UAAA0E,GAAG;EAAA,aAAIqiB,cAAa,CAACriB,GAAG,CAACL,EAAL,CAAjB;EAAA,KAAhB;EAEA,wBACK4I,KADL;EAEEwZ,MAAAA,cAAc,EAAEK;EAFlB;EAID;;EACD,SAAO7Z,KAAP;EACD;;EAED,SAASrB,aAAT,CAAqB/I,QAArB,EAA+B;EAAA,MAE3B6M,IAF2B,GAczB7M,QAdyB,CAE3B6M,IAF2B;EAAA,MAG3B5H,IAH2B,GAczBjF,QAdyB,CAG3BiF,IAH2B;EAAA,MAI3B0G,QAJ2B,GAczB3L,QAdyB,CAI3B2L,QAJ2B;EAAA,MAK3BrO,OAL2B,GAczB0C,QAdyB,CAK3B1C,OAL2B;EAAA,MAM3BiQ,QAN2B,GAczBvN,QAdyB,CAM3BuN,QAN2B;EAAA,+BAczBvN,QAdyB,CAO3BqZ,kBAP2B;EAAA,MAO3BA,kBAP2B,uCAON9L,QAPM;EAAA,8BAczBvN,QAdyB,CAQ3BmkB,qBAR2B;EAAA,MAQ3BA,qBAR2B,sCAQH,IARG;EAAA,MASlBP,cATkB,GAczB5jB,QAdyB,CAS3BoK,KAT2B,CASlBwZ,cATkB;EAAA,+BAczB5jB,QAdyB,CAU3BgkB,aAV2B;EAAA,MAU3BA,aAV2B,uCAUX,IAVW;EAAA,MAW3BtY,QAX2B,GAczB1L,QAdyB,CAW3B0L,QAX2B;EAAA,MAY3B+T,IAZ2B,GAczBzf,QAdyB,CAY3Byf,IAZ2B;EAAA,MAa3BlT,UAb2B,GAczBvM,QAdyB,CAa3BuM,UAb2B;EAgB7BlP,EAAAA,iBAAiB,CACfC,OADe,EAEf,CAAC,YAAD,EAAe,YAAf,EAA6B,WAA7B,EAA0C,aAA1C,EAAyD,eAAzD,CAFe,EAGf,cAHe,CAAjB;EAMA,MAAM8mB,gBAAgB,GAAG/lB,KAAK,CAACgP,OAAN,CAAc,YAAM;EAC3C,QAAM+W,gBAAgB,GAAG,EAAzB;EAEAnf,IAAAA,IAAI,CAAC9H,OAAL,CAAa,UAAA0E,GAAG,EAAI;EAClB,UAAM4hB,UAAU,GAAGO,aAAa,GAC5BK,gBAAgB,CAACxiB,GAAD,EAAM+hB,cAAN,EAAsBrX,UAAtB,CADY,GAE5B,CAAC,CAACqX,cAAc,CAAC/hB,GAAG,CAACL,EAAL,CAFpB;EAGAK,MAAAA,GAAG,CAAC4hB,UAAJ,GAAiB,CAAC,CAACA,UAAnB;EACA5hB,MAAAA,GAAG,CAAC6hB,cAAJ,GAAqBD,UAAU,KAAK,IAApC;;EAEA,UAAIA,UAAJ,EAAgB;EACdW,QAAAA,gBAAgB,CAAC7gB,IAAjB,CAAsB1B,GAAtB;EACD;EACF,KAVD;EAYA,WAAOuiB,gBAAP;EACD,GAhBwB,EAgBtB,CAACnf,IAAD,EAAO+e,aAAP,EAAsBJ,cAAtB,EAAsCrX,UAAtC,CAhBsB,CAAzB;EAkBA,MAAIoX,iBAAiB,GAAG/W,OAAO,CAC7BlM,MAAM,CAACgQ,IAAP,CAAY2I,kBAAZ,EAAgC7V,MAAhC,IAA0C9C,MAAM,CAACgQ,IAAP,CAAYkT,cAAZ,EAA4BpgB,MADzC,CAA/B;EAIA,MAAIqgB,qBAAqB,GAAGF,iBAA5B;;EAEA,MAAIA,iBAAJ,EAAuB;EACrB,QAAIjjB,MAAM,CAACgQ,IAAP,CAAY2I,kBAAZ,EAAgCrW,IAAhC,CAAqC,UAAAxB,EAAE;EAAA,aAAI,CAACoiB,cAAc,CAACpiB,EAAD,CAAnB;EAAA,KAAvC,CAAJ,EAAqE;EACnEmiB,MAAAA,iBAAiB,GAAG,KAApB;EACD;EACF;;EAED,MAAI,CAACA,iBAAL,EAAwB;EACtB,QAAIlE,IAAI,IAAIA,IAAI,CAACjc,MAAb,IAAuBic,IAAI,CAACzc,IAAL,CAAU;EAAA,UAAGxB,EAAH,SAAGA,EAAH;EAAA,aAAY,CAACoiB,cAAc,CAACpiB,EAAD,CAA3B;EAAA,KAAV,CAA3B,EAAuE;EACrEqiB,MAAAA,qBAAqB,GAAG,KAAxB;EACD;EACF;;EAED,MAAMS,wBAAwB,GAAGpmB,YAAY,CAACimB,qBAAD,CAA7C;EAEAtlB,EAAAA,sBAAsB,CAAC,YAAM;EAC3B,QAAIylB,wBAAwB,EAA5B,EAAgC;EAC9B5Y,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAACgoB;EAAhB,OAAD,CAAR;EACD;EACF,GAJqB,EAInB,CAACnX,QAAD,EAAWmB,IAAX,CAJmB,CAAtB;EAMA,MAAMiW,qBAAqB,GAAGzkB,KAAK,CAACG,WAAN,CAC5B,UAAAxD,KAAK;EAAA,WAAI0Q,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACioB,qBAAhB;EAAuC9nB,MAAAA,KAAK,EAALA;EAAvC,KAAD,CAAZ;EAAA,GADuB,EAE5B,CAAC0Q,QAAD,CAF4B,CAA9B;EAKA,MAAMsX,yBAAyB,GAAG3kB,KAAK,CAACG,WAAN,CAChC,UAAAxD,KAAK;EAAA,WAAI0Q,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACmoB,yBAAhB;EAA2ChoB,MAAAA,KAAK,EAALA;EAA3C,KAAD,CAAZ;EAAA,GAD2B,EAEhC,CAAC0Q,QAAD,CAFgC,CAAlC;EAKA,MAAMqX,iBAAiB,GAAG1kB,KAAK,CAACG,WAAN,CACxB,UAACgD,EAAD,EAAKxG,KAAL;EAAA,WAAe0Q,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACkoB,iBAAhB;EAAmCvhB,MAAAA,EAAE,EAAFA,EAAnC;EAAuCxG,MAAAA,KAAK,EAALA;EAAvC,KAAD,CAAvB;EAAA,GADwB,EAExB,CAAC0Q,QAAD,CAFwB,CAA1B;EAKA,MAAMG,WAAW,GAAG3N,YAAY,CAAC8B,QAAD,CAAhC;EAEA,MAAMojB,6BAA6B,GAAG7mB,cAAc,CAClDoP,QAAQ,GAAGyX,6BADuC,EAElD;EAAEpjB,IAAAA,QAAQ,EAAE6L,WAAW;EAAvB,GAFkD,CAApD;EAKA,MAAMyX,iCAAiC,GAAG/mB,cAAc,CACtDoP,QAAQ,GAAG2X,iCAD2C,EAEtD;EAAEtjB,IAAAA,QAAQ,EAAE6L,WAAW;EAAvB,GAFsD,CAAxD;EAKAnL,EAAAA,MAAM,CAACsB,MAAP,CAAchC,QAAd,EAAwB;EACtBokB,IAAAA,gBAAgB,EAAhBA,gBADsB;EAEtBT,IAAAA,iBAAiB,EAAjBA,iBAFsB;EAGtBE,IAAAA,qBAAqB,EAArBA,qBAHsB;EAItBd,IAAAA,iBAAiB,EAAjBA,iBAJsB;EAKtBD,IAAAA,qBAAqB,EAArBA,qBALsB;EAMtBM,IAAAA,6BAA6B,EAA7BA,6BANsB;EAOtBE,IAAAA,iCAAiC,EAAjCA,iCAPsB;EAQtBN,IAAAA,yBAAyB,EAAzBA;EARsB,GAAxB;EAUD;;EAED,SAASha,YAAT,CAAoBnH,GAApB,SAAuC;EAAA,MAAZ7B,QAAY,SAAZA,QAAY;;EACrC6B,EAAAA,GAAG,CAACkhB,iBAAJ,GAAwB,UAAA5e,GAAG;EAAA,WAAInE,QAAQ,CAAC+iB,iBAAT,CAA2BlhB,GAAG,CAACL,EAA/B,EAAmC2C,GAAnC,CAAJ;EAAA,GAA3B;;EAEAtC,EAAAA,GAAG,CAACqhB,yBAAJ,GAAgC3mB,cAAc,CAC5CyD,QAAQ,CAAC2L,QAAT,GAAoBuX,yBADwB,EAE5C;EAAEljB,IAAAA,QAAQ,EAAEA,QAAZ;EAAsB6B,IAAAA,GAAG,EAAHA;EAAtB,GAF4C,CAA9C;EAID;;EAED,SAASwiB,gBAAT,CAA0BxiB,GAA1B,EAA+B+hB,cAA/B,EAA+CrX,UAA/C,EAA2D;EACzD,MAAIqX,cAAc,CAAC/hB,GAAG,CAACL,EAAL,CAAlB,EAA4B;EAC1B,WAAO,IAAP;EACD;;EAED,MAAMmE,OAAO,GAAG4G,UAAU,CAAC1K,GAAD,CAA1B;;EAEA,MAAI8D,OAAO,IAAIA,OAAO,CAACnC,MAAvB,EAA+B;EAC7B,QAAI+gB,mBAAmB,GAAG,IAA1B;EACA,QAAIC,YAAY,GAAG,KAAnB;EAEA7e,IAAAA,OAAO,CAACxI,OAAR,CAAgB,UAAA0c,MAAM,EAAI;EACxB;EACA,UAAI2K,YAAY,IAAI,CAACD,mBAArB,EAA0C;EACxC;EACD;;EAED,UAAIF,gBAAgB,CAACxK,MAAD,EAAS+J,cAAT,EAAyBrX,UAAzB,CAApB,EAA0D;EACxDiY,QAAAA,YAAY,GAAG,IAAf;EACD,OAFD,MAEO;EACLD,QAAAA,mBAAmB,GAAG,KAAtB;EACD;EACF,KAXD;EAYA,WAAOA,mBAAmB,GAAG,IAAH,GAAUC,YAAY,GAAG,IAAH,GAAU,KAA1D;EACD;;EAED,SAAO,KAAP;EACD;;EChWD,IAAMC,8BAA8B,GAAG,SAAjCA,8BAAiC,CAAA5iB,GAAG;EAAA,SAAK,EAAL;EAAA,CAA1C;;EACA,IAAM6iB,+BAA+B,GAAG,SAAlCA,+BAAkC,CAAA1c,IAAI;EAAA,SAAK,EAAL;EAAA,CAA5C;;;EAGAnN,OAAO,CAAC8pB,WAAR,GAAsB,aAAtB;EACA9pB,OAAO,CAAC+pB,YAAR,GAAuB,cAAvB;EACA/pB,OAAO,CAACgqB,aAAR,GAAwB,eAAxB;AAEA,MAAaC,WAAW,GAAG,SAAdA,WAAc,CAAAtoB,KAAK,EAAI;EAClCA,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,aAAvB;EACAvM,EAAAA,KAAK,CAACwM,UAAN,CAAiBzF,IAAjB,CAAsByF,YAAtB;EACD,CAJM;EAMP8b,WAAW,CAACtnB,UAAZ,GAAyB,aAAzB;;EAEA,SAAS2M,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EAAA,8BAKnDA,QALmD,CAErD+kB,uBAFqD;EAAA,MAErDA,uBAFqD,sCAE3BN,8BAF2B;EAAA,8BAKnDzkB,QALmD,CAGrDglB,wBAHqD;EAAA,MAGrDA,wBAHqD,sCAG1BN,+BAH0B;EAAA,MAIrDnX,QAJqD,GAKnDvN,QALmD,CAIrDuN,QAJqD;;EAOvD,MAAIzC,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACEmqB,MAAAA,QAAQ,EAAE;EADZ,OAEK7a,KAFL;EAID;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACgqB,aAA5B,EAA2C;EACzC,wBACKza,KADL;EAEE6a,MAAAA,QAAQ,EAAEjlB,QAAQ,CAACgL,YAAT,CAAsBia,QAAtB,IAAkC;EAF9C;EAID;;EAED,MAAIna,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC8pB,WAA5B,EAAyC;EAAA;;EAAA,QAC/B/T,KAD+B,GACd9F,MADc,CAC/B8F,KAD+B;EAAA,QACxB5V,KADwB,GACd8P,MADc,CACxB9P,KADwB;EAGvC,QAAMkqB,WAAW,GACf,OAAO9a,KAAK,CAAC6a,QAAN,CAAerU,KAAf,CAAP,KAAiC,WAAjC,GACIxG,KAAK,CAAC6a,QAAN,CAAerU,KAAf,CADJ,GAEImU,uBAAuB,CAACxX,QAAQ,CAACqD,KAAD,CAAT,CAH7B;EAKA,wBACKxG,KADL;EAEE6a,MAAAA,QAAQ,eACH7a,KAAK,CAAC6a,QADH,6BAELrU,KAFK,IAEG7S,gBAAgB,CAAC/C,KAAD,EAAQkqB,WAAR,CAFnB;EAFV;EAOD;;EAED,MAAIpa,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC+pB,YAA5B,EAA0C;EAAA;;EAAA,QAChChU,MADgC,GACL9F,MADK,CAChC8F,KADgC;EAAA,QACzB1F,QADyB,GACLJ,MADK,CACzBI,QADyB;EAAA,QACflQ,MADe,GACL8P,MADK,CACf9P,KADe;;EAGxC,QAAMkqB,YAAW,GACf,OAAO9a,KAAK,CAAC6a,QAAN,CAAerU,MAAf,CAAP,KAAiC,WAAjC,GACIxG,KAAK,CAAC6a,QAAN,CAAerU,MAAf,CADJ,GAEImU,uBAAuB,CAACxX,QAAQ,CAACqD,MAAD,CAAT,CAH7B;;EAKA,QAAMuU,YAAY,GAChB,QAAOD,YAAP,6CAAOA,YAAW,CAAEE,SAApB,qBAAO,sBAAyBla,QAAzB,CAAP,MAA8C,WAA9C,GACIga,YAAW,CAACE,SAAZ,CAAsBla,QAAtB,CADJ,GAEI8Z,wBAAwB,qBACtBzX,QAAQ,CAACqD,MAAD,CADc,8CACtB,iBAAiBlC,KADK,qBACtB,sBAAwBb,IAAxB,CAA6B,UAAA7F,IAAI;EAAA,aAAIA,IAAI,CAAC/H,MAAL,CAAYuB,EAAZ,KAAmB0J,QAAvB;EAAA,KAAjC,CADsB,CAH9B;EAOA,wBACKd,KADL;EAEE6a,MAAAA,QAAQ,eACH7a,KAAK,CAAC6a,QADH,6BAELrU,MAFK,iBAGDsU,YAHC;EAIJE,QAAAA,SAAS,eACHF,YAAW,CAACE,SAAZ,IAAyB,EADtB,6BAENla,QAFM,IAEKnN,gBAAgB,CAAC/C,MAAD,EAAQmqB,YAAR,CAFrB;EAJL;EAFV;EAaD;EACF;;EAED,SAASpc,aAAT,CAAqB/I,QAArB,EAA+B;EAAA,8BACwBA,QADxB,CACrBqlB,iBADqB;EAAA,MACrBA,iBADqB,sCACD,IADC;EAAA,MACKxY,IADL,GACwB7M,QADxB,CACK6M,IADL;EAAA,MACWnB,QADX,GACwB1L,QADxB,CACW0L,QADX;EAG7B,MAAMiZ,WAAW,GAAGtmB,KAAK,CAACG,WAAN,CAClB,UAACoS,KAAD,EAAQ5V,KAAR;EAAA,WACE0Q,QAAQ,CAAC;EACPxL,MAAAA,IAAI,EAAErF,OAAO,CAAC8pB,WADP;EAEP/T,MAAAA,KAAK,EAALA,KAFO;EAGP5V,MAAAA,KAAK,EAALA;EAHO,KAAD,CADV;EAAA,GADkB,EAOlB,CAAC0Q,QAAD,CAPkB,CAApB;EAUA,MAAMkZ,YAAY,GAAGvmB,KAAK,CAACG,WAAN,CACnB,UAACoS,KAAD,EAAQ1F,QAAR,EAAkBlQ,KAAlB;EAAA,WACE0Q,QAAQ,CAAC;EACPxL,MAAAA,IAAI,EAAErF,OAAO,CAAC+pB,YADP;EAEPhU,MAAAA,KAAK,EAALA,KAFO;EAGP1F,MAAAA,QAAQ,EAARA,QAHO;EAIPlQ,MAAAA,KAAK,EAALA;EAJO,KAAD,CADV;EAAA,GADmB,EAQnB,CAAC0Q,QAAD,CARmB,CAArB;EAWA,MAAM4Z,oBAAoB,GAAGpnB,YAAY,CAACmnB,iBAAD,CAAzC;EAEAxmB,EAAAA,sBAAsB,CAAC,YAAM;EAC3B,QAAIymB,oBAAoB,EAAxB,EAA4B;EAC1B5Z,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAACgqB;EAAhB,OAAD,CAAR;EACD;EACF,GAJqB,EAInB,CAAChY,IAAD,CAJmB,CAAtB;EAMAnM,EAAAA,MAAM,CAACsB,MAAP,CAAchC,QAAd,EAAwB;EACtB2kB,IAAAA,WAAW,EAAXA,WADsB;EAEtBC,IAAAA,YAAY,EAAZA;EAFsB,GAAxB;EAID;;EAED,SAAS5b,YAAT,CAAoBnH,GAApB,QAAuC;EAAA,MAAZ7B,QAAY,QAAZA,QAAY;EAAA,+BAKjCA,QALiC,CAEnC+kB,uBAFmC;EAAA,MAEnCA,uBAFmC,uCAETN,8BAFS;EAAA,+BAKjCzkB,QALiC,CAGnCglB,wBAHmC;EAAA,MAGnCA,wBAHmC,uCAGRN,+BAHQ;EAAA,MAI1BO,QAJ0B,GAKjCjlB,QALiC,CAInCoK,KAJmC,CAI1B6a,QAJ0B;;EAOrC,MAAIpjB,GAAJ,EAAS;EACPA,IAAAA,GAAG,CAACuI,KAAJ,GACE,OAAO6a,QAAQ,CAACpjB,GAAG,CAACL,EAAL,CAAf,KAA4B,WAA5B,GACIyjB,QAAQ,CAACpjB,GAAG,CAACL,EAAL,CADZ,GAEIujB,uBAAuB,CAACljB,GAAD,CAH7B;;EAKAA,IAAAA,GAAG,CAAC0jB,QAAJ,GAAe,UAAAvnB,OAAO,EAAI;EACxB,aAAOgC,QAAQ,CAAC2kB,WAAT,CAAqB9iB,GAAG,CAACL,EAAzB,EAA6BxD,OAA7B,CAAP;EACD,KAFD;;EAIA6D,IAAAA,GAAG,CAAC6M,KAAJ,CAAUvR,OAAV,CAAkB,UAAA6K,IAAI,EAAI;EACxB,UAAI,CAACnG,GAAG,CAACuI,KAAJ,CAAUgb,SAAf,EAA0B;EACxBvjB,QAAAA,GAAG,CAACuI,KAAJ,CAAUgb,SAAV,GAAsB,EAAtB;EACD;;EAEDpd,MAAAA,IAAI,CAACoC,KAAL,GACE,OAAOvI,GAAG,CAACuI,KAAJ,CAAUgb,SAAV,CAAoBpd,IAAI,CAAC/H,MAAL,CAAYuB,EAAhC,CAAP,KAA+C,WAA/C,GACIK,GAAG,CAACuI,KAAJ,CAAUgb,SAAV,CAAoBpd,IAAI,CAAC/H,MAAL,CAAYuB,EAAhC,CADJ,GAEIwjB,wBAAwB,CAAChd,IAAD,CAH9B;;EAKAA,MAAAA,IAAI,CAACud,QAAL,GAAgB,UAAAvnB,OAAO,EAAI;EACzB,eAAOgC,QAAQ,CAAC4kB,YAAT,CAAsB/iB,GAAG,CAACL,EAA1B,EAA8BwG,IAAI,CAAC/H,MAAL,CAAYuB,EAA1C,EAA8CxD,OAA9C,CAAP;EACD,OAFD;EAGD,KAbD;EAcD;EACF;;EC/JDnD,OAAO,CAAC2qB,gBAAR,GAA2B,kBAA3B;EACA3qB,OAAO,CAAC4qB,cAAR,GAAyB,gBAAzB;AAEA,MAAaC,cAAc,GAAG,SAAjBA,cAAiB,CAAAlpB,KAAK,EAAI;EACrCA,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACoM,kBAAN,CAAyBrF,IAAzB,CAA8B,UAACxE,IAAD,QAAwB;EAAA,QAAfiB,QAAe,QAAfA,QAAe;EACpD,qBAAWjB,IAAX,GAAiBiB,QAAQ,CAACoK,KAAT,CAAeub,WAAhC;EACD,GAFD;EAGAnpB,EAAAA,KAAK,CAACmM,cAAN,CAAqBpF,IAArB,CAA0BoF,gBAA1B;EACAnM,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,aAAvB;EACD,CAPM;EASP2c,cAAc,CAACloB,UAAf,GAA4B,gBAA5B;;EAEA,SAAS2M,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EACvD,MAAI8K,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACE6qB,MAAAA,WAAW,EAAE;EADf,OAEKvb,KAFL;EAID;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC2qB,gBAA5B,EAA8C;EAC5C,wBACKpb,KADL;EAEEub,MAAAA,WAAW,EAAE3lB,QAAQ,CAACgL,YAAT,CAAsB2a,WAAtB,IAAqC;EAFpD;EAID;;EAED,MAAI7a,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAAC4qB,cAA5B,EAA4C;EAC1C,wBACKrb,KADL;EAEEub,MAAAA,WAAW,EAAE5nB,gBAAgB,CAAC+M,MAAM,CAAC6a,WAAR,EAAqBvb,KAAK,CAACub,WAA3B;EAF/B;EAID;EACF;;EAED,SAAShd,gBAAT,CACE1H,OADF,SAOE;EAAA,MAHa0kB,WAGb,SAJE3lB,QAIF,CAHIoK,KAGJ,CAHaub,WAGb;;EACA;EACA,MAAI,CAACA,WAAD,IAAgB,CAACA,WAAW,CAACniB,MAAjC,EAAyC;EACvC,WAAOvC,OAAP;EACD;;EAED,MAAM2kB,eAAe,aAAOD,WAAP,CAArB,CANA;;EASA,MAAME,WAAW,aAAO5kB,OAAP,CAAjB,CATA;;EAYA,MAAM6kB,cAAc,GAAG,EAAvB,CAZA;;EAAA;EAgBE,QAAMC,cAAc,GAAGH,eAAe,CAACnY,KAAhB,EAAvB;EACA,QAAMuY,UAAU,GAAGH,WAAW,CAACloB,SAAZ,CAAsB,UAAAsF,CAAC;EAAA,aAAIA,CAAC,CAACzB,EAAF,KAASukB,cAAb;EAAA,KAAvB,CAAnB;;EACA,QAAIC,UAAU,GAAG,CAAC,CAAlB,EAAqB;EACnBF,MAAAA,cAAc,CAACviB,IAAf,CAAoBsiB,WAAW,CAACxI,MAAZ,CAAmB2I,UAAnB,EAA+B,CAA/B,EAAkC,CAAlC,CAApB;EACD;EApBH;;EAeA,SAAOH,WAAW,CAACriB,MAAZ,IAAsBoiB,eAAe,CAACpiB,MAA7C,EAAqD;EAAA;EAMpD,GArBD;;;EAwBA,mBAAWsiB,cAAX,EAA8BD,WAA9B;EACD;;EAED,SAAS9c,aAAT,CAAqB/I,QAArB,EAA+B;EAAA,MACrB0L,QADqB,GACR1L,QADQ,CACrB0L,QADqB;EAG7B1L,EAAAA,QAAQ,CAACylB,cAAT,GAA0BpnB,KAAK,CAACG,WAAN,CACxB,UAAAmnB,WAAW,EAAI;EACb,WAAOja,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAAC4qB,cAAhB;EAAgCE,MAAAA,WAAW,EAAXA;EAAhC,KAAD,CAAf;EACD,GAHuB,EAIxB,CAACja,QAAD,CAJwB,CAA1B;EAMD;;ECvEDxQ,aAAa,CAACwU,SAAd,GAA0B,IAA1B;;EAGA7U,OAAO,CAACorB,mBAAR,GAA8B,qBAA9B;EACAprB,OAAO,CAACqrB,cAAR,GAAyB,gBAAzB;EACArrB,OAAO,CAACsrB,kBAAR,GAA6B,oBAA7B;EACAtrB,OAAO,CAACurB,WAAR,GAAsB,aAAtB;AAEA,MAAaC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAAA7pB,KAAK,EAAI;EACvCA,EAAAA,KAAK,CAAC8pB,eAAN,GAAwB,CAACC,sBAAD,CAAxB;EACA/pB,EAAAA,KAAK,CAAC6M,cAAN,CAAqB9F,IAArB,CAA0B;EACxBzH,IAAAA,KAAK,EAAE;EACL0qB,MAAAA,QAAQ,EAAE;EADL;EADiB,GAA1B;EAKAhqB,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACuM,WAAN,CAAkBxF,IAAlB,CAAuBwF,aAAvB;EACAvM,EAAAA,KAAK,CAACsM,2BAAN,CAAkCvF,IAAlC,CAAuCuF,6BAAvC;EACD,CAVM;;EAYP,IAAMyd,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAC3qB,KAAD,QAAiC;EAAA,MAAvBoE,QAAuB,QAAvBA,QAAuB;EAAA,MAAbgP,MAAa,QAAbA,MAAa;EAAA,MACtDtD,QADsD,GACzC1L,QADyC,CACtD0L,QADsD;;EAG9D,MAAM+a,aAAa,GAAG,SAAhBA,aAAgB,CAACliB,CAAD,EAAIyK,MAAJ,EAAe;EACnC,QAAI0X,YAAY,GAAG,KAAnB;;EACA,QAAIniB,CAAC,CAACrE,IAAF,KAAW,YAAf,EAA6B;EAC3B;EACA,UAAIqE,CAAC,CAACoiB,OAAF,IAAapiB,CAAC,CAACoiB,OAAF,CAAUnjB,MAAV,GAAmB,CAApC,EAAuC;EACrC;EACD;;EACDkjB,MAAAA,YAAY,GAAG,IAAf;EACD;;EACD,QAAME,eAAe,GAAGC,cAAc,CAAC7X,MAAD,CAAtC;EACA,QAAM8X,cAAc,GAAGF,eAAe,CAACxlB,GAAhB,CAAoB,UAAA6B,CAAC;EAAA,aAAI,CAACA,CAAC,CAACzB,EAAH,EAAOyB,CAAC,CAACmM,UAAT,CAAJ;EAAA,KAArB,CAAvB;EAEA,QAAM2X,OAAO,GAAGL,YAAY,GAAGnX,IAAI,CAACyX,KAAL,CAAWziB,CAAC,CAACoiB,OAAF,CAAU,CAAV,EAAaI,OAAxB,CAAH,GAAsCxiB,CAAC,CAACwiB,OAApE;EAEA,QAAIE,GAAJ;EACA,QAAIC,iBAAJ;;EAEA,QAAMC,WAAW,GAAG,SAAdA,WAAc,GAAM;EACxB1gB,MAAAA,MAAM,CAAC2gB,oBAAP,CAA4BH,GAA5B;EACAA,MAAAA,GAAG,GAAG,IAAN;EACAvb,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAACsrB;EAAhB,OAAD,CAAR;EACD,KAJD;;EAKA,QAAMkB,YAAY,GAAG,SAAfA,YAAe,GAAM;EACzB5gB,MAAAA,MAAM,CAAC2gB,oBAAP,CAA4BH,GAA5B;EACAA,MAAAA,GAAG,GAAG,IAAN;EACAvb,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAACqrB,cAAhB;EAAgCa,QAAAA,OAAO,EAAEG;EAAzC,OAAD,CAAR;EACD,KAJD;;EAMA,QAAMI,wCAAwC,GAAG,SAA3CA,wCAA2C,CAAAC,UAAU,EAAI;EAC7DL,MAAAA,iBAAiB,GAAGK,UAApB;;EACA,UAAI,CAACN,GAAL,EAAU;EACRA,QAAAA,GAAG,GAAGxgB,MAAM,CAAC+gB,qBAAP,CAA6BH,YAA7B,CAAN;EACD;EACF,KALD;;EAOA,QAAMI,iBAAiB,GAAG;EACxBC,MAAAA,KAAK,EAAE;EACLC,QAAAA,SAAS,EAAE,WADN;EAELC,QAAAA,WAAW,EAAE,qBAAArjB,CAAC;EAAA,iBAAI+iB,wCAAwC,CAAC/iB,CAAC,CAACwiB,OAAH,CAA5C;EAAA,SAFT;EAGLc,QAAAA,OAAO,EAAE,SAHJ;EAILC,QAAAA,SAAS,EAAE,mBAAAvjB,CAAC,EAAI;EACd7F,UAAAA,QAAQ,CAACiI,mBAAT,CACE,WADF,EAEE8gB,iBAAiB,CAACC,KAAlB,CAAwBE,WAF1B;EAIAlpB,UAAAA,QAAQ,CAACiI,mBAAT,CACE,SADF,EAEE8gB,iBAAiB,CAACC,KAAlB,CAAwBI,SAF1B;EAIAX,UAAAA,WAAW;EACZ;EAdI,OADiB;EAiBxBY,MAAAA,KAAK,EAAE;EACLJ,QAAAA,SAAS,EAAE,WADN;EAELC,QAAAA,WAAW,EAAE,qBAAArjB,CAAC,EAAI;EAChB,cAAIA,CAAC,CAACyjB,UAAN,EAAkB;EAChBzjB,YAAAA,CAAC,CAAC0jB,cAAF;EACA1jB,YAAAA,CAAC,CAAC2jB,eAAF;EACD;;EACDZ,UAAAA,wCAAwC,CAAC/iB,CAAC,CAACoiB,OAAF,CAAU,CAAV,EAAaI,OAAd,CAAxC;EACA,iBAAO,KAAP;EACD,SATI;EAULc,QAAAA,OAAO,EAAE,UAVJ;EAWLC,QAAAA,SAAS,EAAE,mBAAAvjB,CAAC,EAAI;EACd7F,UAAAA,QAAQ,CAACiI,mBAAT,CACE8gB,iBAAiB,CAACM,KAAlB,CAAwBJ,SAD1B,EAEEF,iBAAiB,CAACM,KAAlB,CAAwBH,WAF1B;EAIAlpB,UAAAA,QAAQ,CAACiI,mBAAT,CACE8gB,iBAAiB,CAACM,KAAlB,CAAwBF,OAD1B,EAEEJ,iBAAiB,CAACM,KAAlB,CAAwBH,WAF1B;EAIAT,UAAAA,WAAW;EACZ;EArBI;EAjBiB,KAA1B;EA0CA,QAAMgB,MAAM,GAAGzB,YAAY,GACvBe,iBAAiB,CAACM,KADK,GAEvBN,iBAAiB,CAACC,KAFtB;EAGA,QAAMU,kBAAkB,GAAG/hB,qBAAqB,KAC5C;EAAEG,MAAAA,OAAO,EAAE;EAAX,KAD4C,GAE5C,KAFJ;EAGA9H,IAAAA,QAAQ,CAACgI,gBAAT,CACEyhB,MAAM,CAACR,SADT,EAEEQ,MAAM,CAACP,WAFT,EAGEQ,kBAHF;EAKA1pB,IAAAA,QAAQ,CAACgI,gBAAT,CACEyhB,MAAM,CAACN,OADT,EAEEM,MAAM,CAACL,SAFT,EAGEM,kBAHF;EAMA1c,IAAAA,QAAQ,CAAC;EACPxL,MAAAA,IAAI,EAAErF,OAAO,CAACorB,mBADP;EAEP/a,MAAAA,QAAQ,EAAE8D,MAAM,CAACxN,EAFV;EAGP6mB,MAAAA,WAAW,EAAErZ,MAAM,CAACI,UAHb;EAIP0X,MAAAA,cAAc,EAAdA,cAJO;EAKPC,MAAAA,OAAO,EAAPA;EALO,KAAD,CAAR;EAOD,GArGD;;EAuGA,SAAO,CACLnrB,KADK,EAEL;EACE0sB,IAAAA,WAAW,EAAE,qBAAA/jB,CAAC;EAAA,aAAIA,CAAC,CAACwS,OAAF,MAAe0P,aAAa,CAACliB,CAAD,EAAIyK,MAAJ,CAAhC;EAAA,KADhB;EAEEuZ,IAAAA,YAAY,EAAE,sBAAAhkB,CAAC;EAAA,aAAIA,CAAC,CAACwS,OAAF,MAAe0P,aAAa,CAACliB,CAAD,EAAIyK,MAAJ,CAAhC;EAAA,KAFjB;EAGElT,IAAAA,KAAK,EAAE;EACLuI,MAAAA,MAAM,EAAE;EADH,KAHT;EAMEmkB,IAAAA,SAAS,EAAE,KANb;EAOEnhB,IAAAA,IAAI,EAAE;EAPR,GAFK,CAAP;EAYD,CAtHD;;EAwHAgf,gBAAgB,CAAC7oB,UAAjB,GAA8B,kBAA9B;;EAEA,SAAS2M,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgC;EAC9B,MAAIA,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACEorB,MAAAA,cAAc,EAAE;EACduC,QAAAA,YAAY,EAAE;EADA;EADlB,OAIKre,KAJL;EAMD;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACurB,WAA5B,EAAyC;EACvC,wBACKhc,KADL;EAEE8b,MAAAA,cAAc,EAAE;EACduC,QAAAA,YAAY,EAAE;EADA;EAFlB;EAMD;;EAED,MAAI3d,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACorB,mBAA5B,EAAiD;EAAA,QACvCc,OADuC,GACYjc,MADZ,CACvCic,OADuC;EAAA,QAC9B7b,QAD8B,GACYJ,MADZ,CAC9BI,QAD8B;EAAA,QACpBmd,WADoB,GACYvd,MADZ,CACpBud,WADoB;EAAA,QACPvB,cADO,GACYhc,MADZ,CACPgc,cADO;EAG/C,wBACK1c,KADL;EAEE8b,MAAAA,cAAc,eACT9b,KAAK,CAAC8b,cADG;EAEZwC,QAAAA,MAAM,EAAE3B,OAFI;EAGZD,QAAAA,cAAc,EAAdA,cAHY;EAIZuB,QAAAA,WAAW,EAAXA,WAJY;EAKZM,QAAAA,gBAAgB,EAAEzd;EALN;EAFhB;EAUD;;EAED,MAAIJ,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACqrB,cAA5B,EAA4C;EAAA,QAClCa,QADkC,GACtBjc,MADsB,CAClCic,OADkC;;EAAA,gCAEW3c,KAAK,CAAC8b,cAFjB;EAAA,QAElCwC,MAFkC,yBAElCA,MAFkC;EAAA,QAE1BL,YAF0B,yBAE1BA,WAF0B;EAAA,uDAEbvB,cAFa;EAAA,QAEbA,eAFa,uCAEI,EAFJ;;EAI1C,QAAM8B,MAAM,GAAG7B,QAAO,GAAG2B,MAAzB;EACA,QAAMG,gBAAgB,GAAGD,MAAM,GAAGP,YAAlC;EAEA,QAAMS,eAAe,GAAG,EAAxB;;EAEAhC,IAAAA,eAAc,CAAC3pB,OAAf,CAAuB,iBAA6B;EAAA,UAA3B4rB,QAA2B;EAAA,UAAjBC,WAAiB;EAClDF,MAAAA,eAAe,CAACC,QAAD,CAAf,GAA4BxZ,IAAI,CAACE,GAAL,CAC1BuZ,WAAW,GAAGA,WAAW,GAAGH,gBADF,EAE1B,CAF0B,CAA5B;EAID,KALD;;EAOA,wBACKze,KADL;EAEE8b,MAAAA,cAAc,eACT9b,KAAK,CAAC8b,cADG;EAEZuC,QAAAA,YAAY,eACPre,KAAK,CAAC8b,cAAN,CAAqBuC,YADd,MAEPK,eAFO;EAFA;EAFhB;EAUD;;EAED,MAAIhe,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACsrB,kBAA5B,EAAgD;EAC9C,wBACK/b,KADL;EAEE8b,MAAAA,cAAc,eACT9b,KAAK,CAAC8b,cADG;EAEZwC,QAAAA,MAAM,EAAE,IAFI;EAGZC,QAAAA,gBAAgB,EAAE;EAHN;EAFhB;EAQD;EACF;;EAED,IAAM7f,6BAA2B,GAAG,SAA9BA,2BAA8B,CAAA9I,QAAQ,EAAI;EAAA,MAE5CyL,WAF4C,GAM1CzL,QAN0C,CAE5CyL,WAF4C;EAAA,MAG5Cwd,eAH4C,GAM1CjpB,QAN0C,CAG5CipB,eAH4C;EAAA,MAI5Ctd,QAJ4C,GAM1C3L,QAN0C,CAI5C2L,QAJ4C;EAAA,MAKnCua,cALmC,GAM1ClmB,QAN0C,CAK5CoK,KAL4C,CAKnC8b,cALmC;EAQ9C,MAAMra,WAAW,GAAG3N,YAAY,CAAC8B,QAAD,CAAhC;EAEAyL,EAAAA,WAAW,CAACtO,OAAZ,CAAoB,UAAA6R,MAAM,EAAI;EAC5B,QAAMU,SAAS,GAAGlL,eAAe,CAC/BwK,MAAM,CAACia,eAAP,KAA2B,IAA3B,GAAkC,KAAlC,GAA0C9b,SADX,EAE/B8b,eAAe,KAAK,IAApB,GAA2B,KAA3B,GAAmC9b,SAFJ,EAG/B,IAH+B,CAAjC;EAMA6B,IAAAA,MAAM,CAACU,SAAP,GAAmBA,SAAnB;EACAV,IAAAA,MAAM,CAAC5T,KAAP,GACE8qB,cAAc,CAACuC,YAAf,CAA4BzZ,MAAM,CAACxN,EAAnC,KACAwN,MAAM,CAAC5M,aADP,IAEA4M,MAAM,CAAC5T,KAHT;EAIA4T,IAAAA,MAAM,CAACka,UAAP,GAAoBhD,cAAc,CAACyC,gBAAf,KAAoC3Z,MAAM,CAACxN,EAA/D;;EAEA,QAAIkO,SAAJ,EAAe;EACbV,MAAAA,MAAM,CAACsX,eAAP,GAAyB/pB,cAAc,CAACoP,QAAQ,GAAG2a,eAAZ,EAA6B;EAClEtmB,QAAAA,QAAQ,EAAE6L,WAAW,EAD6C;EAElEmD,QAAAA,MAAM,EAANA;EAFkE,OAA7B,CAAvC;EAID;EACF,GApBD;EAqBD,CA/BD;;EAiCA,SAASjG,aAAT,CAAqB/I,QAArB,EAA+B;EAAA,MACrB1C,OADqB,GACkC0C,QADlC,CACrB1C,OADqB;EAAA,MACZoO,QADY,GACkC1L,QADlC,CACZ0L,QADY;EAAA,8BACkC1L,QADlC,CACFmpB,eADE;EAAA,MACFA,eADE,sCACgB,IADhB;EAAA,MACsBloB,OADtB,GACkCjB,QADlC,CACsBiB,OADtB;EAG7B5D,EAAAA,iBAAiB,CAACC,OAAD,EAAU,CAAC,mBAAD,CAAV,EAAiC,kBAAjC,CAAjB;EAEA,MAAM8rB,kBAAkB,GAAGlrB,YAAY,CAACirB,eAAD,CAAvC;EACAtqB,EAAAA,sBAAsB,CAAC,YAAM;EAC3B,QAAIuqB,kBAAkB,EAAtB,EAA0B;EACxB1d,MAAAA,QAAQ,CAAC;EAAExL,QAAAA,IAAI,EAAErF,OAAO,CAACurB;EAAhB,OAAD,CAAR;EACD;EACF,GAJqB,EAInB,CAACnlB,OAAD,CAJmB,CAAtB;EAMA,MAAMooB,aAAa,GAAGhrB,KAAK,CAACG,WAAN,CACpB;EAAA,WAAMkN,QAAQ,CAAC;EAAExL,MAAAA,IAAI,EAAErF,OAAO,CAACurB;EAAhB,KAAD,CAAd;EAAA,GADoB,EAEpB,CAAC1a,QAAD,CAFoB,CAAtB;EAKAhL,EAAAA,MAAM,CAACsB,MAAP,CAAchC,QAAd,EAAwB;EACtBqpB,IAAAA,aAAa,EAAbA;EADsB,GAAxB;EAGD;;EAED,SAASxC,cAAT,CAAwB7X,MAAxB,EAAgC;EAC9B,MAAMsa,WAAW,GAAG,EAApB;;EACA,MAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAAAva,MAAM,EAAI;EAC9B,QAAIA,MAAM,CAAC/N,OAAP,IAAkB+N,MAAM,CAAC/N,OAAP,CAAeuC,MAArC,EAA6C;EAC3CwL,MAAAA,MAAM,CAAC/N,OAAP,CAAeG,GAAf,CAAmBmoB,aAAnB;EACD;;EACDD,IAAAA,WAAW,CAAC/lB,IAAZ,CAAiByL,MAAjB;EACD,GALD;;EAMAua,EAAAA,aAAa,CAACva,MAAD,CAAb;EACA,SAAOsa,WAAP;EACD;;ECvSD,IAAME,UAAU,GAAG;EACjBhD,EAAAA,QAAQ,EAAE,UADO;EAEjBiD,EAAAA,GAAG,EAAE;EAFY,CAAnB;AAKA,MAAaC,iBAAiB,GAAG,SAApBA,iBAAoB,CAAAltB,KAAK,EAAI;EACxCA,EAAAA,KAAK,CAAC0M,iBAAN,CAAwB3F,IAAxB,CAA6BomB,YAA7B;EACAntB,EAAAA,KAAK,CAAC+M,WAAN,CAAkBhG,IAAlB,CAAuBomB,YAAvB;EACAntB,EAAAA,KAAK,CAAC2M,mBAAN,CAA0B5F,IAA1B,CAA+BomB,YAA/B;EACAntB,EAAAA,KAAK,CAAC4M,mBAAN,CAA0B7F,IAA1B,CAA+BomB,YAA/B;EAEAntB,EAAAA,KAAK,CAAC6M,cAAN,CAAqB9F,IAArB,CAA0B,UAAC3H,KAAD;EAAA,QAAUqE,MAAV,QAAUA,MAAV;EAAA,WAAuB,CAC/CrE,KAD+C,EAE/C;EACEE,MAAAA,KAAK,eACA0tB,UADA;EAEH7a,QAAAA,IAAI,EAAK1O,MAAM,CAACiP,SAAZ,OAFD;EAGH9T,QAAAA,KAAK,EAAK6E,MAAM,CAACmP,UAAZ;EAHF;EADP,KAF+C,CAAvB;EAAA,GAA1B;EAWA5S,EAAAA,KAAK,CAACgN,YAAN,CAAmBjG,IAAnB,CAAwB,UAAC3H,KAAD;EAAA,QAAUoM,IAAV,SAAUA,IAAV;EAAA,WAAqB,CAC3CpM,KAD2C,EAE3C;EACEE,MAAAA,KAAK,eACA0tB,UADA;EAEH7a,QAAAA,IAAI,EAAK3G,IAAI,CAAC/H,MAAL,CAAYiP,SAAjB,OAFD;EAGH9T,QAAAA,KAAK,EAAK4M,IAAI,CAAC/H,MAAL,CAAYmP,UAAjB;EAHF;EADP,KAF2C,CAArB;EAAA,GAAxB;EAWA5S,EAAAA,KAAK,CAAC8M,cAAN,CAAqB/F,IAArB,CAA0B,UAAC3H,KAAD;EAAA,QAAUqE,MAAV,SAAUA,MAAV;EAAA,WAAuB,CAC/CrE,KAD+C,EAE/C;EACEE,MAAAA,KAAK,eACA0tB,UADA;EAEH7a,QAAAA,IAAI,EAAK1O,MAAM,CAACiP,SAAZ,OAFD;EAGH9T,QAAAA,KAAK,EAAK6E,MAAM,CAACmP,UAAZ;EAHF;EADP,KAF+C,CAAvB;EAAA,GAA1B;EAUD,CAtCM;EAwCPsa,iBAAiB,CAAClsB,UAAlB,GAA+B,mBAA/B;;EAEA,IAAMmsB,YAAY,GAAG,SAAfA,YAAe,CAAC/tB,KAAD;EAAA,MAAUoE,QAAV,SAAUA,QAAV;EAAA,SAAyB,CAC5CpE,KAD4C,EAE5C;EACEE,IAAAA,KAAK,EAAE;EACL0qB,MAAAA,QAAQ,EAAE,UADL;EAELprB,MAAAA,KAAK,EAAK4E,QAAQ,CAACoO,iBAAd;EAFA;EADT,GAF4C,CAAzB;EAAA,CAArB;;EC/CA,IAAMob,YAAU,GAAG;EACjBI,EAAAA,OAAO,EAAE,cADQ;EAEjBC,EAAAA,SAAS,EAAE;EAFM,CAAnB;;EAKA,IAAMF,cAAY,GAAG,SAAfA,YAAe,CAAC/tB,KAAD;EAAA,MAAUoE,QAAV,QAAUA,QAAV;EAAA,SAAyB,CAC5CpE,KAD4C,EAE5C;EACEE,IAAAA,KAAK,EAAE;EACL8tB,MAAAA,OAAO,EAAE,MADJ;EAELxuB,MAAAA,KAAK,EAAK4E,QAAQ,CAACoO,iBAAd;EAFA;EADT,GAF4C,CAAzB;EAAA,CAArB;;AAUA,MAAa0b,cAAc,GAAG,SAAjBA,cAAiB,CAAAttB,KAAK,EAAI;EACrCA,EAAAA,KAAK,CAAC+M,WAAN,CAAkBhG,IAAlB,CAAuBomB,cAAvB;EACAntB,EAAAA,KAAK,CAAC2M,mBAAN,CAA0B5F,IAA1B,CAA+BomB,cAA/B;EACAntB,EAAAA,KAAK,CAAC4M,mBAAN,CAA0B7F,IAA1B,CAA+BomB,cAA/B;EAEAntB,EAAAA,KAAK,CAAC6M,cAAN,CAAqB9F,IAArB,CAA0B,UAAC3H,KAAD;EAAA,QAAUqE,MAAV,SAAUA,MAAV;EAAA,WAAuB,CAC/CrE,KAD+C,EAE/C;EACEE,MAAAA,KAAK,eACA0tB,YADA;EAEHpuB,QAAAA,KAAK,EAAK6E,MAAM,CAACmP,UAAZ;EAFF;EADP,KAF+C,CAAvB;EAAA,GAA1B;EAUA5S,EAAAA,KAAK,CAACgN,YAAN,CAAmBjG,IAAnB,CAAwB,UAAC3H,KAAD;EAAA,QAAUoM,IAAV,SAAUA,IAAV;EAAA,WAAqB,CAC3CpM,KAD2C,EAE3C;EACEE,MAAAA,KAAK,eACA0tB,YADA;EAEHpuB,QAAAA,KAAK,EAAK4M,IAAI,CAAC/H,MAAL,CAAYmP,UAAjB;EAFF;EADP,KAF2C,CAArB;EAAA,GAAxB;EAUA5S,EAAAA,KAAK,CAAC8M,cAAN,CAAqB/F,IAArB,CAA0B,UAAC3H,KAAD;EAAA,QAAUqE,MAAV,SAAUA,MAAV;EAAA,WAAuB,CAC/CrE,KAD+C,EAE/C;EACEE,MAAAA,KAAK,eACA0tB,YADA;EAEHpuB,QAAAA,KAAK,EAAK6E,MAAM,CAACmP,UAAZ;EAFF;EADP,KAF+C,CAAvB;EAAA,GAA1B;EASD,CAlCM;EAoCP0a,cAAc,CAACtsB,UAAf,GAA4B,gBAA5B;;ECnDO,SAASusB,aAAT,CAAuBvtB,KAAvB,EAA8B;EACnCA,EAAAA,KAAK,CAACyM,aAAN,CAAoB1F,IAApB,CAAyB0F,aAAzB;EACAzM,EAAAA,KAAK,CAAC+M,WAAN,CAAkBhG,IAAlB,CAAuBomB,cAAvB;EACAntB,EAAAA,KAAK,CAAC2M,mBAAN,CAA0B5F,IAA1B,CAA+BomB,cAA/B;EACAntB,EAAAA,KAAK,CAAC4M,mBAAN,CAA0B7F,IAA1B,CAA+BomB,cAA/B;EACAntB,EAAAA,KAAK,CAAC6M,cAAN,CAAqB9F,IAArB,CAA0B8F,cAA1B;EACA7M,EAAAA,KAAK,CAACgN,YAAN,CAAmBjG,IAAnB,CAAwBiG,YAAxB;EACAhN,EAAAA,KAAK,CAAC8M,cAAN,CAAqB/F,IAArB,CAA0B+F,cAA1B;EACD;EAEDygB,aAAa,CAACvsB,UAAd,GAA2B,eAA3B;;EAEA,IAAMyL,aAAa,GAAG,SAAhBA,aAAgB,CAACrN,KAAD;EAAA,MAAUoE,QAAV,QAAUA,QAAV;EAAA,SAAyB,CAC7CpE,KAD6C,EAE7C;EACEE,IAAAA,KAAK,EAAE;EACLT,MAAAA,QAAQ,EAAK2E,QAAQ,CAACmO,oBAAd;EADH;EADT,GAF6C,CAAzB;EAAA,CAAtB;;EASA,IAAMwb,cAAY,GAAG,SAAfA,YAAe,CAAC/tB,KAAD;EAAA,MAAUoE,QAAV,SAAUA,QAAV;EAAA,SAAyB,CAC5CpE,KAD4C,EAE5C;EACEE,IAAAA,KAAK,EAAE;EACL8tB,MAAAA,OAAO,EAAE,MADJ;EAELI,MAAAA,IAAI,EAAE,UAFD;EAGL3uB,MAAAA,QAAQ,EAAK2E,QAAQ,CAACmO,oBAAd;EAHH;EADT,GAF4C,CAAzB;EAAA,CAArB;;EAWA,IAAM9E,cAAc,GAAG,SAAjBA,cAAiB,CAACzN,KAAD;EAAA,MAAUqE,MAAV,SAAUA,MAAV;EAAA,SAAuB,CAC5CrE,KAD4C,EAE5C;EACEE,IAAAA,KAAK,EAAE;EACL+tB,MAAAA,SAAS,EAAE,YADN;EAELG,MAAAA,IAAI,EAAE/pB,MAAM,CAACqP,cAAP,GACCrP,MAAM,CAACqP,cADR,eAEFnC,SAJC;EAKL9R,MAAAA,QAAQ,EAAK4E,MAAM,CAACkP,aAAZ,OALH;EAML/T,MAAAA,KAAK,EAAK6E,MAAM,CAACmP,UAAZ;EANA;EADT,GAF4C,CAAvB;EAAA,CAAvB;;EAcA,IAAM5F,YAAY,GAAG,SAAfA,YAAe,CAAC5N,KAAD;EAAA,MAAUoM,IAAV,SAAUA,IAAV;EAAA,SAAqB,CACxCpM,KADwC,EAExC;EACEE,IAAAA,KAAK,EAAE;EACL+tB,MAAAA,SAAS,EAAE,YADN;EAELG,MAAAA,IAAI,EAAKhiB,IAAI,CAAC/H,MAAL,CAAYqP,cAAjB,YAFC;EAGLjU,MAAAA,QAAQ,EAAK2M,IAAI,CAAC/H,MAAL,CAAYkP,aAAjB,OAHH;EAIL/T,MAAAA,KAAK,EAAK4M,IAAI,CAAC/H,MAAL,CAAYmP,UAAjB;EAJA;EADT,GAFwC,CAArB;EAAA,CAArB;;EAYA,IAAM9F,cAAc,GAAG,SAAjBA,cAAiB,CAAC1N,KAAD;EAAA,MAAUqE,MAAV,SAAUA,MAAV;EAAA,SAAuB,CAC5CrE,KAD4C,EAE5C;EACEE,IAAAA,KAAK,EAAE;EACL+tB,MAAAA,SAAS,EAAE,YADN;EAELG,MAAAA,IAAI,EAAE/pB,MAAM,CAACqP,cAAP,GACCrP,MAAM,CAACqP,cADR,eAEFnC,SAJC;EAKL9R,MAAAA,QAAQ,EAAK4E,MAAM,CAACkP,aAAZ,OALH;EAML/T,MAAAA,KAAK,EAAK6E,MAAM,CAACmP,UAAZ;EANA;EADT,GAF4C,CAAvB;EAAA,CAAvB;;ECvDAvU,OAAO,CAACorB,mBAAR,GAA8B,qBAA9B;EACAprB,OAAO,CAACqrB,cAAR,GAAyB,gBAAzB;EACArrB,OAAO,CAACsrB,kBAAR,GAA6B,oBAA7B;EACAtrB,OAAO,CAACurB,WAAR,GAAsB,aAAtB;AAEA,EAAO,SAAS6D,aAAT,CAAuBztB,KAAvB,EAA8B;EACnCA,EAAAA,KAAK,CAAC2L,aAAN,CAAoB5E,IAApB,CAAyB4G,SAAzB;EACA3N,EAAAA,KAAK,CAACyM,aAAN,CAAoB1F,IAApB,CAAyB0F,eAAzB;EACAzM,EAAAA,KAAK,CAAC6M,cAAN,CAAqB9F,IAArB,CAA0B8F,gBAA1B;EACA7M,EAAAA,KAAK,CAAC+M,WAAN,CAAkBhG,IAAlB,CAAuBgG,WAAvB;EACD;EAED0gB,aAAa,CAACzsB,UAAd,GAA2B,eAA3B;;EAEA,IAAMyL,eAAa,GAAG,SAAhBA,aAAgB,CAACrN,KAAD,QAAyB;EAAA,MAAfoE,QAAe,QAAfA,QAAe;EAC7C,MAAMkqB,mBAAmB,GAAGlqB,QAAQ,CAAC2I,cAAT,CAAwBvH,GAAxB,CAA4B,UAAAnB,MAAM,EAAI;EAAA;;EAChE,QAAID,QAAQ,CAACoK,KAAT,CAAe+f,UAAf,CAA0B1B,YAA1B,CAAuCxoB,MAAM,CAACuB,EAA9C,CAAJ,EACE,OAAUxB,QAAQ,CAACoK,KAAT,CAAe+f,UAAf,CAA0B1B,YAA1B,CAAuCxoB,MAAM,CAACuB,EAA9C,CAAV,QAF8D;EAIhE;EACA;;EACA,iCAAIxB,QAAQ,CAACoK,KAAT,CAAe8b,cAAnB,qBAAI,sBAA+ByC,gBAAnC,EACE,OAAU3oB,QAAQ,CAACoK,KAAT,CAAe+f,UAAf,CAA0BC,WAA1B,CAAsCnqB,MAAM,CAACuB,EAA7C,CAAV;EACF,QAAI,OAAOvB,MAAM,CAAC7E,KAAd,KAAwB,QAA5B,EAAsC,OAAU6E,MAAM,CAAC7E,KAAjB;EACtC,WAAO6E,MAAM,CAAC7E,KAAd;EACD,GAV2B,CAA5B;EAWA,SAAO,CACLQ,KADK,EAEL;EACEE,IAAAA,KAAK,EAAE;EACL8tB,MAAAA,OAAO,QADF;EAELM,MAAAA,mBAAmB,EAAEA,mBAAmB,CAAChjB,IAApB;EAFhB;EADT,GAFK,CAAP;EASD,CArBD;;EAuBA,IAAMmC,gBAAc,GAAG,SAAjBA,cAAiB,CAACzN,KAAD;EAAA,MAAUqE,MAAV,SAAUA,MAAV;EAAA,SAAuB,CAC5CrE,KAD4C,EAE5C;EACE4F,IAAAA,EAAE,mBAAiBvB,MAAM,CAACuB,EAD5B;EAEE1F,IAAAA,KAAK,EAAE;EACL0qB,MAAAA,QAAQ,UADH;EACe;EACpB6D,MAAAA,UAAU,YAAUpqB,MAAM,CAACwH;EAFtB;EAFT,GAF4C,CAAvB;EAAA,CAAvB;;EAWA,IAAM8B,WAAW,GAAG,SAAdA,WAAc,CAAC3N,KAAD,SAAoB;EAAA,MAAViG,GAAU,SAAVA,GAAU;;EACtC,MAAIA,GAAG,CAAC2D,UAAR,EAAoB;EAClB,WAAO,CACL5J,KADK,EAEL;EACEE,MAAAA,KAAK,EAAE;EACLuuB,QAAAA,UAAU,YAASxoB,GAAG,CAAC6M,KAAJ,CAAUlL,MAAV,GAAmB,CAA5B;EADL;EADT,KAFK,CAAP;EAQD;;EACD,SAAO,CAAC5H,KAAD,EAAQ,EAAR,CAAP;EACD,CAZD;;EAcA,SAASuO,SAAT,CAAiBC,KAAjB,EAAwBU,MAAxB,EAAgCC,aAAhC,EAA+C/K,QAA/C,EAAyD;EACvD,MAAI8K,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACC,IAA5B,EAAkC;EAChC;EACEqvB,MAAAA,UAAU,EAAE;EACV1B,QAAAA,YAAY,EAAE;EADJ;EADd,OAIKre,KAJL;EAMD;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACurB,WAA5B,EAAyC;EACvC,wBACKhc,KADL;EAEE+f,MAAAA,UAAU,EAAE;EACV1B,QAAAA,YAAY,EAAE;EADJ;EAFd;EAMD;;EAED,MAAI3d,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACorB,mBAA5B,EAAiD;EAAA,QACvC/a,QADuC,GACVJ,MADU,CACvCI,QADuC;EAAA,QAC7B4b,cAD6B,GACVhc,MADU,CAC7Bgc,cAD6B;EAE/C,QAAMuB,WAAW,GAAGiC,eAAe,CAACpf,QAAD,CAAnC;;EAEA,QAAImd,WAAW,KAAKlb,SAApB,EAA+B;EAC7B,UAAMid,WAAW,GAAGpqB,QAAQ,CAAC2I,cAAT,CAAwBhN,MAAxB,CAClB,UAAC4uB,GAAD,EAAMtqB,MAAN;EAAA;;EAAA,4BACKsqB,GADL,6BAEGtqB,MAAM,CAACuB,EAFV,IAEe8oB,eAAe,CAACrqB,MAAM,CAACuB,EAAR,CAF9B;EAAA,OADkB,EAKlB,EALkB,CAApB;EAOA,UAAMgpB,SAAS,GAAGxqB,QAAQ,CAAC2I,cAAT,CAAwBhN,MAAxB,CAChB,UAAC4uB,GAAD,EAAMtqB,MAAN;EAAA;;EAAA,4BACKsqB,GADL,6BAEGtqB,MAAM,CAACuB,EAFV,IAEevB,MAAM,CAAC5E,QAFtB;EAAA,OADgB,EAKhB,EALgB,CAAlB;EAOA,UAAMovB,SAAS,GAAGzqB,QAAQ,CAAC2I,cAAT,CAAwBhN,MAAxB,CAChB,UAAC4uB,GAAD,EAAMtqB,MAAN;EAAA;;EAAA,4BACKsqB,GADL,6BAEGtqB,MAAM,CAACuB,EAFV,IAEevB,MAAM,CAAC3E,QAFtB;EAAA,OADgB,EAKhB,EALgB,CAAlB;EAQA,UAAMovB,kBAAkB,GAAG5D,cAAc,CAAC1lB,GAAf,CAAmB;EAAA,YAAE2nB,QAAF;EAAA,eAAgB,CAC5DA,QAD4D,EAE5DuB,eAAe,CAACvB,QAAD,CAF6C,CAAhB;EAAA,OAAnB,CAA3B;EAKA,0BACK3e,KADL;EAEE+f,QAAAA,UAAU,eACL/f,KAAK,CAAC+f,UADD;EAERC,UAAAA,WAAW,EAAXA,WAFQ;EAGRI,UAAAA,SAAS,EAATA,SAHQ;EAIRC,UAAAA,SAAS,EAATA,SAJQ;EAKRC,UAAAA,kBAAkB,EAAlBA,kBALQ;EAMRrC,UAAAA,WAAW,EAAXA;EANQ;EAFZ;EAWD,KAvCD,MAuCO;EACL,aAAOje,KAAP;EACD;EACF;;EAED,MAAIU,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACqrB,cAA5B,EAA4C;EAAA,QAClCa,OADkC,GACtBjc,MADsB,CAClCic,OADkC;EAAA,QAElC2B,MAFkC,GAEvBte,KAAK,CAAC8b,cAFiB,CAElCwC,MAFkC;;EAAA,4BAQtCte,KAAK,CAAC+f,UARgC;EAAA,QAIxC9B,YAJwC,qBAIxCA,WAJwC;EAAA,QAKxCmC,UALwC,qBAKxCA,SALwC;EAAA,QAMxCC,UANwC,qBAMxCA,SANwC;EAAA,kDAOxCC,kBAPwC;EAAA,QAOxCA,mBAPwC,sCAOnB,EAPmB;;EAU1C,QAAM9B,MAAM,GAAG7B,OAAO,GAAG2B,MAAzB;EACA,QAAMG,gBAAgB,GAAGD,MAAM,GAAGP,YAAlC;EAEA,QAAMS,eAAe,GAAG,EAAxB;;EAEA4B,IAAAA,mBAAkB,CAACvtB,OAAnB,CAA2B,iBAA6B;EAAA,UAA3B4rB,QAA2B;EAAA,UAAjBC,WAAiB;EACtDF,MAAAA,eAAe,CAACC,QAAD,CAAf,GAA4BxZ,IAAI,CAACC,GAAL,CAC1BD,IAAI,CAACE,GAAL,CACE+a,UAAS,CAACzB,QAAD,CADX,EAEEC,WAAW,GAAGA,WAAW,GAAGH,gBAF9B,CAD0B,EAK1B4B,UAAS,CAAC1B,QAAD,CALiB,CAA5B;EAOD,KARD;;EAUA,wBACK3e,KADL;EAEE+f,MAAAA,UAAU,eACL/f,KAAK,CAAC+f,UADD;EAER1B,QAAAA,YAAY,eACPre,KAAK,CAAC+f,UAAN,CAAiB1B,YADV,MAEPK,eAFO;EAFJ;EAFZ;EAUD;;EAED,MAAIhe,MAAM,CAAC5K,IAAP,KAAgBrF,OAAO,CAACsrB,kBAA5B,EAAgD;EAC9C,wBACK/b,KADL;EAEE+f,MAAAA,UAAU,eACL/f,KAAK,CAAC+f,UADD;EAERC,QAAAA,WAAW,EAAE,EAFL;EAGRI,QAAAA,SAAS,EAAE,EAHH;EAIRC,QAAAA,SAAS,EAAE;EAJH;EAFZ;EASD;EACF;;EAED,SAASH,eAAT,CAAyBpf,QAAzB,EAAmC;EAAA;;EACjC,MAAM9P,KAAK,4BAAGsD,QAAQ,CAACisB,cAAT,kBAAuCzf,QAAvC,CAAH,qBAAG,sBAAoD0f,WAAlE;;EAEA,MAAIxvB,KAAK,KAAK+R,SAAd,EAAyB;EACvB,WAAO/R,KAAP;EACD;EACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}