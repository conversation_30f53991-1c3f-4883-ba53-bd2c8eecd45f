{"name": "react-table", "version": "7.8.0", "description": "Hooks for building lightweight, fast and extendable datagrids for React", "license": "MIT", "homepage": "https://react-table.tanstack.com", "repository": {"type": "git", "url": "https://github.com/tannerlinsley/react-table"}, "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "table", "react-table", "datagrid"], "main": "index.js", "scripts": {"test": "is-ci \"test:ci\" \"test:dev\"", "test:dev": "jest --watch", "test:ci": "yarn test:jest", "test:jest": "jest --coverage", "test:coverage": "yarn test:jest; open coverage/lcov-report/index.html", "build": "cross-env NODE_ENV=production rollup -c", "start": "rollup -c -w", "prepare": "yarn build", "format": "prettier {src,src/**,examples/*/src,examples/*/src/**}/*.{md,js,jsx,tsx} --write", "docz:build": "docz build", "docz:dev": "docz dev", "docz:serve": "docz build && docz serve"}, "release": {"branches": ["6.x", "master", {"name": "next", "prerelease": true}]}, "files": ["CHANGELOG.md", "src/**/*.js", "dist", "README.md", "scripts/"], "browserslist": "> 0.25%, not dead", "peerDependencies": {"react": "^16.8.3 || ^17.0.0-0 || ^18.0.0"}, "devDependencies": {"@babel/core": "^7.7.2", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.7.1", "@babel/preset-react": "^7.7.0", "@babel/runtime": "^7.7.2", "@rollup/plugin-replace": "^2.3.3", "@svgr/rollup": "^4.3.3", "@testing-library/dom": "^6.10.1", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^13.1.1", "@testing-library/react-hooks": "^3.2.1", "babel-eslint": "9.x", "babel-jest": "^24.9.0", "core-js": "3.4.1", "cross-env": "^6.0.3", "docz": "^2.2.0", "eslint": "6.6.0", "eslint-config-prettier": "^6.7.0", "eslint-config-react-app": "^5.0.2", "eslint-config-standard": "^14.1.0", "eslint-config-standard-react": "^9.2.0", "eslint-plugin-flowtype": "4.4.1", "eslint-plugin-import": "2.18.2", "eslint-plugin-jsx-a11y": "6.2.3", "eslint-plugin-node": "^10.0.0", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "7.16.0", "eslint-plugin-react-hooks": "2.3.0", "eslint-plugin-standard": "^4.0.1", "gatsby-theme-docz": "^2.2.0", "is-ci-cli": "^2.0.0", "jest": "^24.9.0", "jest-cli": "^24.9.0", "jest-diff": "^25.1.0", "jest-runner-eslint": "^0.7.5", "jest-watch-select-projects": "^1.0.0", "jest-watch-typeahead": "^0.4.2", "lint-staged": "^9.4.3", "prettier": "^1.19.1", "prop-types": "^15.5.0", "react": "^18.1.0", "react-dom": "^18.1.0", "react-test-renderer": "^18.1.0", "rollup": "^1.32.1", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-peer-deps-external": "^2.2.0", "rollup-plugin-size": "^0.2.1", "rollup-plugin-size-snapshot": "^0.10.0", "rollup-plugin-terser": "^5.1.2", "serve": "^11.2.0"}}