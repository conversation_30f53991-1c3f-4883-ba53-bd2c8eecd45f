import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import analysisService, { AnalysisListParams, StartAnalysisData } from '@/services/analysisService';
import { AnalysisTask } from '@/types';
import toast from 'react-hot-toast';

export interface UseAnalysisOptions {
  initialParams?: AnalysisListParams;
  enabled?: boolean;
}

export const useAnalysis = (options: UseAnalysisOptions = {}) => {
  const { initialParams = {}, enabled = true } = options;
  const [params, setParams] = useState<AnalysisListParams>({
    page: 1,
    limit: 20,
    ...initialParams
  });

  const queryClient = useQueryClient();

  // 获取分析任务列表
  const {
    data: tasksData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['analysis-tasks', params],
    queryFn: () => analysisService.getAnalysisList(params),
    enabled,
    staleTime: 30 * 1000, // 30秒
    cacheTime: 5 * 60 * 1000, // 5分钟
  });

  // 获取分析统计
  const {
    data: statistics,
    isLoading: isLoadingStats,
    refetch: refetchStats
  } = useQuery({
    queryKey: ['analysis-statistics'],
    queryFn: () => analysisService.getAnalysisStatistics(),
    enabled,
    staleTime: 60 * 1000, // 1分钟
    cacheTime: 5 * 60 * 1000, // 5分钟
  });

  // 开始分析
  const startAnalysisMutation = useMutation({
    mutationFn: (data: StartAnalysisData) => analysisService.startAnalysis(data),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['analysis-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['analysis-statistics'] });
      toast.success('分析任务已开始');
      return result.task;
    },
    onError: (error: any) => {
      toast.error(error.message || '开始分析失败');
    }
  });

  // 取消分析
  const cancelAnalysisMutation = useMutation({
    mutationFn: (taskId: string) => analysisService.cancelAnalysis(taskId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analysis-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['analysis-statistics'] });
      toast.success('分析任务已取消');
    },
    onError: (error: any) => {
      toast.error(error.message || '取消分析失败');
    }
  });

  // 删除分析
  const deleteAnalysisMutation = useMutation({
    mutationFn: (taskId: string) => analysisService.deleteAnalysis(taskId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analysis-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['analysis-statistics'] });
      toast.success('分析任务已删除');
    },
    onError: (error: any) => {
      toast.error(error.message || '删除分析失败');
    }
  });

  // 重试分析
  const retryAnalysisMutation = useMutation({
    mutationFn: (taskId: string) => analysisService.retryAnalysis(taskId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analysis-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['analysis-statistics'] });
      toast.success('分析任务已重试');
    },
    onError: (error: any) => {
      toast.error(error.message || '重试分析失败');
    }
  });

  // 分享分析
  const shareAnalysisMutation = useMutation({
    mutationFn: ({ taskId, options }: { taskId: string; options?: any }) => 
      analysisService.shareAnalysis(taskId, options),
    onSuccess: (result) => {
      toast.success('分析结果已分享');
      return result;
    },
    onError: (error: any) => {
      toast.error(error.message || '分享分析失败');
    }
  });

  // 更新查询参数
  const updateParams = useCallback((newParams: Partial<AnalysisListParams>) => {
    setParams(prev => ({
      ...prev,
      ...newParams,
      page: newParams.page || 1 // 重置页码除非明确指定
    }));
  }, []);

  // 筛选任务
  const filterTasks = useCallback((status?: string, type?: string) => {
    updateParams({ 
      status: status as any, 
      type: type as any, 
      page: 1 
    });
  }, [updateParams]);

  // 分页
  const goToPage = useCallback((page: number) => {
    updateParams({ page });
  }, [updateParams]);

  // 刷新数据
  const refresh = useCallback(() => {
    refetch();
    refetchStats();
  }, [refetch, refetchStats]);

  // 导出分析报告
  const exportAnalysis = useCallback(async (taskId: string, format: 'pdf' | 'excel' | 'word' = 'pdf') => {
    try {
      const blob = await analysisService.exportAnalysis(taskId, format);
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `分析报告_${taskId}.${format === 'pdf' ? 'pdf' : format === 'excel' ? 'xlsx' : 'docx'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('报告导出成功');
    } catch (error: any) {
      toast.error(error.message || '报告导出失败');
    }
  }, []);

  return {
    // 数据
    tasks: tasksData?.tasks || [],
    pagination: tasksData?.pagination,
    statistics,
    
    // 状态
    isLoading,
    isLoadingStats,
    error,
    
    // 查询参数
    params,
    updateParams,
    
    // 操作方法
    filterTasks,
    goToPage,
    refresh,
    exportAnalysis,
    
    // 变更操作
    startAnalysis: startAnalysisMutation.mutate,
    cancelAnalysis: cancelAnalysisMutation.mutate,
    deleteAnalysis: deleteAnalysisMutation.mutate,
    retryAnalysis: retryAnalysisMutation.mutate,
    shareAnalysis: shareAnalysisMutation.mutate,
    
    // 变更状态
    isStarting: startAnalysisMutation.isPending,
    isCancelling: cancelAnalysisMutation.isPending,
    isDeleting: deleteAnalysisMutation.isPending,
    isRetrying: retryAnalysisMutation.isPending,
    isSharing: shareAnalysisMutation.isPending,
  };
};

// 获取单个分析任务详情的Hook
export const useAnalysisTask = (taskId: string, enabled: boolean = true) => {
  const queryClient = useQueryClient();

  const {
    data: task,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['analysis-task', taskId],
    queryFn: () => analysisService.getAnalysisById(taskId),
    enabled: enabled && !!taskId,
    staleTime: 10 * 1000, // 10秒
    cacheTime: 2 * 60 * 1000, // 2分钟
    refetchInterval: (data) => {
      // 如果任务正在运行中，每2秒刷新一次
      if (data?.status === 'running' || data?.status === 'pending') {
        return 2000;
      }
      return false;
    },
  });

  return {
    task,
    isLoading,
    error,
    refetch
  };
};

// 获取分析模板的Hook
export const useAnalysisTemplates = () => {
  const {
    data: templates,
    isLoading,
    error
  } = useQuery({
    queryKey: ['analysis-templates'],
    queryFn: () => analysisService.getAnalysisTemplates(),
    staleTime: 30 * 60 * 1000, // 30分钟
    cacheTime: 60 * 60 * 1000, // 1小时
  });

  return {
    templates: templates || [],
    isLoading,
    error
  };
};

// 任务状态轮询Hook
export const useAnalysisPolling = (taskId: string, enabled: boolean = true) => {
  const [isPolling, setIsPolling] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<any>(null);

  const startPolling = useCallback(async () => {
    if (!taskId || isPolling) return;

    setIsPolling(true);
    try {
      const finalStatus = await analysisService.pollTaskStatus(
        taskId,
        (status) => {
          setCurrentStatus(status);
        }
      );
      setCurrentStatus(finalStatus);
      return finalStatus;
    } catch (error) {
      console.error('轮询分析状态失败:', error);
      throw error;
    } finally {
      setIsPolling(false);
    }
  }, [taskId, isPolling]);

  const stopPolling = useCallback(() => {
    setIsPolling(false);
  }, []);

  return {
    currentStatus,
    isPolling,
    startPolling,
    stopPolling
  };
};

// 产品分析Hook
export const useProductAnalysis = () => {
  const { startAnalysis, isStarting } = useAnalysis();

  const analyzeProduct = useCallback(async (productData: any, options: {
    analysisDepth?: 'basic' | 'detailed' | 'competitive';
    includeMarketAnalysis?: boolean;
    includeCompetitorAnalysis?: boolean;
    includeTrendAnalysis?: boolean;
  } = {}) => {
    const analysisData: StartAnalysisData = {
      type: 'product',
      config: {
        productData,
        analysisDepth: options.analysisDepth || 'detailed',
        includeMarketAnalysis: options.includeMarketAnalysis !== false,
        includeCompetitorAnalysis: options.includeCompetitorAnalysis !== false,
        includeTrendAnalysis: options.includeTrendAnalysis !== false
      },
      priority: 'normal'
    };

    return startAnalysis(analysisData);
  }, [startAnalysis]);

  return {
    analyzeProduct,
    isAnalyzing: isStarting
  };
};
