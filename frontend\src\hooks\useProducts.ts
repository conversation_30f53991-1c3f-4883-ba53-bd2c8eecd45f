import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import productService, { ProductListParams, CreateProductData } from '@/services/productService';
import { Product } from '@/types';
import toast from 'react-hot-toast';

export interface UseProductsOptions {
  initialParams?: ProductListParams;
  enabled?: boolean;
}

export const useProducts = (options: UseProductsOptions = {}) => {
  const { initialParams = {}, enabled = true } = options;
  const [params, setParams] = useState<ProductListParams>({
    page: 1,
    limit: 20,
    ...initialParams
  });

  const queryClient = useQueryClient();

  // 获取产品列表
  const {
    data: productsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['products', params],
    queryFn: () => productService.getProducts(params),
    enabled,
    staleTime: 5 * 60 * 1000, // 5分钟
    cacheTime: 10 * 60 * 1000, // 10分钟
  });

  // 创建产品
  const createProductMutation = useMutation({
    mutationFn: (data: CreateProductData) => productService.createProduct(data),
    onSuccess: (newProduct) => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product-stats'] });
      toast.success('产品创建成功');
    },
    onError: (error: any) => {
      toast.error(error.message || '创建产品失败');
    }
  });

  // 更新产品
  const updateProductMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateProductData> }) => 
      productService.updateProduct(id, data),
    onSuccess: (updatedProduct) => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product', updatedProduct.id] });
      toast.success('产品更新成功');
    },
    onError: (error: any) => {
      toast.error(error.message || '更新产品失败');
    }
  });

  // 删除产品
  const deleteProductMutation = useMutation({
    mutationFn: (id: string) => productService.deleteProduct(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product-stats'] });
      toast.success('产品删除成功');
    },
    onError: (error: any) => {
      toast.error(error.message || '删除产品失败');
    }
  });

  // 批量导入产品
  const batchImportMutation = useMutation({
    mutationFn: (products: CreateProductData[]) => 
      productService.batchImportProducts({ products }),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product-stats'] });
      toast.success(`成功导入 ${result.success} 个产品${result.failed > 0 ? `，失败 ${result.failed} 个` : ''}`);
    },
    onError: (error: any) => {
      toast.error(error.message || '批量导入失败');
    }
  });

  // 切换产品跟踪状态
  const toggleTrackingMutation = useMutation({
    mutationFn: ({ id, isTracked }: { id: string; isTracked: boolean }) => 
      productService.toggleTracking(id, isTracked),
    onSuccess: (updatedProduct) => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product', updatedProduct.id] });
      toast.success(`产品${updatedProduct.is_tracked ? '已开启' : '已关闭'}跟踪`);
    },
    onError: (error: any) => {
      toast.error(error.message || '切换跟踪状态失败');
    }
  });

  // 分析产品
  const analyzeProductMutation = useMutation({
    mutationFn: ({ id, analysisType }: { id: string; analysisType?: 'basic' | 'detailed' | 'competitive' }) => 
      productService.analyzeProduct(id, analysisType),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      toast.success('产品分析已开始，请稍后查看结果');
    },
    onError: (error: any) => {
      toast.error(error.message || '启动产品分析失败');
    }
  });

  // 更新查询参数
  const updateParams = useCallback((newParams: Partial<ProductListParams>) => {
    setParams(prev => ({
      ...prev,
      ...newParams,
      page: newParams.page || 1 // 重置页码除非明确指定
    }));
  }, []);

  // 搜索产品
  const searchProducts = useCallback((query: string) => {
    updateParams({ search: query, page: 1 });
  }, [updateParams]);

  // 筛选产品
  const filterProducts = useCallback((filters: Partial<ProductListParams>) => {
    updateParams({ ...filters, page: 1 });
  }, [updateParams]);

  // 排序产品
  const sortProducts = useCallback((sortBy: string, sortOrder: 'asc' | 'desc' = 'desc') => {
    updateParams({ sortBy, sortOrder, page: 1 });
  }, [updateParams]);

  // 分页
  const goToPage = useCallback((page: number) => {
    updateParams({ page });
  }, [updateParams]);

  // 刷新数据
  const refresh = useCallback(() => {
    refetch();
  }, [refetch]);

  return {
    // 数据
    products: productsData?.products || [],
    pagination: productsData?.pagination,
    
    // 状态
    isLoading,
    error,
    
    // 查询参数
    params,
    updateParams,
    
    // 操作方法
    searchProducts,
    filterProducts,
    sortProducts,
    goToPage,
    refresh,
    
    // 变更操作
    createProduct: createProductMutation.mutate,
    updateProduct: updateProductMutation.mutate,
    deleteProduct: deleteProductMutation.mutate,
    batchImport: batchImportMutation.mutate,
    toggleTracking: toggleTrackingMutation.mutate,
    analyzeProduct: analyzeProductMutation.mutate,
    
    // 变更状态
    isCreating: createProductMutation.isPending,
    isUpdating: updateProductMutation.isPending,
    isDeleting: deleteProductMutation.isPending,
    isBatchImporting: batchImportMutation.isPending,
    isTogglingTracking: toggleTrackingMutation.isPending,
    isAnalyzing: analyzeProductMutation.isPending,
  };
};

// 获取单个产品详情的Hook
export const useProduct = (id: string, enabled: boolean = true) => {
  const queryClient = useQueryClient();

  const {
    data: product,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['product', id],
    queryFn: () => productService.getProductById(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
    cacheTime: 10 * 60 * 1000,
  });

  return {
    product,
    isLoading,
    error,
    refetch
  };
};

// 获取产品统计信息的Hook
export const useProductStats = () => {
  const {
    data: stats,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['product-stats'],
    queryFn: () => productService.getProductStats(),
    staleTime: 10 * 60 * 1000, // 10分钟
    cacheTime: 15 * 60 * 1000, // 15分钟
  });

  return {
    stats,
    isLoading,
    error,
    refetch
  };
};

// 获取产品分类的Hook
export const useProductCategories = () => {
  const {
    data: categories,
    isLoading,
    error
  } = useQuery({
    queryKey: ['product-categories'],
    queryFn: () => productService.getCategories(),
    staleTime: 30 * 60 * 1000, // 30分钟
    cacheTime: 60 * 60 * 1000, // 1小时
  });

  return {
    categories: categories || [],
    isLoading,
    error
  };
};

// 获取品牌列表的Hook
export const useProductBrands = () => {
  const {
    data: brands,
    isLoading,
    error
  } = useQuery({
    queryKey: ['product-brands'],
    queryFn: () => productService.getBrands(),
    staleTime: 30 * 60 * 1000, // 30分钟
    cacheTime: 60 * 60 * 1000, // 1小时
  });

  return {
    brands: brands || [],
    isLoading,
    error
  };
};
