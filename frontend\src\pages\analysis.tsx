import React, { useState } from 'react';
import { useRouter } from 'next/router';
import {
  PlusIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  ArrowPathIcon,
  TrashIcon,
  ShareIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import { useAnalysis, useAnalysisTemplates } from '@/hooks/useAnalysis';
import analysisService from '@/services/analysisService';
import toast from 'react-hot-toast';

const AnalysisPage: React.FC = () => {
  const { isAuthenticated, isLoading: authLoading } = useRequireAuth();
  const router = useRouter();
  
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showNewAnalysisModal, setShowNewAnalysisModal] = useState(false);

  // 使用分析管理Hook
  const {
    tasks,
    statistics,
    isLoading: isLoadingTasks,
    isLoadingStats,
    startAnalysis,
    cancelAnalysis,
    deleteAnalysis,
    retryAnalysis,
    shareAnalysis,
    exportAnalysis,
    filterTasks,
    goToPage,
    refresh,
    isStarting,
    isCancelling,
    isDeleting,
    isRetrying,
    isSharing
  } = useAnalysis({ initialParams: { limit: 12 } });

  // 获取分析模板
  const { templates } = useAnalysisTemplates();

  if (authLoading) {
    return <LoadingScreen message="加载智能分析..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // 处理类型筛选
  const handleTypeFilter = (type: string) => {
    setSelectedType(type);
    filterTasks(selectedStatus === 'all' ? undefined : selectedStatus, type === 'all' ? undefined : type);
  };

  // 处理状态筛选
  const handleStatusFilter = (status: string) => {
    setSelectedStatus(status);
    filterTasks(status === 'all' ? undefined : status, selectedType === 'all' ? undefined : selectedType);
  };

  // 获取任务状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      case 'running':
        return <div className="loading-spinner w-5 h-5"></div>;
      default:
        return <ClockIcon className="w-5 h-5 text-yellow-500" />;
    }
  };

  // 处理任务操作
  const handleViewTask = (taskId: string) => {
    router.push(`/analysis/${taskId}`);
  };

  const handleCancelTask = async (taskId: string) => {
    try {
      await cancelAnalysis(taskId);
    } catch (error) {
      // 错误已在Hook中处理
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    if (window.confirm('确定要删除这个分析任务吗？此操作无法撤销。')) {
      try {
        await deleteAnalysis(taskId);
      } catch (error) {
        // 错误已在Hook中处理
      }
    }
  };

  const handleRetryTask = async (taskId: string) => {
    try {
      await retryAnalysis(taskId);
    } catch (error) {
      // 错误已在Hook中处理
    }
  };

  const handleShareTask = async (taskId: string) => {
    try {
      const result = await shareAnalysis({ taskId });
      if (result) {
        navigator.clipboard.writeText(result.shareUrl);
        toast.success('分享链接已复制到剪贴板');
      }
    } catch (error) {
      // 错误已在Hook中处理
    }
  };

  const handleExportTask = async (taskId: string, format: 'pdf' | 'excel' | 'word' = 'pdf') => {
    try {
      await exportAnalysis(taskId, format);
    } catch (error) {
      // 错误已在Hook中处理
    }
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const analysisTypes = [
    { value: 'all', label: '全部类型' },
    { value: 'product', label: '产品分析' },
    { value: 'market', label: '市场分析' },
    { value: 'competitor', label: '竞争对手分析' },
    { value: 'trend', label: '趋势分析' }
  ];

  const analysisStatuses = [
    { value: 'all', label: '全部状态' },
    { value: 'pending', label: '等待中' },
    { value: 'running', label: '分析中' },
    { value: 'completed', label: '已完成' },
    { value: 'failed', label: '失败' }
  ];

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: '700',
                  color: 'var(--color-gray-900)',
                  marginBottom: '0.5rem'
                }}>
                  智能分析
                </h1>
                <p style={{
                  fontSize: '1rem',
                  color: 'var(--color-gray-600)'
                }}>
                  AI驱动的产品分析和市场洞察
                </p>
              </div>
              <button 
                className="btn btn-primary"
                onClick={() => setShowNewAnalysisModal(true)}
              >
                <PlusIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                新建分析
              </button>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        {statistics && (
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-body">
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                marginBottom: '1rem'
              }}>
                分析统计
              </h2>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '1rem'
              }}>
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-primary-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-primary-200)'
                }}>
                  <div style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    color: 'var(--color-primary-600)'
                  }}>
                    {statistics.totalTasks}
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-primary-700)'
                  }}>
                    总任务数
                  </div>
                </div>
                
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-success-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-success-200)'
                }}>
                  <div style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    color: 'var(--color-success-600)'
                  }}>
                    {statistics.completedTasks}
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-success-700)'
                  }}>
                    已完成
                  </div>
                </div>
                
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-info-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-info-200)'
                }}>
                  <div style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    color: 'var(--color-info-600)'
                  }}>
                    {statistics.runningTasks}
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-info-700)'
                  }}>
                    进行中
                  </div>
                </div>
                
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-warning-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-warning-200)'
                }}>
                  <div style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    color: 'var(--color-warning-600)'
                  }}>
                    {Math.round(statistics.successRate)}%
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-warning-700)'
                  }}>
                    成功率
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 筛选器 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              gap: '1rem',
              alignItems: 'center',
              flexWrap: 'wrap'
            }}>
              <div>
                <label style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  marginBottom: '0.5rem',
                  display: 'block'
                }}>
                  分析类型
                </label>
                <select
                  className="input"
                  value={selectedType}
                  onChange={(e) => handleTypeFilter(e.target.value)}
                  style={{ minWidth: '150px' }}
                >
                  {analysisTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  marginBottom: '0.5rem',
                  display: 'block'
                }}>
                  任务状态
                </label>
                <select
                  className="input"
                  value={selectedStatus}
                  onChange={(e) => handleStatusFilter(e.target.value)}
                  style={{ minWidth: '150px' }}
                >
                  {analysisStatuses.map(status => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div style={{ marginLeft: 'auto' }}>
                <button
                  className="btn btn-ghost"
                  onClick={refresh}
                >
                  刷新
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 分析任务列表 */}
        <div className="card">
          <div className="card-header">
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: '600'
            }}>
              分析任务
            </h2>
          </div>
          <div className="card-body">
            {isLoadingTasks ? (
              <div style={{ textAlign: 'center', padding: '3rem' }}>
                <div className="loading-spinner" style={{ width: '2rem', height: '2rem', margin: '0 auto' }}></div>
                <p style={{ marginTop: '1rem', color: 'var(--color-gray-600)' }}>
                  加载分析任务...
                </p>
              </div>
            ) : tasks.length > 0 ? (
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
                gap: '1.5rem'
              }}>
                {tasks.map((task) => (
                  <div key={task.id} className="card" style={{
                    border: '1px solid var(--color-gray-200)',
                    background: 'var(--color-gray-50)'
                  }}>
                    <div className="card-body">
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start',
                        marginBottom: '1rem'
                      }}>
                        <div style={{ flex: 1 }}>
                          <h3 style={{
                            fontSize: '1rem',
                            fontWeight: '600',
                            marginBottom: '0.5rem',
                            color: 'var(--color-gray-900)'
                          }}>
                            {task.title}
                          </h3>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            marginBottom: '0.5rem'
                          }}>
                            <span className="badge badge-secondary">
                              {analysisService.getTypeText(task.type)}
                            </span>
                            <span className={`badge badge-${analysisService.getStatusColor(task.status)}`}>
                              {analysisService.getStatusText(task.status)}
                            </span>
                          </div>
                          <p style={{
                            fontSize: '0.75rem',
                            color: 'var(--color-gray-600)'
                          }}>
                            创建时间: {formatTime(task.createdAt)}
                          </p>
                        </div>
                        
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem'
                        }}>
                          {getStatusIcon(task.status)}
                        </div>
                      </div>
                      
                      {task.status === 'running' && task.progress && (
                        <div style={{ marginBottom: '1rem' }}>
                          <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: '0.5rem'
                          }}>
                            <span style={{ fontSize: '0.75rem', fontWeight: '500' }}>
                              分析进度
                            </span>
                            <span style={{ fontSize: '0.75rem', color: 'var(--color-gray-600)' }}>
                              {task.progress}%
                            </span>
                          </div>
                          <div style={{
                            width: '100%',
                            height: '6px',
                            background: 'var(--color-gray-200)',
                            borderRadius: '3px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              width: `${task.progress}%`,
                              height: '100%',
                              background: 'var(--color-primary-500)',
                              transition: 'width 0.3s ease'
                            }}></div>
                          </div>
                        </div>
                      )}
                      
                      <div style={{
                        display: 'flex',
                        gap: '0.5rem',
                        flexWrap: 'wrap'
                      }}>
                        <button
                          className="btn btn-ghost btn-sm"
                          onClick={() => handleViewTask(task.id)}
                        >
                          <EyeIcon style={{ width: '1rem', height: '1rem' }} />
                          查看
                        </button>
                        
                        {task.status === 'running' && (
                          <button
                            className="btn btn-ghost btn-sm"
                            onClick={() => handleCancelTask(task.id)}
                            disabled={isCancelling}
                          >
                            取消
                          </button>
                        )}
                        
                        {task.status === 'failed' && (
                          <button
                            className="btn btn-ghost btn-sm"
                            onClick={() => handleRetryTask(task.id)}
                            disabled={isRetrying}
                          >
                            <ArrowPathIcon style={{ width: '1rem', height: '1rem' }} />
                            重试
                          </button>
                        )}
                        
                        {task.status === 'completed' && (
                          <>
                            <button
                              className="btn btn-ghost btn-sm"
                              onClick={() => handleShareTask(task.id)}
                              disabled={isSharing}
                            >
                              <ShareIcon style={{ width: '1rem', height: '1rem' }} />
                              分享
                            </button>
                            <button
                              className="btn btn-ghost btn-sm"
                              onClick={() => handleExportTask(task.id)}
                            >
                              <DocumentArrowDownIcon style={{ width: '1rem', height: '1rem' }} />
                              导出
                            </button>
                          </>
                        )}
                        
                        <button
                          className="btn btn-ghost btn-sm"
                          onClick={() => handleDeleteTask(task.id)}
                          disabled={isDeleting}
                          style={{ color: 'var(--color-error-600)' }}
                        >
                          <TrashIcon style={{ width: '1rem', height: '1rem' }} />
                          删除
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                color: 'var(--color-gray-500)'
              }}>
                <ChartBarIcon style={{
                  width: '4rem',
                  height: '4rem',
                  margin: '0 auto 1rem',
                  color: 'var(--color-gray-300)'
                }} />
                <h3 style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  marginBottom: '0.5rem'
                }}>
                  暂无分析任务
                </h3>
                <p style={{ marginBottom: '1.5rem' }}>
                  点击"新建分析"开始您的第一个AI分析任务
                </p>
                <button 
                  className="btn btn-primary"
                  onClick={() => setShowNewAnalysisModal(true)}
                >
                  <PlusIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                  新建分析
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalysisPage;
