import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  DocumentArrowDownIcon,
  ArrowPathIcon,
  TrashIcon,
  ShareIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import { useAnalysisTask, useAnalysisPolling } from '@/hooks/useAnalysis';
import analysisService from '@/services/analysisService';
import toast from 'react-hot-toast';

const AnalysisDetailPage: React.FC = () => {
  const { isAuthenticated, isLoading: authLoading } = useRequireAuth();
  const router = useRouter();
  const { id } = router.query;
  const taskId = typeof id === 'string' ? id : '';

  // 获取任务详情
  const { task, isLoading, refetch } = useAnalysisTask(taskId);
  
  // 任务轮询（用于实时更新处理中的任务）
  const { currentStatus, isPolling, startPolling, stopPolling } = useAnalysisPolling(taskId);

  // 使用轮询的任务状态或API获取的任务数据
  const displayTask = task;
  const displayStatus = currentStatus || (task ? {
    id: task.id,
    status: task.status,
    progress: task.progress || 0
  } : null);

  useEffect(() => {
    if (displayTask && (displayTask.status === 'running' || displayTask.status === 'pending')) {
      startPolling();
    } else {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [displayTask?.status, startPolling, stopPolling]);

  if (authLoading || isLoading) {
    return <LoadingScreen message="加载分析详情..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  if (!displayTask) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div className="card" style={{ maxWidth: '400px', textAlign: 'center' }}>
          <div className="card-body">
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '1rem' }}>
              分析任务不存在
            </h1>
            <p style={{ color: 'var(--color-gray-600)', marginBottom: '2rem' }}>
              找不到指定的分析任务
            </p>
            <Link href="/analysis" className="btn btn-primary">
              返回分析页面
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // 获取任务状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-8 h-8 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="w-8 h-8 text-red-500" />;
      case 'running':
        return <div className="loading-spinner w-8 h-8"></div>;
      default:
        return <ClockIcon className="w-8 h-8 text-yellow-500" />;
    }
  };

  // 处理重试
  const handleRetry = async () => {
    try {
      await analysisService.retryAnalysis(taskId);
      toast.success('分析任务已重试');
      refetch();
    } catch (error: any) {
      toast.error(error.message || '重试失败');
    }
  };

  // 处理删除
  const handleDelete = async () => {
    if (!window.confirm('确定要删除这个分析任务吗？此操作无法撤销。')) {
      return;
    }

    try {
      await analysisService.deleteAnalysis(taskId);
      toast.success('分析任务已删除');
      router.push('/analysis');
    } catch (error: any) {
      toast.error(error.message || '删除失败');
    }
  };

  // 处理分享
  const handleShare = async () => {
    try {
      const result = await analysisService.shareAnalysis(taskId);
      navigator.clipboard.writeText(result.shareUrl);
      toast.success('分享链接已复制到剪贴板');
    } catch (error: any) {
      toast.error(error.message || '分享失败');
    }
  };

  // 导出报告
  const handleExportReport = async (format: 'pdf' | 'excel' | 'word') => {
    try {
      const blob = await analysisService.exportAnalysis(taskId, format);
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const extensions = { pdf: 'pdf', excel: 'xlsx', word: 'docx' };
      link.download = `分析报告_${taskId}.${extensions[format]}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('报告导出成功');
    } catch (error: any) {
      toast.error(error.message || '导出失败');
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const formatDuration = (ms: number) => {
    return analysisService.formatDuration(ms);
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 返回按钮 */}
        <div style={{ marginBottom: '2rem' }}>
          <Link
            href="/analysis"
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem',
              color: 'var(--color-primary-600)',
              textDecoration: 'none',
              fontSize: '0.875rem',
              fontWeight: '500'
            }}
          >
            <ArrowLeftIcon style={{ width: '1rem', height: '1rem' }} />
            返回分析页面
          </Link>
        </div>

        {/* 任务概览 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: '2rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                {getStatusIcon(displayTask.status)}
                <div>
                  <h1 style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    color: 'var(--color-gray-900)',
                    marginBottom: '0.5rem'
                  }}>
                    {displayTask.title}
                  </h1>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '1rem',
                    fontSize: '0.875rem',
                    color: 'var(--color-gray-600)'
                  }}>
                    <span>类型: {analysisService.getTypeText(displayTask.type)}</span>
                    <span>创建时间: {formatTime(displayTask.createdAt)}</span>
                    {displayTask.completedAt && (
                      <span>完成时间: {formatTime(displayTask.completedAt)}</span>
                    )}
                    {displayTask.duration && (
                      <span>耗时: {formatDuration(displayTask.duration)}</span>
                    )}
                  </div>
                </div>
              </div>
              
              <div style={{ display: 'flex', gap: '0.5rem' }}>
                <span className={`badge badge-${analysisService.getStatusColor(displayTask.status)}`}>
                  {analysisService.getStatusText(displayTask.status)}
                </span>
                {isPolling && (
                  <span className="badge badge-info">实时更新中</span>
                )}
              </div>
            </div>

            {/* 进度信息 */}
            {displayTask.status === 'running' && displayStatus?.progress !== undefined && (
              <div style={{ marginBottom: '2rem' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '0.5rem'
                }}>
                  <span style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                    分析进度
                  </span>
                  <span style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    {displayStatus.progress}%
                  </span>
                </div>
                <div style={{
                  width: '100%',
                  height: '8px',
                  background: 'var(--color-gray-200)',
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${displayStatus.progress}%`,
                    height: '100%',
                    background: 'var(--color-primary-500)',
                    transition: 'width 0.3s ease'
                  }}></div>
                </div>
              </div>
            )}

            {/* 错误信息 */}
            {displayTask.status === 'failed' && displayTask.errorMessage && (
              <div style={{
                padding: '1rem',
                background: 'var(--color-error-50)',
                border: '1px solid var(--color-error-200)',
                borderRadius: 'var(--radius-lg)',
                marginBottom: '2rem'
              }}>
                <h3 style={{
                  fontSize: '1rem',
                  fontWeight: '600',
                  color: 'var(--color-error-800)',
                  marginBottom: '0.5rem'
                }}>
                  错误信息
                </h3>
                <p style={{
                  fontSize: '0.875rem',
                  color: 'var(--color-error-700)',
                  margin: 0
                }}>
                  {displayTask.errorMessage}
                </p>
              </div>
            )}

            {/* 操作按钮 */}
            <div style={{
              display: 'flex',
              gap: '1rem',
              paddingTop: '2rem',
              borderTop: '1px solid var(--color-gray-200)'
            }}>
              {displayTask.status === 'completed' && (
                <>
                  <button
                    className="btn btn-primary"
                    onClick={() => handleExportReport('pdf')}
                  >
                    <DocumentArrowDownIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                    导出PDF
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={() => handleExportReport('excel')}
                  >
                    <DocumentArrowDownIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                    导出Excel
                  </button>
                  <button
                    className="btn btn-ghost"
                    onClick={handleShare}
                  >
                    <ShareIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                    分享结果
                  </button>
                </>
              )}
              
              {displayTask.status === 'failed' && (
                <button
                  className="btn btn-primary"
                  onClick={handleRetry}
                >
                  <ArrowPathIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                  重试分析
                </button>
              )}
              
              <button
                className="btn btn-ghost"
                onClick={handleDelete}
                style={{ color: 'var(--color-error-600)' }}
              >
                <TrashIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                删除任务
              </button>
            </div>
          </div>
        </div>

        {/* 分析结果 */}
        {displayTask.status === 'completed' && displayTask.result && (
          <div className="card">
            <div className="card-header">
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600'
              }}>
                分析结果
              </h2>
            </div>
            <div className="card-body">
              <div style={{
                background: 'var(--color-gray-50)',
                padding: '1.5rem',
                borderRadius: 'var(--radius-lg)',
                border: '1px solid var(--color-gray-200)'
              }}>
                <pre style={{
                  whiteSpace: 'pre-wrap',
                  fontSize: '0.875rem',
                  lineHeight: '1.6',
                  margin: 0,
                  color: 'var(--color-gray-800)'
                }}>
                  {typeof displayTask.result === 'string' 
                    ? displayTask.result 
                    : JSON.stringify(displayTask.result, null, 2)
                  }
                </pre>
              </div>
            </div>
          </div>
        )}

        {/* 等待或处理中状态 */}
        {(displayTask.status === 'pending' || displayTask.status === 'running') && (
          <div className="card">
            <div className="card-body" style={{ textAlign: 'center', padding: '3rem' }}>
              <div className="loading-spinner" style={{ 
                width: '3rem', 
                height: '3rem', 
                margin: '0 auto 1rem' 
              }}></div>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                marginBottom: '0.5rem'
              }}>
                {displayTask.status === 'pending' ? '等待分析中...' : '正在分析中...'}
              </h3>
              <p style={{ color: 'var(--color-gray-600)' }}>
                AI正在处理您的分析请求，请耐心等待
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AnalysisDetailPage;
