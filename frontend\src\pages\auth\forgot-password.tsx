import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { EnvelopeIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { LoadingSpinner } from '@/components/LoadingScreen';
import apiService from '@/services/api';
import toast from 'react-hot-toast';

const ForgotPasswordPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);
  
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // 如果已经登录，重定向到仪表板
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, authLoading, router]);

  // 如果正在加载认证状态，显示加载界面
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // 如果已经认证，不显示忘记密码表单
  if (isAuthenticated) {
    return null;
  }

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      toast.error('请输入邮箱地址');
      return;
    }

    if (!validateEmail(email)) {
      toast.error('请输入有效的邮箱地址');
      return;
    }

    setIsLoading(true);
    
    try {
      await apiService.post('/auth/forgot-password', { email });
      setIsEmailSent(true);
      toast.success('重置密码邮件已发送，请检查您的邮箱');
    } catch (error: any) {
      toast.error(error.message || '发送重置邮件失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendEmail = async () => {
    setIsLoading(true);
    
    try {
      await apiService.post('/auth/forgot-password', { email });
      toast.success('重置密码邮件已重新发送');
    } catch (error: any) {
      toast.error(error.message || '重新发送邮件失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '3rem 1rem'
    }}>
      <div className="card" style={{ maxWidth: '28rem', width: '100%' }}>
        <div className="card-body" style={{ padding: '2rem' }}>
          <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
            <div style={{
              width: '3rem',
              height: '3rem',
              margin: '0 auto 1.5rem',
              background: 'linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: 'var(--shadow-lg)'
            }}>
              <EnvelopeIcon style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />
            </div>
            <h1 style={{
              fontSize: '1.875rem',
              fontWeight: '700',
              color: 'var(--color-gray-900)',
              marginBottom: '0.5rem'
            }}>
              {isEmailSent ? '邮件已发送' : '重置密码'}
            </h1>
            <p style={{
              fontSize: '0.875rem',
              color: 'var(--color-gray-600)'
            }}>
              {isEmailSent 
                ? '我们已向您的邮箱发送了重置密码的链接'
                : '输入您的邮箱地址，我们将发送重置密码的链接'
              }
            </p>
          </div>

          {!isEmailSent ? (
            <form onSubmit={handleSubmit} style={{ marginTop: '1.5rem' }}>
              <div style={{ marginBottom: '1.5rem' }}>
                <div className="form-group">
                  <div className="input-group">
                    <input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="input input-floating"
                      placeholder="请输入邮箱地址"
                    />
                    <label htmlFor="email" className="input-label">
                      邮箱地址
                    </label>
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="btn btn-primary"
                style={{ width: '100%', marginBottom: '1rem' }}
              >
                {isLoading ? (
                  <>
                    <div className="loading-spinner" style={{ width: '1rem', height: '1rem' }}></div>
                    发送中...
                  </>
                ) : (
                  '发送重置链接'
                )}
              </button>

              <div style={{
                textAlign: 'center',
                paddingTop: '1.5rem',
                borderTop: '1px solid var(--color-gray-200)'
              }}>
                <Link
                  href="/auth/login"
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    fontSize: '0.875rem',
                    color: 'var(--color-primary-600)',
                    textDecoration: 'none',
                    fontWeight: '500'
                  }}
                >
                  <ArrowLeftIcon style={{ width: '1rem', height: '1rem' }} />
                  返回登录
                </Link>
              </div>
            </form>
          ) : (
            <div style={{ textAlign: 'center' }}>
              <div style={{
                background: 'var(--color-success-50)',
                border: '1px solid var(--color-success-200)',
                borderRadius: 'var(--radius-lg)',
                padding: '1rem',
                marginBottom: '1.5rem'
              }}>
                <p style={{
                  fontSize: '0.875rem',
                  color: 'var(--color-success-700)',
                  margin: 0
                }}>
                  重置密码邮件已发送到 <strong>{email}</strong>
                </p>
              </div>

              <div style={{
                fontSize: '0.875rem',
                color: 'var(--color-gray-600)',
                marginBottom: '1.5rem'
              }}>
                <p style={{ marginBottom: '0.5rem' }}>
                  请检查您的邮箱（包括垃圾邮件文件夹）并点击邮件中的链接来重置密码。
                </p>
                <p style={{ margin: 0 }}>
                  如果您在几分钟内没有收到邮件，请检查邮箱地址是否正确。
                </p>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                <button
                  onClick={handleResendEmail}
                  disabled={isLoading}
                  className="btn btn-secondary"
                  style={{ width: '100%' }}
                >
                  {isLoading ? (
                    <>
                      <div className="loading-spinner" style={{ width: '1rem', height: '1rem' }}></div>
                      重新发送中...
                    </>
                  ) : (
                    '重新发送邮件'
                  )}
                </button>

                <Link
                  href="/auth/login"
                  className="btn btn-ghost"
                  style={{ 
                    width: '100%',
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5rem'
                  }}
                >
                  <ArrowLeftIcon style={{ width: '1rem', height: '1rem' }} />
                  返回登录
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
