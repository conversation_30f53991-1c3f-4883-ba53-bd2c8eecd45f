import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { LoadingSpinner } from '@/components/LoadingScreen';
import toast from 'react-hot-toast';

const RegisterPage: React.FC = () => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const { register, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // 如果已经登录，重定向到仪表板
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, authLoading, router]);

  // 如果正在加载认证状态，显示加载界面
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // 如果已经认证，不显示注册表单
  if (isAuthenticated) {
    return null;
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const validateForm = () => {
    if (!formData.username.trim()) {
      toast.error('请输入用户名');
      return false;
    }
    
    if (formData.username.length < 3) {
      toast.error('用户名至少需要3个字符');
      return false;
    }

    if (!formData.email.trim()) {
      toast.error('请输入邮箱地址');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error('请输入有效的邮箱地址');
      return false;
    }

    if (!formData.password) {
      toast.error('请输入密码');
      return false;
    }

    if (formData.password.length < 6) {
      toast.error('密码至少需要6个字符');
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error('两次输入的密码不一致');
      return false;
    }

    if (!formData.agreeToTerms) {
      toast.error('请同意服务条款和隐私政策');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      await register({
        username: formData.username,
        email: formData.email,
        password: formData.password
      });
      toast.success('注册成功，欢迎使用SmartPick！');
      router.push('/dashboard');
    } catch (error: any) {
      toast.error(error.message || '注册失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '3rem 1rem'
    }}>
      <div className="card" style={{ maxWidth: '28rem', width: '100%' }}>
        <div className="card-body" style={{ padding: '2rem' }}>
          <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
            <div style={{
              width: '3rem',
              height: '3rem',
              margin: '0 auto 1.5rem',
              background: 'linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: 'var(--shadow-lg)'
            }}>
              <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
              </svg>
            </div>
            <h1 style={{
              fontSize: '1.875rem',
              fontWeight: '700',
              color: 'var(--color-gray-900)',
              marginBottom: '0.5rem'
            }}>
              创建账户
            </h1>
            <p style={{
              fontSize: '0.875rem',
              color: 'var(--color-gray-600)'
            }}>
              加入 SmartPick 智能选品分析平台
            </p>
          </div>
        
          <form onSubmit={handleSubmit} style={{ marginTop: '1.5rem' }}>
            <div style={{ marginBottom: '1.5rem' }}>
              <div className="form-group">
                <div className="input-group">
                  <input
                    id="username"
                    name="username"
                    type="text"
                    autoComplete="username"
                    required
                    value={formData.username}
                    onChange={handleInputChange}
                    className="input input-floating"
                    placeholder="请输入用户名"
                  />
                  <label htmlFor="username" className="input-label">
                    用户名
                  </label>
                </div>
              </div>
            
              <div className="form-group">
                <div className="input-group">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="input input-floating"
                    placeholder="请输入邮箱地址"
                  />
                  <label htmlFor="email" className="input-label">
                    邮箱地址
                  </label>
                </div>
              </div>
            
              <div className="form-group">
                <div className="input-group">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    value={formData.password}
                    onChange={handleInputChange}
                    className="input input-floating"
                    placeholder="请输入密码"
                    style={{ paddingRight: '3rem' }}
                  />
                  <label htmlFor="password" className="input-label">
                    密码
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    style={{
                      position: 'absolute',
                      right: '0.75rem',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      color: 'var(--color-gray-400)',
                      padding: '0.25rem'
                    }}
                  >
                    {showPassword ? (
                      <EyeSlashIcon style={{ width: '1rem', height: '1rem' }} />
                    ) : (
                      <EyeIcon style={{ width: '1rem', height: '1rem' }} />
                    )}
                  </button>
                </div>
              </div>

              <div className="form-group">
                <div className="input-group">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="input input-floating"
                    placeholder="请再次输入密码"
                    style={{ paddingRight: '3rem' }}
                  />
                  <label htmlFor="confirmPassword" className="input-label">
                    确认密码
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    style={{
                      position: 'absolute',
                      right: '0.75rem',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      color: 'var(--color-gray-400)',
                      padding: '0.25rem'
                    }}
                  >
                    {showConfirmPassword ? (
                      <EyeSlashIcon style={{ width: '1rem', height: '1rem' }} />
                    ) : (
                      <EyeIcon style={{ width: '1rem', height: '1rem' }} />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'flex',
                alignItems: 'flex-start',
                gap: '0.75rem',
                fontSize: '0.875rem',
                color: 'var(--color-gray-700)',
                cursor: 'pointer'
              }}>
                <input
                  id="agreeToTerms"
                  name="agreeToTerms"
                  type="checkbox"
                  checked={formData.agreeToTerms}
                  onChange={handleInputChange}
                  style={{
                    width: '1rem',
                    height: '1rem',
                    marginTop: '0.125rem',
                    accentColor: 'var(--color-primary-500)'
                  }}
                />
                <span>
                  我已阅读并同意{' '}
                  <Link
                    href="/terms"
                    style={{
                      color: 'var(--color-primary-600)',
                      textDecoration: 'none',
                      fontWeight: '500'
                    }}
                  >
                    服务条款
                  </Link>
                  {' '}和{' '}
                  <Link
                    href="/privacy"
                    style={{
                      color: 'var(--color-primary-600)',
                      textDecoration: 'none',
                      fontWeight: '500'
                    }}
                  >
                    隐私政策
                  </Link>
                </span>
              </label>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="btn btn-primary"
              style={{ width: '100%', marginBottom: '1rem' }}
            >
              {isLoading ? (
                <>
                  <div className="loading-spinner" style={{ width: '1rem', height: '1rem' }}></div>
                  注册中...
                </>
              ) : (
                '创建账户'
              )}
            </button>

            <div style={{
              textAlign: 'center',
              paddingTop: '1.5rem',
              borderTop: '1px solid var(--color-gray-200)'
            }}>
              <span style={{
                fontSize: '0.875rem',
                color: 'var(--color-gray-600)'
              }}>
                已有账户？{' '}
                <Link
                  href="/auth/login"
                  style={{
                    color: 'var(--color-primary-600)',
                    textDecoration: 'none',
                    fontWeight: '500'
                  }}
                >
                  立即登录
                </Link>
              </span>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
