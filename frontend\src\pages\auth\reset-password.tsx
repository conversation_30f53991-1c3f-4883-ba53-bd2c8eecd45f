import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { EyeIcon, EyeSlashIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { LoadingSpinner } from '@/components/LoadingScreen';
import apiService from '@/services/api';
import toast from 'react-hot-toast';

const ResetPasswordPage: React.FC = () => {
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null);
  const [isPasswordReset, setIsPasswordReset] = useState(false);
  
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const { token } = router.query;

  // 验证重置令牌
  useEffect(() => {
    const validateToken = async () => {
      if (!token || typeof token !== 'string') {
        setIsTokenValid(false);
        return;
      }

      try {
        await apiService.post('/auth/validate-reset-token', { token });
        setIsTokenValid(true);
      } catch (error) {
        setIsTokenValid(false);
        toast.error('重置链接无效或已过期');
      }
    };

    if (token) {
      validateToken();
    }
  }, [token]);

  // 如果已经登录，重定向到仪表板
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, authLoading, router]);

  // 如果正在加载认证状态，显示加载界面
  if (authLoading || isTokenValid === null) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // 如果已经认证，不显示重置密码表单
  if (isAuthenticated) {
    return null;
  }

  // 如果令牌无效
  if (isTokenValid === false) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '3rem 1rem'
      }}>
        <div className="card" style={{ maxWidth: '28rem', width: '100%' }}>
          <div className="card-body" style={{ padding: '2rem', textAlign: 'center' }}>
            <div style={{
              width: '3rem',
              height: '3rem',
              margin: '0 auto 1.5rem',
              background: 'var(--color-error-500)',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 style={{
              fontSize: '1.5rem',
              fontWeight: '700',
              color: 'var(--color-gray-900)',
              marginBottom: '1rem'
            }}>
              链接无效
            </h1>
            <p style={{
              fontSize: '0.875rem',
              color: 'var(--color-gray-600)',
              marginBottom: '2rem'
            }}>
              此重置密码链接无效或已过期。请重新申请密码重置。
            </p>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              <Link href="/auth/forgot-password" className="btn btn-primary">
                重新申请重置
              </Link>
              <Link href="/auth/login" className="btn btn-ghost">
                返回登录
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.password) {
      toast.error('请输入新密码');
      return false;
    }

    if (formData.password.length < 6) {
      toast.error('密码至少需要6个字符');
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error('两次输入的密码不一致');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      await apiService.post('/auth/reset-password', {
        token,
        password: formData.password
      });
      setIsPasswordReset(true);
      toast.success('密码重置成功！');
    } catch (error: any) {
      toast.error(error.message || '密码重置失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  if (isPasswordReset) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '3rem 1rem'
      }}>
        <div className="card" style={{ maxWidth: '28rem', width: '100%' }}>
          <div className="card-body" style={{ padding: '2rem', textAlign: 'center' }}>
            <div style={{
              width: '3rem',
              height: '3rem',
              margin: '0 auto 1.5rem',
              background: 'var(--color-success-500)',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <CheckCircleIcon style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />
            </div>
            <h1 style={{
              fontSize: '1.5rem',
              fontWeight: '700',
              color: 'var(--color-gray-900)',
              marginBottom: '1rem'
            }}>
              密码重置成功
            </h1>
            <p style={{
              fontSize: '0.875rem',
              color: 'var(--color-gray-600)',
              marginBottom: '2rem'
            }}>
              您的密码已成功重置。现在可以使用新密码登录您的账户。
            </p>
            <Link href="/auth/login" className="btn btn-primary" style={{ width: '100%' }}>
              立即登录
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '3rem 1rem'
    }}>
      <div className="card" style={{ maxWidth: '28rem', width: '100%' }}>
        <div className="card-body" style={{ padding: '2rem' }}>
          <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
            <div style={{
              width: '3rem',
              height: '3rem',
              margin: '0 auto 1.5rem',
              background: 'linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))',
              borderRadius: '0.75rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: 'var(--shadow-lg)'
            }}>
              <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
              </svg>
            </div>
            <h1 style={{
              fontSize: '1.875rem',
              fontWeight: '700',
              color: 'var(--color-gray-900)',
              marginBottom: '0.5rem'
            }}>
              设置新密码
            </h1>
            <p style={{
              fontSize: '0.875rem',
              color: 'var(--color-gray-600)'
            }}>
              请输入您的新密码
            </p>
          </div>
        
          <form onSubmit={handleSubmit} style={{ marginTop: '1.5rem' }}>
            <div style={{ marginBottom: '1.5rem' }}>
              <div className="form-group">
                <div className="input-group">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    value={formData.password}
                    onChange={handleInputChange}
                    className="input input-floating"
                    placeholder="请输入新密码"
                    style={{ paddingRight: '3rem' }}
                  />
                  <label htmlFor="password" className="input-label">
                    新密码
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    style={{
                      position: 'absolute',
                      right: '0.75rem',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      color: 'var(--color-gray-400)',
                      padding: '0.25rem'
                    }}
                  >
                    {showPassword ? (
                      <EyeSlashIcon style={{ width: '1rem', height: '1rem' }} />
                    ) : (
                      <EyeIcon style={{ width: '1rem', height: '1rem' }} />
                    )}
                  </button>
                </div>
              </div>

              <div className="form-group">
                <div className="input-group">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="input input-floating"
                    placeholder="请再次输入新密码"
                    style={{ paddingRight: '3rem' }}
                  />
                  <label htmlFor="confirmPassword" className="input-label">
                    确认新密码
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    style={{
                      position: 'absolute',
                      right: '0.75rem',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      color: 'var(--color-gray-400)',
                      padding: '0.25rem'
                    }}
                  >
                    {showConfirmPassword ? (
                      <EyeSlashIcon style={{ width: '1rem', height: '1rem' }} />
                    ) : (
                      <EyeIcon style={{ width: '1rem', height: '1rem' }} />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="btn btn-primary"
              style={{ width: '100%', marginBottom: '1rem' }}
            >
              {isLoading ? (
                <>
                  <div className="loading-spinner" style={{ width: '1rem', height: '1rem' }}></div>
                  重置中...
                </>
              ) : (
                '重置密码'
              )}
            </button>

            <div style={{
              textAlign: 'center',
              paddingTop: '1.5rem',
              borderTop: '1px solid var(--color-gray-200)'
            }}>
              <Link
                href="/auth/login"
                style={{
                  fontSize: '0.875rem',
                  color: 'var(--color-primary-600)',
                  textDecoration: 'none',
                  fontWeight: '500'
                }}
              >
                返回登录
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordPage;
