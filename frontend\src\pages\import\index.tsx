import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/router';
import { useDropzone } from 'react-dropzone';
import {
  CloudArrowUpIcon,
  DocumentArrowDownIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  TrashIcon,
  ArrowPathIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import { useImport, useTemplateFields } from '@/hooks/useImport';
import importService from '@/services/importService';
import toast from 'react-hot-toast';

const ImportPage: React.FC = () => {
  const { isAuthenticated, isLoading: authLoading } = useRequireAuth();
  const router = useRouter();
  
  const [uploadProgress, setUploadProgress] = useState(0);
  
  // 使用导入管理Hook
  const {
    tasks,
    statistics,
    isLoading: isLoadingTasks,
    isLoadingStats,
    uploadFile,
    deleteTask,
    retryTask,
    downloadTemplate,
    exportTaskReport,
    isUploading,
    isDeleting,
    isRetrying,
    refresh
  } = useImport({ initialParams: { limit: 10 } });

  // 获取模板字段信息
  const { fields } = useTemplateFields();

  if (authLoading) {
    return <LoadingScreen message="加载中..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // 文件拖拽上传
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;
    
    const file = acceptedFiles[0];
    
    // 验证文件
    const validationErrors = importService.validateFile(file);
    if (validationErrors.length > 0) {
      toast.error(validationErrors[0]);
      return;
    }
    
    try {
      const result = await uploadFile({ 
        file, 
        onProgress: (progress) => {
          setUploadProgress(progress);
        }
      });
      
      if (result) {
        // 上传成功后可以跳转到任务详情页
        router.push(`/import/tasks/${result.id}`);
      }
    } catch (error: any) {
      console.error('文件上传失败:', error);
    } finally {
      setUploadProgress(0);
    }
  }, [uploadFile, router]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv']
    },
    multiple: false
  });

  // 获取任务状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'FAILED':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      case 'PROCESSING':
        return <div className="loading-spinner w-5 h-5"></div>;
      default:
        return <ClockIcon className="w-5 h-5 text-yellow-500" />;
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    return importService.formatFileSize(bytes);
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: '700',
                  color: 'var(--color-gray-900)',
                  marginBottom: '0.5rem'
                }}>
                  数据导入
                </h1>
                <p style={{
                  fontSize: '1rem',
                  color: 'var(--color-gray-600)'
                }}>
                  批量导入产品数据，支持Excel和CSV格式
                </p>
              </div>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button
                  className="btn btn-secondary"
                  onClick={() => downloadTemplate('excel')}
                >
                  <DocumentArrowDownIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                  下载Excel模板
                </button>
                <button
                  className="btn btn-ghost"
                  onClick={() => downloadTemplate('csv')}
                >
                  <DocumentArrowDownIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                  下载CSV模板
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        {statistics && (
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-body">
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                marginBottom: '1rem'
              }}>
                导入统计
              </h2>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '1rem'
              }}>
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-primary-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-primary-200)'
                }}>
                  <div style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    color: 'var(--color-primary-600)'
                  }}>
                    {statistics.totalTasks}
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-primary-700)'
                  }}>
                    总任务数
                  </div>
                </div>
                
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-success-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-success-200)'
                }}>
                  <div style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    color: 'var(--color-success-600)'
                  }}>
                    {statistics.completedTasks}
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-success-700)'
                  }}>
                    已完成
                  </div>
                </div>
                
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-error-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-error-200)'
                }}>
                  <div style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    color: 'var(--color-error-600)'
                  }}>
                    {statistics.failedTasks}
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-error-700)'
                  }}>
                    失败任务
                  </div>
                </div>
                
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-info-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-info-200)'
                }}>
                  <div style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    color: 'var(--color-info-600)'
                  }}>
                    {Math.round(statistics.successRate)}%
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-info-700)'
                  }}>
                    成功率
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 400px',
          gap: '2rem'
        }}>
          {/* 文件上传区域 */}
          <div className="card">
            <div className="card-body">
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                marginBottom: '1rem'
              }}>
                上传数据文件
              </h2>
              
              <div
                {...getRootProps()}
                style={{
                  border: `2px dashed ${isDragActive ? 'var(--color-primary-500)' : 'var(--color-gray-300)'}`,
                  borderRadius: 'var(--radius-lg)',
                  padding: '3rem 2rem',
                  textAlign: 'center',
                  cursor: 'pointer',
                  background: isDragActive ? 'var(--color-primary-50)' : 'var(--color-gray-50)',
                  transition: 'all var(--transition-fast)'
                }}
              >
                <input {...getInputProps()} />
                
                {isUploading ? (
                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '1rem' }}>
                    <div className="loading-spinner" style={{ width: '3rem', height: '3rem' }}></div>
                    <div>
                      <p style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem' }}>
                        正在上传文件...
                      </p>
                      {uploadProgress > 0 && (
                        <div style={{
                          width: '200px',
                          height: '8px',
                          background: 'var(--color-gray-200)',
                          borderRadius: '4px',
                          overflow: 'hidden'
                        }}>
                          <div style={{
                            width: `${uploadProgress}%`,
                            height: '100%',
                            background: 'var(--color-primary-500)',
                            transition: 'width 0.3s ease'
                          }}></div>
                        </div>
                      )}
                      <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                        {uploadProgress}%
                      </p>
                    </div>
                  </div>
                ) : (
                  <div>
                    <CloudArrowUpIcon style={{
                      width: '3rem',
                      height: '3rem',
                      color: 'var(--color-gray-400)',
                      margin: '0 auto 1rem'
                    }} />
                    <p style={{
                      fontSize: '1.125rem',
                      fontWeight: '600',
                      color: 'var(--color-gray-900)',
                      marginBottom: '0.5rem'
                    }}>
                      {isDragActive ? '释放文件以上传' : '拖拽文件到此处或点击选择'}
                    </p>
                    <p style={{
                      fontSize: '0.875rem',
                      color: 'var(--color-gray-600)'
                    }}>
                      支持 Excel (.xlsx, .xls) 和 CSV (.csv) 格式，最大50MB
                    </p>
                  </div>
                )}
              </div>
              
              {/* 模板字段信息 */}
              {fields && (
                <div style={{
                  marginTop: '1.5rem',
                  padding: '1rem',
                  background: 'var(--color-info-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-info-200)'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    marginBottom: '0.75rem'
                  }}>
                    <InformationCircleIcon style={{
                      width: '1.25rem',
                      height: '1.25rem',
                      color: 'var(--color-info-600)'
                    }} />
                    <h3 style={{
                      fontSize: '0.875rem',
                      fontWeight: '600',
                      color: 'var(--color-info-800)'
                    }}>
                      模板字段说明
                    </h3>
                  </div>
                  <div style={{
                    fontSize: '0.75rem',
                    color: 'var(--color-info-700)'
                  }}>
                    <p style={{ marginBottom: '0.5rem' }}>
                      <strong>必填字段：</strong>{fields.required.join('、')}
                    </p>
                    <p>
                      <strong>可选字段：</strong>{fields.optional.join('、')}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 最近任务 */}
          <div className="card">
            <div className="card-header">
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <h2 style={{
                  fontSize: '1.25rem',
                  fontWeight: '600'
                }}>
                  最近任务
                </h2>
                <button
                  className="btn btn-ghost btn-sm"
                  onClick={refresh}
                >
                  刷新
                </button>
              </div>
            </div>
            <div className="card-body">
              {isLoadingTasks ? (
                <div style={{ textAlign: 'center', padding: '2rem' }}>
                  <div className="loading-spinner" style={{ width: '2rem', height: '2rem', margin: '0 auto' }}></div>
                </div>
              ) : tasks.length > 0 ? (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {tasks.map((task) => (
                    <div key={task.id} style={{
                      padding: '1rem',
                      border: '1px solid var(--color-gray-200)',
                      borderRadius: 'var(--radius-lg)',
                      background: 'var(--color-gray-50)'
                    }}>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start',
                        marginBottom: '0.5rem'
                      }}>
                        <div style={{ flex: 1 }}>
                          <h3 style={{
                            fontSize: '0.875rem',
                            fontWeight: '600',
                            marginBottom: '0.25rem'
                          }}>
                            {task.fileName}
                          </h3>
                          <p style={{
                            fontSize: '0.75rem',
                            color: 'var(--color-gray-600)'
                          }}>
                            {formatFileSize(task.fileSize)} • {formatTime(task.createdAt)}
                          </p>
                        </div>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem'
                        }}>
                          {getStatusIcon(task.status)}
                          <span className={`badge badge-${importService.getStatusColor(task.status)}`}>
                            {importService.getStatusText(task.status)}
                          </span>
                        </div>
                      </div>
                      
                      {task.status === 'COMPLETED' && (
                        <div style={{
                          fontSize: '0.75rem',
                          color: 'var(--color-success-600)',
                          marginBottom: '0.5rem'
                        }}>
                          成功: {task.successRows} 行，失败: {task.failedRows} 行
                        </div>
                      )}
                      
                      {task.status === 'FAILED' && task.errorMessage && (
                        <div style={{
                          fontSize: '0.75rem',
                          color: 'var(--color-error-600)',
                          marginBottom: '0.5rem'
                        }}>
                          {task.errorMessage}
                        </div>
                      )}
                      
                      <div style={{
                        display: 'flex',
                        gap: '0.5rem'
                      }}>
                        <button
                          className="btn btn-ghost btn-sm"
                          onClick={() => router.push(`/import/tasks/${task.id}`)}
                        >
                          <EyeIcon style={{ width: '1rem', height: '1rem' }} />
                          查看
                        </button>
                        
                        {task.status === 'FAILED' && (
                          <button
                            className="btn btn-ghost btn-sm"
                            onClick={() => retryTask(task.id)}
                            disabled={isRetrying}
                          >
                            <ArrowPathIcon style={{ width: '1rem', height: '1rem' }} />
                            重试
                          </button>
                        )}
                        
                        <button
                          className="btn btn-ghost btn-sm"
                          onClick={() => deleteTask(task.id)}
                          disabled={isDeleting}
                          style={{ color: 'var(--color-error-600)' }}
                        >
                          <TrashIcon style={{ width: '1rem', height: '1rem' }} />
                          删除
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div style={{
                  textAlign: 'center',
                  padding: '2rem',
                  color: 'var(--color-gray-500)'
                }}>
                  <p>暂无导入任务</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImportPage;
