import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  DocumentArrowDownIcon,
  ArrowPathIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import { useImportTask, useTaskPolling } from '@/hooks/useImport';
import importService from '@/services/importService';
import toast from 'react-hot-toast';

const ImportTaskDetailPage: React.FC = () => {
  const { isAuthenticated, isLoading: authLoading } = useRequireAuth();
  const router = useRouter();
  const { id } = router.query;
  const taskId = typeof id === 'string' ? id : '';

  // 获取任务详情
  const { task, isLoading, refetch } = useImportTask(taskId);
  
  // 任务轮询（用于实时更新处理中的任务）
  const { currentTask, isPolling, startPolling, stopPolling } = useTaskPolling(taskId);

  // 使用轮询的任务数据或API获取的任务数据
  const displayTask = currentTask || task;

  useEffect(() => {
    if (displayTask && (displayTask.status === 'PROCESSING' || displayTask.status === 'PENDING')) {
      startPolling();
    } else {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [displayTask?.status, startPolling, stopPolling]);

  if (authLoading || isLoading) {
    return <LoadingScreen message="加载任务详情..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  if (!displayTask) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div className="card" style={{ maxWidth: '400px', textAlign: 'center' }}>
          <div className="card-body">
            <h1 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '1rem' }}>
              任务不存在
            </h1>
            <p style={{ color: 'var(--color-gray-600)', marginBottom: '2rem' }}>
              找不到指定的导入任务
            </p>
            <Link href="/import" className="btn btn-primary">
              返回导入页面
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // 获取任务状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircleIcon className="w-8 h-8 text-green-500" />;
      case 'FAILED':
        return <XCircleIcon className="w-8 h-8 text-red-500" />;
      case 'PROCESSING':
        return <div className="loading-spinner w-8 h-8"></div>;
      default:
        return <ClockIcon className="w-8 h-8 text-yellow-500" />;
    }
  };

  // 处理重试
  const handleRetry = async () => {
    try {
      await importService.retryImportTask(taskId);
      toast.success('任务重试成功');
      refetch();
    } catch (error: any) {
      toast.error(error.message || '重试失败');
    }
  };

  // 处理删除
  const handleDelete = async () => {
    if (!window.confirm('确定要删除这个导入任务吗？此操作无法撤销。')) {
      return;
    }

    try {
      await importService.deleteImportTask(taskId);
      toast.success('任务删除成功');
      router.push('/import');
    } catch (error: any) {
      toast.error(error.message || '删除失败');
    }
  };

  // 导出报告
  const handleExportReport = async (format: 'excel' | 'csv') => {
    try {
      const blob = await importService.exportTaskReport(taskId, format);
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `导入报告_${taskId}.${format === 'excel' ? 'xlsx' : 'csv'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('报告导出成功');
    } catch (error: any) {
      toast.error(error.message || '导出失败');
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const formatFileSize = (bytes: number) => {
    return importService.formatFileSize(bytes);
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 返回按钮 */}
        <div style={{ marginBottom: '2rem' }}>
          <Link
            href="/import"
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem',
              color: 'var(--color-primary-600)',
              textDecoration: 'none',
              fontSize: '0.875rem',
              fontWeight: '500'
            }}
          >
            <ArrowLeftIcon style={{ width: '1rem', height: '1rem' }} />
            返回导入页面
          </Link>
        </div>

        {/* 任务概览 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: '2rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                {getStatusIcon(displayTask.status)}
                <div>
                  <h1 style={{
                    fontSize: '2rem',
                    fontWeight: '700',
                    color: 'var(--color-gray-900)',
                    marginBottom: '0.5rem'
                  }}>
                    {displayTask.fileName}
                  </h1>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '1rem',
                    fontSize: '0.875rem',
                    color: 'var(--color-gray-600)'
                  }}>
                    <span>文件大小: {formatFileSize(displayTask.fileSize)}</span>
                    <span>创建时间: {formatTime(displayTask.createdAt)}</span>
                    {displayTask.completedAt && (
                      <span>完成时间: {formatTime(displayTask.completedAt)}</span>
                    )}
                  </div>
                </div>
              </div>
              
              <div style={{ display: 'flex', gap: '0.5rem' }}>
                <span className={`badge badge-${importService.getStatusColor(displayTask.status)}`}>
                  {importService.getStatusText(displayTask.status)}
                </span>
                {isPolling && (
                  <span className="badge badge-info">实时更新中</span>
                )}
              </div>
            </div>

            {/* 进度信息 */}
            {displayTask.status === 'PROCESSING' && displayTask.progress && (
              <div style={{ marginBottom: '2rem' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '0.5rem'
                }}>
                  <span style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                    处理进度
                  </span>
                  <span style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    {displayTask.progress}%
                  </span>
                </div>
                <div style={{
                  width: '100%',
                  height: '8px',
                  background: 'var(--color-gray-200)',
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${displayTask.progress}%`,
                    height: '100%',
                    background: 'var(--color-primary-500)',
                    transition: 'width 0.3s ease'
                  }}></div>
                </div>
              </div>
            )}

            {/* 结果统计 */}
            {(displayTask.status === 'COMPLETED' || displayTask.status === 'FAILED') && (
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                gap: '1rem',
                marginBottom: '2rem'
              }}>
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-info-50)',
                  borderRadius: 'var(--radius-lg)',
                  textAlign: 'center'
                }}>
                  <div style={{
                    fontSize: '1.5rem',
                    fontWeight: '700',
                    color: 'var(--color-info-600)'
                  }}>
                    {displayTask.totalRows || 0}
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-info-700)'
                  }}>
                    总行数
                  </div>
                </div>
                
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-success-50)',
                  borderRadius: 'var(--radius-lg)',
                  textAlign: 'center'
                }}>
                  <div style={{
                    fontSize: '1.5rem',
                    fontWeight: '700',
                    color: 'var(--color-success-600)'
                  }}>
                    {displayTask.successRows || 0}
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-success-700)'
                  }}>
                    成功行数
                  </div>
                </div>
                
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-error-50)',
                  borderRadius: 'var(--radius-lg)',
                  textAlign: 'center'
                }}>
                  <div style={{
                    fontSize: '1.5rem',
                    fontWeight: '700',
                    color: 'var(--color-error-600)'
                  }}>
                    {displayTask.failedRows || 0}
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-error-700)'
                  }}>
                    失败行数
                  </div>
                </div>
                
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-warning-50)',
                  borderRadius: 'var(--radius-lg)',
                  textAlign: 'center'
                }}>
                  <div style={{
                    fontSize: '1.5rem',
                    fontWeight: '700',
                    color: 'var(--color-warning-600)'
                  }}>
                    {displayTask.totalRows ? Math.round((displayTask.successRows || 0) / displayTask.totalRows * 100) : 0}%
                  </div>
                  <div style={{
                    fontSize: '0.875rem',
                    color: 'var(--color-warning-700)'
                  }}>
                    成功率
                  </div>
                </div>
              </div>
            )}

            {/* 错误信息 */}
            {displayTask.status === 'FAILED' && displayTask.errorMessage && (
              <div style={{
                padding: '1rem',
                background: 'var(--color-error-50)',
                border: '1px solid var(--color-error-200)',
                borderRadius: 'var(--radius-lg)',
                marginBottom: '2rem'
              }}>
                <h3 style={{
                  fontSize: '1rem',
                  fontWeight: '600',
                  color: 'var(--color-error-800)',
                  marginBottom: '0.5rem'
                }}>
                  错误信息
                </h3>
                <p style={{
                  fontSize: '0.875rem',
                  color: 'var(--color-error-700)',
                  margin: 0
                }}>
                  {displayTask.errorMessage}
                </p>
              </div>
            )}

            {/* 操作按钮 */}
            <div style={{
              display: 'flex',
              gap: '1rem',
              paddingTop: '2rem',
              borderTop: '1px solid var(--color-gray-200)'
            }}>
              {displayTask.status === 'COMPLETED' && (
                <>
                  <button
                    className="btn btn-primary"
                    onClick={() => handleExportReport('excel')}
                  >
                    <DocumentArrowDownIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                    导出Excel报告
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={() => handleExportReport('csv')}
                  >
                    <DocumentArrowDownIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                    导出CSV报告
                  </button>
                </>
              )}
              
              {displayTask.status === 'FAILED' && (
                <button
                  className="btn btn-primary"
                  onClick={handleRetry}
                >
                  <ArrowPathIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                  重试任务
                </button>
              )}
              
              <button
                className="btn btn-ghost"
                onClick={handleDelete}
                style={{ color: 'var(--color-error-600)' }}
              >
                <TrashIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                删除任务
              </button>
            </div>
          </div>
        </div>

        {/* 错误详情 */}
        {displayTask.errors && displayTask.errors.length > 0 && (
          <div className="card">
            <div className="card-header">
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600'
              }}>
                错误详情 ({displayTask.errors.length} 个错误)
              </h2>
            </div>
            <div className="card-body">
              <div style={{
                maxHeight: '400px',
                overflowY: 'auto'
              }}>
                <table style={{
                  width: '100%',
                  borderCollapse: 'collapse'
                }}>
                  <thead>
                    <tr style={{
                      borderBottom: '1px solid var(--color-gray-200)'
                    }}>
                      <th style={{
                        padding: '0.75rem',
                        textAlign: 'left',
                        fontSize: '0.875rem',
                        fontWeight: '600',
                        color: 'var(--color-gray-700)'
                      }}>
                        行号
                      </th>
                      <th style={{
                        padding: '0.75rem',
                        textAlign: 'left',
                        fontSize: '0.875rem',
                        fontWeight: '600',
                        color: 'var(--color-gray-700)'
                      }}>
                        字段
                      </th>
                      <th style={{
                        padding: '0.75rem',
                        textAlign: 'left',
                        fontSize: '0.875rem',
                        fontWeight: '600',
                        color: 'var(--color-gray-700)'
                      }}>
                        错误信息
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {displayTask.errors.map((error, index) => (
                      <tr key={index} style={{
                        borderBottom: '1px solid var(--color-gray-100)'
                      }}>
                        <td style={{
                          padding: '0.75rem',
                          fontSize: '0.875rem'
                        }}>
                          {error.row}
                        </td>
                        <td style={{
                          padding: '0.75rem',
                          fontSize: '0.875rem'
                        }}>
                          {error.field}
                        </td>
                        <td style={{
                          padding: '0.75rem',
                          fontSize: '0.875rem',
                          color: 'var(--color-error-600)'
                        }}>
                          {error.message}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportTaskDetailPage;
