import React, { useState } from 'react';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import { useProducts, useProductCategories, useProductBrands } from '@/hooks/useProducts';
import { CreateProductData } from '@/services/productService';
import toast from 'react-hot-toast';

const ProductsPage: React.FC = () => {
  const { isAuthenticated, isLoading: authLoading } = useRequireAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedPlatform, setSelectedPlatform] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<any>(null);

  const [newProduct, setNewProduct] = useState<CreateProductData>({
    title: '',
    description: '',
    url: '',
    imageUrl: '',
    brand: '',
    model: '',
    platform: 'manual',
    currentPrice: 0,
    originalPrice: 0,
    sales: 0,
    rating: 0,
    reviewCount: 0,
    stock: 0,
    category: '',
    subCategory: '',
    tags: []
  });

  // 使用产品管理Hook
  const {
    products,
    pagination,
    isLoading: productsLoading,
    params,
    searchProducts,
    filterProducts,
    sortProducts,
    goToPage,
    createProduct,
    updateProduct,
    deleteProduct,
    toggleTracking,
    analyzeProduct,
    isCreating,
    isUpdating,
    isDeleting,
    isAnalyzing
  } = useProducts();

  // 获取分类和品牌数据
  const { categories } = useProductCategories();
  const { brands } = useProductBrands();

  if (authLoading) {
    return <LoadingScreen message="加载产品管理..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    searchProducts(value);
  };

  // 处理分类筛选
  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category);
    filterProducts({
      category: category === 'all' ? undefined : category,
      platform: selectedPlatform === 'all' ? undefined : selectedPlatform as any
    });
  };

  // 处理平台筛选
  const handlePlatformFilter = (platform: string) => {
    setSelectedPlatform(platform);
    filterProducts({
      category: selectedCategory === 'all' ? undefined : selectedCategory,
      platform: platform === 'all' ? undefined : platform as any
    });
  };

  // 处理排序
  const handleSort = (sortBy: string) => {
    sortProducts(sortBy as any);
  };

  // 重置表单
  const resetForm = () => {
    setNewProduct({
      title: '',
      description: '',
      url: '',
      imageUrl: '',
      brand: '',
      model: '',
      platform: 'manual',
      currentPrice: 0,
      originalPrice: 0,
      sales: 0,
      rating: 0,
      reviewCount: 0,
      stock: 0,
      category: '',
      subCategory: '',
      tags: []
    });
    setEditingProduct(null);
  };

  // 处理产品创建/更新
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (editingProduct) {
        await updateProduct({ id: editingProduct.id, data: newProduct });
      } else {
        await createProduct(newProduct);
      }
      setShowAddModal(false);
      resetForm();
    } catch (error) {
      // 错误已在Hook中处理
    }
  };

  // 处理产品删除
  const handleDelete = async (id: string) => {
    if (window.confirm('确定要删除这个产品吗？此操作无法撤销。')) {
      try {
        await deleteProduct(id);
      } catch (error) {
        // 错误已在Hook中处理
      }
    }
  };

  // 处理编辑
  const handleEdit = (product: any) => {
    setEditingProduct(product);
    setNewProduct({
      title: product.title || '',
      description: product.description || '',
      url: product.url || '',
      imageUrl: product.imageUrl || '',
      brand: product.brand || '',
      model: product.model || '',
      platform: product.platform || 'manual',
      currentPrice: product.currentPrice || 0,
      originalPrice: product.originalPrice || 0,
      sales: product.sales || 0,
      rating: product.rating || 0,
      reviewCount: product.reviewCount || 0,
      stock: product.stockQuantity || 0,
      category: product.category || '',
      subCategory: product.subcategory || '',
      tags: product.tags ? JSON.parse(product.tags) : []
    });
    setShowAddModal(true);
  };

  // 处理跟踪切换
  const handleToggleTracking = async (id: string, isTracked: boolean) => {
    try {
      await toggleTracking({ id, isTracked: !isTracked });
    } catch (error) {
      // 错误已在Hook中处理
    }
  };

  // 处理产品分析
  const handleAnalyze = async (id: string) => {
    try {
      await analyzeProduct({ id, analysisType: 'basic' });
    } catch (error) {
      // 错误已在Hook中处理
    }
  };

  const platforms = [
    { value: 'all', label: '全部平台' },
    { value: 'taobao', label: '淘宝' },
    { value: 'tmall', label: '天猫' },
    { value: 'jd', label: '京东' },
    { value: 'pdd', label: '拼多多' },
    { value: 'manual', label: '手动添加' }
  ];

  const allCategories = ['all', ...categories];

  if (productsLoading && products.length === 0) {
    return <LoadingScreen message="加载产品数据..." />;
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: '700',
                  color: 'var(--color-gray-900)',
                  marginBottom: '0.5rem'
                }}>
                  产品管理
                </h1>
                <p style={{
                  fontSize: '1rem',
                  color: 'var(--color-gray-600)'
                }}>
                  管理您的产品库存和信息
                </p>
              </div>
              <button
                className="btn btn-primary"
                onClick={() => setShowAddModal(true)}
              >
                <svg style={{ width: '1.25rem', height: '1.25rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                添加产品
              </button>
            </div>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              gap: '1rem',
              alignItems: 'center'
            }}>
              <div style={{ flex: 1, position: 'relative' }}>
                <svg style={{
                  position: 'absolute',
                  left: '0.75rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  width: '1.25rem',
                  height: '1.25rem',
                  color: 'var(--color-gray-400)'
                }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                </svg>
                <input
                  type="text"
                  className="input"
                  placeholder="搜索产品名称或品牌..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  style={{ paddingLeft: '2.5rem' }}
                />
              </div>
              <button className="btn btn-secondary">
                <svg style={{ width: '1.25rem', height: '1.25rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z" />
                </svg>
                筛选
              </button>
            </div>
          </div>
        </div>

        {/* 筛选选项 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
              <div>
                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                  产品分类
                </label>
                <select
                  className="input"
                  value={selectedCategory}
                  onChange={(e) => handleCategoryFilter(e.target.value)}
                >
                  {allCategories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? '全部分类' : category}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                  销售平台
                </label>
                <select
                  className="input"
                  value={selectedPlatform}
                  onChange={(e) => handlePlatformFilter(e.target.value)}
                >
                  {platforms.map(platform => (
                    <option key={platform.value} value={platform.value}>
                      {platform.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                  排序方式
                </label>
                <select
                  className="input"
                  onChange={(e) => handleSort(e.target.value)}
                >
                  <option value="created_at">创建时间</option>
                  <option value="price">价格</option>
                  <option value="sales">销量</option>
                  <option value="rating">评分</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* 产品列表 */}
        <div className="card">
          <div className="card-body">
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
              gap: '1.5rem'
            }}>
              {productsLoading ? (
                <div style={{ gridColumn: '1 / -1', textAlign: 'center', padding: '2rem' }}>
                  <div className="loading-spinner" style={{ width: '2rem', height: '2rem', margin: '0 auto 1rem' }}></div>
                  <p style={{ color: 'var(--color-gray-600)' }}>加载产品数据...</p>
                </div>
              ) : products.length === 0 ? (
                <div style={{ gridColumn: '1 / -1', textAlign: 'center', padding: '2rem' }}>
                  <p style={{ color: 'var(--color-gray-600)', marginBottom: '1rem' }}>暂无产品数据</p>
                  <button
                    className="btn btn-primary"
                    onClick={() => setShowAddModal(true)}
                  >
                    添加第一个产品
                  </button>
                </div>
              ) : (
                products.map((product) => (
                <div key={product.id} className="card">
                  <div className="card-body">
                    <div style={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: '1rem'
                    }}>
                      <div style={{
                        width: '4rem',
                        height: '4rem',
                        background: 'var(--color-gray-200)',
                        borderRadius: 'var(--radius-lg)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '0.75rem',
                        color: 'var(--color-gray-500)'
                      }}>
                        图片
                      </div>
                      
                      <div style={{ flex: 1 }}>
                        <h3 style={{
                          fontSize: '1.125rem',
                          fontWeight: '600',
                          color: 'var(--color-gray-900)',
                          marginBottom: '0.25rem'
                        }}>
                          {product.title}
                        </h3>

                        <p style={{
                          fontSize: '0.875rem',
                          color: 'var(--color-gray-600)',
                          marginBottom: '0.5rem'
                        }}>
                          {product.brand && `${product.brand} · `}{product.category}
                        </p>
                        
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: '1rem'
                        }}>
                          {product.currentPrice && (
                            <span style={{
                              fontSize: '1.25rem',
                              fontWeight: '700',
                              color: 'var(--color-primary-600)'
                            }}>
                              ¥{product.currentPrice.toLocaleString()}
                            </span>
                          )}

                          <span className={`badge ${product.status === 'ACTIVE' ? 'badge-success' : 'badge-error'}`}>
                            {product.status === 'ACTIVE' ? '在售' : '下架'}
                          </span>
                        </div>
                        
                        <div style={{
                          fontSize: '0.875rem',
                          color: 'var(--color-gray-600)',
                          marginBottom: '1rem'
                        }}>
                          {product.stockQuantity !== null && `库存: ${product.stockQuantity} 件`}
                          {product.platform && (
                            <span className={`badge badge-${product.platform === 'taobao' ? 'warning' : product.platform === 'tmall' ? 'error' : product.platform === 'jd' ? 'info' : product.platform === 'pdd' ? 'success' : 'secondary'}`} style={{ marginLeft: '0.5rem' }}>
                              {product.platform === 'taobao' ? '淘宝' :
                               product.platform === 'tmall' ? '天猫' :
                               product.platform === 'jd' ? '京东' :
                               product.platform === 'pdd' ? '拼多多' : '手动'}
                            </span>
                          )}
                        </div>
                        
                        <div style={{
                          display: 'flex',
                          gap: '0.5rem'
                        }}>
                          <button
                            className="btn btn-ghost btn-sm"
                            onClick={() => window.open(product.url, '_blank')}
                            disabled={!product.url}
                          >
                            <svg style={{ width: '1rem', height: '1rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            查看
                          </button>
                          <button
                            className="btn btn-ghost btn-sm"
                            onClick={() => handleEdit(product)}
                            disabled={isUpdating}
                          >
                            <svg style={{ width: '1rem', height: '1rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10" />
                            </svg>
                            编辑
                          </button>
                          <button
                            className="btn btn-ghost btn-sm"
                            onClick={() => handleAnalyze(product.id)}
                            disabled={isAnalyzing}
                          >
                            <svg style={{ width: '1rem', height: '1rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            分析
                          </button>
                          <button
                            className="btn btn-ghost btn-sm"
                            onClick={() => handleDelete(product.id)}
                            disabled={isDeleting}
                            style={{ color: 'var(--color-error-600)' }}
                          >
                            <svg style={{ width: '1rem', height: '1rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                            </svg>
                            删除
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 分页 */}
            {pagination && pagination.pages > 1 && (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '0.5rem',
                marginTop: '2rem',
                paddingTop: '2rem',
                borderTop: '1px solid var(--color-gray-200)'
              }}>
                <button
                  className="btn btn-ghost btn-sm"
                  onClick={() => goToPage(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                >
                  上一页
                </button>

                <div style={{ display: 'flex', gap: '0.25rem' }}>
                  {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <button
                        key={page}
                        className={`btn btn-sm ${pagination.page === page ? 'btn-primary' : 'btn-ghost'}`}
                        onClick={() => goToPage(page)}
                      >
                        {page}
                      </button>
                    );
                  })}
                </div>

                <button
                  className="btn btn-ghost btn-sm"
                  onClick={() => goToPage(pagination.page + 1)}
                  disabled={pagination.page >= pagination.pages}
                >
                  下一页
                </button>

                <span style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)', marginLeft: '1rem' }}>
                  共 {pagination.total} 个产品，第 {pagination.page} / {pagination.pages} 页
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 添加产品模态框 */}
      {showAddModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div className="card" style={{ width: '100%', maxWidth: '500px', margin: '1rem' }}>
            <div className="card-header">
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <h2 style={{ fontSize: '1.25rem', fontWeight: '600', margin: 0 }}>
                  {editingProduct ? '编辑产品' : '添加新产品'}
                </h2>
                <button
                  className="btn btn-ghost btn-sm"
                  onClick={() => {
                    setShowAddModal(false);
                    resetForm();
                  }}
                >
                  <svg style={{ width: '1.25rem', height: '1.25rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="card-body">
              <form onSubmit={handleSubmit}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                      产品标题 *
                    </label>
                    <input
                      type="text"
                      className="input"
                      value={newProduct.title}
                      onChange={(e) => setNewProduct({ ...newProduct, title: e.target.value })}
                      placeholder="请输入产品标题"
                      required
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                      产品URL *
                    </label>
                    <input
                      type="url"
                      className="input"
                      value={newProduct.url}
                      onChange={(e) => setNewProduct({ ...newProduct, url: e.target.value })}
                      placeholder="请输入产品链接"
                      required
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                      品牌
                    </label>
                    <input
                      type="text"
                      className="input"
                      value={newProduct.brand}
                      onChange={(e) => setNewProduct({ ...newProduct, brand: e.target.value })}
                      placeholder="请输入品牌名称"
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                      平台 *
                    </label>
                    <select
                      className="input"
                      value={newProduct.platform}
                      onChange={(e) => setNewProduct({ ...newProduct, platform: e.target.value as any })}
                      required
                    >
                      <option value="manual">手动添加</option>
                      <option value="taobao">淘宝</option>
                      <option value="tmall">天猫</option>
                      <option value="jd">京东</option>
                      <option value="pdd">拼多多</option>
                    </select>
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                      分类 *
                    </label>
                    <select
                      className="input"
                      value={newProduct.category}
                      onChange={(e) => setNewProduct({ ...newProduct, category: e.target.value })}
                      required
                    >
                      <option value="">请选择分类</option>
                      {allCategories.filter(c => c !== 'all').map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                      子分类
                    </label>
                    <input
                      type="text"
                      className="input"
                      value={newProduct.subCategory || ''}
                      onChange={(e) => setNewProduct({ ...newProduct, subCategory: e.target.value })}
                      placeholder="请输入子分类"
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                      当前价格 *
                    </label>
                    <input
                      type="number"
                      className="input"
                      value={newProduct.currentPrice || ''}
                      onChange={(e) => setNewProduct({ ...newProduct, currentPrice: parseFloat(e.target.value) })}
                      placeholder="请输入当前价格"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                      原价
                    </label>
                    <input
                      type="number"
                      className="input"
                      value={newProduct.originalPrice || ''}
                      onChange={(e) => setNewProduct({ ...newProduct, originalPrice: parseFloat(e.target.value) })}
                      placeholder="请输入原价"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                      库存数量
                    </label>
                    <input
                      type="number"
                      className="input"
                      value={newProduct.stock || ''}
                      onChange={(e) => setNewProduct({ ...newProduct, stock: parseInt(e.target.value) })}
                      placeholder="请输入库存数量"
                      min="0"
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                      图片URL
                    </label>
                    <input
                      type="url"
                      className="input"
                      value={newProduct.imageUrl || ''}
                      onChange={(e) => setNewProduct({ ...newProduct, imageUrl: e.target.value })}
                      placeholder="请输入图片链接"
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
                      产品描述
                    </label>
                    <textarea
                      className="input"
                      value={newProduct.description || ''}
                      onChange={(e) => setNewProduct({ ...newProduct, description: e.target.value })}
                      placeholder="请输入产品描述"
                      rows={3}
                    />
                  </div>
                </div>

                <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>
                  <button
                    type="button"
                    className="btn btn-ghost"
                    onClick={() => {
                      setShowAddModal(false);
                      resetForm();
                    }}
                    style={{ flex: 1 }}
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={isCreating || isUpdating}
                    style={{ flex: 1 }}
                  >
                    {isCreating || isUpdating ? (
                      <>
                        <div className="loading-spinner" style={{ width: '1rem', height: '1rem' }}></div>
                        {editingProduct ? '更新中...' : '创建中...'}
                      </>
                    ) : (
                      editingProduct ? '更新产品' : '添加产品'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductsPage;
