import React, { useState } from 'react';
import { useRequireAuth, useAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import { UserIcon, EnvelopeIcon, PhoneIcon, KeyIcon, BellIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const ProfilePage: React.FC = () => {
  const { isAuthenticated, isLoading } = useRequireAuth();
  const { user, updateProfile, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [isUpdating, setIsUpdating] = useState(false);

  const [profileData, setProfileData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    phone: user?.phone || '',
    bio: user?.bio || '',
    timezone: user?.timezone || 'Asia/Shanghai',
    language: user?.language || 'zh-CN'
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [notificationSettings, setNotificationSettings] = useState({
    email: user?.preferences?.notifications?.email ?? true,
    push: user?.preferences?.notifications?.push ?? true,
    analysis_complete: user?.preferences?.notifications?.analysis_complete ?? true,
    price_alert: user?.preferences?.notifications?.price_alert ?? true
  });

  if (isLoading) {
    return <LoadingScreen message="加载用户资料..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  const tabs = [
    { id: 'profile', name: '基本信息', icon: UserIcon },
    { id: 'security', name: '安全设置', icon: KeyIcon },
    { id: 'notifications', name: '通知设置', icon: BellIcon }
  ];

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdating(true);

    try {
      await updateProfile(profileData);
      toast.success('资料更新成功');
    } catch (error) {
      toast.error('资料更新失败');
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('新密码和确认密码不一致');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      toast.error('新密码至少需要6个字符');
      return;
    }

    setIsUpdating(true);

    try {
      // 这里应该调用修改密码的API
      toast.success('密码修改成功');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error) {
      toast.error('密码修改失败');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleNotificationUpdate = async () => {
    setIsUpdating(true);

    try {
      await updateProfile({
        preferences: {
          ...user?.preferences,
          notifications: notificationSettings
        }
      });
      toast.success('通知设置已更新');
    } catch (error) {
      toast.error('通知设置更新失败');
    } finally {
      setIsUpdating(false);
    }
  };

  const renderProfileTab = () => (
    <form onSubmit={handleProfileUpdate}>
      <div style={{ display: 'grid', gap: '1.5rem' }}>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem' }}>
          <div className="form-group">
            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
              用户名
            </label>
            <div className="input-group">
              <UserIcon style={{ 
                position: 'absolute', 
                left: '0.75rem', 
                top: '50%', 
                transform: 'translateY(-50%)', 
                width: '1.25rem', 
                height: '1.25rem', 
                color: 'var(--color-gray-400)' 
              }} />
              <input
                type="text"
                className="input"
                value={profileData.username}
                onChange={(e) => setProfileData({ ...profileData, username: e.target.value })}
                style={{ paddingLeft: '2.5rem' }}
                placeholder="请输入用户名"
              />
            </div>
          </div>

          <div className="form-group">
            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
              邮箱地址
            </label>
            <div className="input-group">
              <EnvelopeIcon style={{ 
                position: 'absolute', 
                left: '0.75rem', 
                top: '50%', 
                transform: 'translateY(-50%)', 
                width: '1.25rem', 
                height: '1.25rem', 
                color: 'var(--color-gray-400)' 
              }} />
              <input
                type="email"
                className="input"
                value={profileData.email}
                onChange={(e) => setProfileData({ ...profileData, email: e.target.value })}
                style={{ paddingLeft: '2.5rem' }}
                placeholder="请输入邮箱地址"
              />
            </div>
          </div>

          <div className="form-group">
            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
              手机号码
            </label>
            <div className="input-group">
              <PhoneIcon style={{ 
                position: 'absolute', 
                left: '0.75rem', 
                top: '50%', 
                transform: 'translateY(-50%)', 
                width: '1.25rem', 
                height: '1.25rem', 
                color: 'var(--color-gray-400)' 
              }} />
              <input
                type="tel"
                className="input"
                value={profileData.phone}
                onChange={(e) => setProfileData({ ...profileData, phone: e.target.value })}
                style={{ paddingLeft: '2.5rem' }}
                placeholder="请输入手机号码"
              />
            </div>
          </div>

          <div className="form-group">
            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
              时区
            </label>
            <select
              className="input"
              value={profileData.timezone}
              onChange={(e) => setProfileData({ ...profileData, timezone: e.target.value })}
            >
              <option value="Asia/Shanghai">中国标准时间 (UTC+8)</option>
              <option value="America/New_York">美国东部时间 (UTC-5)</option>
              <option value="Europe/London">英国时间 (UTC+0)</option>
              <option value="Asia/Tokyo">日本时间 (UTC+9)</option>
            </select>
          </div>
        </div>

        <div className="form-group">
          <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
            个人简介
          </label>
          <textarea
            className="input"
            rows={4}
            value={profileData.bio}
            onChange={(e) => setProfileData({ ...profileData, bio: e.target.value })}
            placeholder="介绍一下自己..."
            style={{ resize: 'vertical' }}
          />
        </div>

        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
          <button
            type="button"
            className="btn btn-ghost"
            onClick={() => setProfileData({
              username: user?.username || '',
              email: user?.email || '',
              phone: user?.phone || '',
              bio: user?.bio || '',
              timezone: user?.timezone || 'Asia/Shanghai',
              language: user?.language || 'zh-CN'
            })}
          >
            重置
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isUpdating}
          >
            {isUpdating ? (
              <>
                <div className="loading-spinner" style={{ width: '1rem', height: '1rem' }}></div>
                更新中...
              </>
            ) : (
              '保存更改'
            )}
          </button>
        </div>
      </div>
    </form>
  );

  const renderSecurityTab = () => (
    <div style={{ display: 'grid', gap: '2rem' }}>
      <form onSubmit={handlePasswordChange}>
        <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '1rem' }}>
          修改密码
        </h3>
        <div style={{ display: 'grid', gap: '1rem', maxWidth: '400px' }}>
          <div className="form-group">
            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
              当前密码
            </label>
            <input
              type="password"
              className="input"
              value={passwordData.currentPassword}
              onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
              placeholder="请输入当前密码"
            />
          </div>

          <div className="form-group">
            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
              新密码
            </label>
            <input
              type="password"
              className="input"
              value={passwordData.newPassword}
              onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
              placeholder="请输入新密码"
            />
          </div>

          <div className="form-group">
            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.5rem' }}>
              确认新密码
            </label>
            <input
              type="password"
              className="input"
              value={passwordData.confirmPassword}
              onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
              placeholder="请再次输入新密码"
            />
          </div>

          <button
            type="submit"
            className="btn btn-primary"
            disabled={isUpdating}
            style={{ justifySelf: 'start' }}
          >
            {isUpdating ? (
              <>
                <div className="loading-spinner" style={{ width: '1rem', height: '1rem' }}></div>
                修改中...
              </>
            ) : (
              '修改密码'
            )}
          </button>
        </div>
      </form>

      <div style={{ borderTop: '1px solid var(--color-gray-200)', paddingTop: '2rem' }}>
        <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '1rem', color: 'var(--color-error-600)' }}>
          危险操作
        </h3>
        <div style={{ 
          background: 'var(--color-error-50)', 
          border: '1px solid var(--color-error-200)', 
          borderRadius: 'var(--radius-lg)', 
          padding: '1rem' 
        }}>
          <p style={{ fontSize: '0.875rem', color: 'var(--color-error-700)', marginBottom: '1rem' }}>
            注销账户将永久删除您的所有数据，此操作无法撤销。
          </p>
          <button
            className="btn"
            style={{ 
              background: 'var(--color-error-600)', 
              color: 'white',
              border: 'none'
            }}
            onClick={() => {
              if (confirm('确定要注销账户吗？此操作无法撤销！')) {
                // 这里应该调用注销账户的API
                toast.error('账户注销功能暂未开放');
              }
            }}
          >
            注销账户
          </button>
        </div>
      </div>
    </div>
  );

  const renderNotificationsTab = () => (
    <div style={{ display: 'grid', gap: '1.5rem' }}>
      <h3 style={{ fontSize: '1.125rem', fontWeight: '600' }}>
        通知偏好设置
      </h3>

      <div style={{ display: 'grid', gap: '1rem' }}>
        {[
          { key: 'email', label: '邮件通知', description: '接收重要更新和通知邮件' },
          { key: 'push', label: '推送通知', description: '接收浏览器推送通知' },
          { key: 'analysis_complete', label: '分析完成通知', description: '当产品分析完成时通知我' },
          { key: 'price_alert', label: '价格提醒', description: '当产品价格发生变化时通知我' }
        ].map((setting) => (
          <div key={setting.key} style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '1rem',
            border: '1px solid var(--color-gray-200)',
            borderRadius: 'var(--radius-lg)'
          }}>
            <div>
              <div style={{ fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.25rem' }}>
                {setting.label}
              </div>
              <div style={{ fontSize: '0.75rem', color: 'var(--color-gray-600)' }}>
                {setting.description}
              </div>
            </div>
            <label style={{ position: 'relative', display: 'inline-block', width: '3rem', height: '1.5rem' }}>
              <input
                type="checkbox"
                checked={notificationSettings[setting.key as keyof typeof notificationSettings]}
                onChange={(e) => setNotificationSettings({
                  ...notificationSettings,
                  [setting.key]: e.target.checked
                })}
                style={{ opacity: 0, width: 0, height: 0 }}
              />
              <span style={{
                position: 'absolute',
                cursor: 'pointer',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: notificationSettings[setting.key as keyof typeof notificationSettings] 
                  ? 'var(--color-primary-500)' 
                  : 'var(--color-gray-300)',
                borderRadius: '0.75rem',
                transition: 'all var(--transition-fast)'
              }}>
                <span style={{
                  position: 'absolute',
                  content: '""',
                  height: '1.125rem',
                  width: '1.125rem',
                  left: notificationSettings[setting.key as keyof typeof notificationSettings] ? '1.625rem' : '0.1875rem',
                  bottom: '0.1875rem',
                  backgroundColor: 'white',
                  borderRadius: '50%',
                  transition: 'all var(--transition-fast)',
                  boxShadow: 'var(--shadow-sm)'
                }}></span>
              </span>
            </label>
          </div>
        ))}
      </div>

      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <button
          className="btn btn-primary"
          onClick={handleNotificationUpdate}
          disabled={isUpdating}
        >
          {isUpdating ? (
            <>
              <div className="loading-spinner" style={{ width: '1rem', height: '1rem' }}></div>
              保存中...
            </>
          ) : (
            '保存设置'
          )}
        </button>
      </div>
    </div>
  );

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div style={{
                width: '4rem',
                height: '4rem',
                background: 'var(--color-gray-300)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <UserIcon style={{ width: '2rem', height: '2rem', color: 'var(--color-gray-600)' }} />
              </div>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: '700',
                  color: 'var(--color-gray-900)',
                  marginBottom: '0.25rem'
                }}>
                  {user?.username}
                </h1>
                <p style={{
                  fontSize: '1rem',
                  color: 'var(--color-gray-600)'
                }}>
                  {user?.email}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body" style={{ padding: '0' }}>
            <div style={{ display: 'flex', borderBottom: '1px solid var(--color-gray-200)' }}>
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    className={`btn ${activeTab === tab.id ? 'btn-primary' : 'btn-ghost'}`}
                    onClick={() => setActiveTab(tab.id)}
                    style={{
                      borderRadius: 0,
                      borderBottom: activeTab === tab.id ? '2px solid var(--color-primary-500)' : '2px solid transparent',
                      padding: '1rem 1.5rem'
                    }}
                  >
                    <Icon style={{ width: '1.25rem', height: '1.25rem', marginRight: '0.5rem' }} />
                    {tab.name}
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* 标签页内容 */}
        <div className="card">
          <div className="card-body">
            {activeTab === 'profile' && renderProfileTab()}
            {activeTab === 'security' && renderSecurityTab()}
            {activeTab === 'notifications' && renderNotificationsTab()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
