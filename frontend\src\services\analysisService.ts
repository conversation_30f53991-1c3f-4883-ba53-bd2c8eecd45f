import apiService from './api';
import { AnalysisTask, ApiResponse } from '@/types';

export interface AnalysisListParams {
  page?: number;
  limit?: number;
  status?: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  type?: 'product' | 'market' | 'competitor' | 'trend';
}

export interface AnalysisListResponse {
  tasks: AnalysisTask[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface StartAnalysisData {
  type: 'product' | 'market' | 'competitor' | 'trend';
  config: {
    productData?: any;
    analysisDepth?: 'basic' | 'detailed' | 'competitive';
    includeMarketAnalysis?: boolean;
    includeCompetitorAnalysis?: boolean;
    includeTrendAnalysis?: boolean;
    category?: string;
    keywords?: string[];
    timeRange?: string;
    competitorUrl?: string;
  };
  priority?: 'low' | 'normal' | 'high';
}

export interface AnalysisResult {
  id: string;
  title: string;
  type: string;
  status: string;
  result: any;
  createdAt: string;
  completedAt?: string;
  duration?: number;
}

export interface AnalysisStatistics {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  runningTasks: number;
  averageDuration: number;
  successRate: number;
  recentTasks: AnalysisTask[];
}

export interface AnalysisTemplate {
  id: string;
  name: string;
  type: string;
  description: string;
  config: any;
}

class AnalysisService {
  private baseUrl = '/analysis';

  /**
   * 开始分析任务
   */
  async startAnalysis(data: StartAnalysisData): Promise<{ task: AnalysisTask }> {
    try {
      const response = await apiService.post<{ data: { task: AnalysisTask } }>(
        `${this.baseUrl}/start`,
        data
      );
      return response.data;
    } catch (error) {
      console.error('开始分析失败:', error);
      throw error;
    }
  }

  /**
   * 获取分析任务列表
   */
  async getAnalysisList(params: AnalysisListParams = {}): Promise<AnalysisListResponse> {
    try {
      const response = await apiService.get<{ data: AnalysisListResponse }>(
        this.baseUrl,
        params
      );
      return response.data;
    } catch (error) {
      console.error('获取分析列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取分析任务详情
   */
  async getAnalysisById(id: string): Promise<AnalysisTask> {
    try {
      const response = await apiService.get<{ data: AnalysisTask }>(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error('获取分析详情失败:', error);
      throw error;
    }
  }

  /**
   * 获取分析任务状态
   */
  async getAnalysisStatus(id: string): Promise<{
    id: string;
    status: string;
    progress: number;
    startedAt?: string;
    completedAt?: string;
    errorMessage?: string;
  }> {
    try {
      const response = await apiService.get<{ 
        data: {
          id: string;
          status: string;
          progress: number;
          started_at?: string;
          completed_at?: string;
          error_message?: string;
        }
      }>(`${this.baseUrl}/${id}/status`);
      
      return {
        id: response.data.id,
        status: response.data.status,
        progress: response.data.progress,
        startedAt: response.data.started_at,
        completedAt: response.data.completed_at,
        errorMessage: response.data.error_message
      };
    } catch (error) {
      console.error('获取分析状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取分析结果
   */
  async getAnalysisResult(id: string): Promise<AnalysisResult> {
    try {
      const response = await apiService.get<{ data: AnalysisResult }>(`${this.baseUrl}/${id}/result`);
      return response.data;
    } catch (error) {
      console.error('获取分析结果失败:', error);
      throw error;
    }
  }

  /**
   * 取消分析任务
   */
  async cancelAnalysis(id: string): Promise<void> {
    try {
      await apiService.post(`${this.baseUrl}/${id}/cancel`);
    } catch (error) {
      console.error('取消分析失败:', error);
      throw error;
    }
  }

  /**
   * 删除分析任务
   */
  async deleteAnalysis(id: string): Promise<void> {
    try {
      await apiService.delete(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error('删除分析失败:', error);
      throw error;
    }
  }

  /**
   * 重试分析任务
   */
  async retryAnalysis(id: string): Promise<AnalysisTask> {
    try {
      const response = await apiService.post<{ data: AnalysisTask }>(`${this.baseUrl}/${id}/retry`);
      return response.data;
    } catch (error) {
      console.error('重试分析失败:', error);
      throw error;
    }
  }

  /**
   * 分享分析结果
   */
  async shareAnalysis(id: string, options: {
    expiresIn?: string;
    password?: string;
    allowDownload?: boolean;
  } = {}): Promise<{ shareUrl: string; token: string }> {
    try {
      const response = await apiService.post<{ 
        data: { shareUrl: string; token: string } 
      }>(`${this.baseUrl}/${id}/share`, options);
      return response.data;
    } catch (error) {
      console.error('分享分析失败:', error);
      throw error;
    }
  }

  /**
   * 获取分享的分析结果
   */
  async getSharedAnalysis(token: string): Promise<AnalysisResult> {
    try {
      const response = await apiService.get<{ data: AnalysisResult }>(`${this.baseUrl}/shared/${token}`);
      return response.data;
    } catch (error) {
      console.error('获取分享分析失败:', error);
      throw error;
    }
  }

  /**
   * 导出分析结果
   */
  async exportAnalysis(id: string, format: 'pdf' | 'excel' | 'word' = 'pdf'): Promise<Blob> {
    try {
      const response = await apiService.get(`${this.baseUrl}/${id}/export`, { format });

      const mimeTypes = {
        pdf: 'application/pdf',
        excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        word: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      };

      return new Blob([response], { type: mimeTypes[format] });
    } catch (error) {
      console.error('导出分析失败:', error);
      throw error;
    }
  }

  /**
   * 获取分析统计信息
   */
  async getAnalysisStatistics(period: '7d' | '30d' | '90d' | '1y' = '30d'): Promise<AnalysisStatistics> {
    try {
      const response = await apiService.get<{ data: AnalysisStatistics }>(
        `${this.baseUrl}/statistics`,
        { period }
      );
      return response.data;
    } catch (error) {
      console.error('获取分析统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取分析模板
   */
  async getAnalysisTemplates(): Promise<AnalysisTemplate[]> {
    try {
      const response = await apiService.get<{ data: AnalysisTemplate[] }>(`${this.baseUrl}/templates`);
      return response.data;
    } catch (error) {
      console.error('获取分析模板失败:', error);
      throw error;
    }
  }

  /**
   * 轮询任务状态
   */
  async pollTaskStatus(
    taskId: string,
    onUpdate: (status: any) => void,
    interval: number = 2000,
    maxAttempts: number = 150 // 5分钟
  ): Promise<any> {
    let attempts = 0;
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++;
          const status = await this.getAnalysisStatus(taskId);
          onUpdate(status);
          
          // 如果任务完成或失败，停止轮询
          if (status.status === 'completed' || status.status === 'failed' || status.status === 'cancelled') {
            resolve(status);
            return;
          }
          
          // 如果超过最大尝试次数，停止轮询
          if (attempts >= maxAttempts) {
            reject(new Error('轮询超时'));
            return;
          }
          
          // 继续轮询
          setTimeout(poll, interval);
        } catch (error) {
          reject(error);
        }
      };
      
      poll();
    });
  }

  /**
   * 获取任务状态显示文本
   */
  getStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      'pending': '等待中',
      'running': '分析中',
      'completed': '已完成',
      'failed': '失败',
      'cancelled': '已取消'
    };
    
    return statusMap[status] || status;
  }

  /**
   * 获取任务状态颜色
   */
  getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      'pending': 'warning',
      'running': 'info',
      'completed': 'success',
      'failed': 'error',
      'cancelled': 'secondary'
    };
    
    return colorMap[status] || 'secondary';
  }

  /**
   * 获取分析类型显示文本
   */
  getTypeText(type: string): string {
    const typeMap: Record<string, string> = {
      'product': '产品分析',
      'market': '市场分析',
      'competitor': '竞争对手分析',
      'trend': '趋势分析'
    };
    
    return typeMap[type] || type;
  }

  /**
   * 格式化持续时间
   */
  formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${Math.round(ms / 1000)}s`;
    if (ms < 3600000) return `${Math.round(ms / 60000)}m`;
    return `${Math.round(ms / 3600000)}h`;
  }
}

// 创建分析服务实例
const analysisService = new AnalysisService();

export default analysisService;
