import apiService from './api';
import { ImportTask, ApiResponse } from '@/types';

export interface ImportTaskListParams {
  page?: number;
  limit?: number;
  status?: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
}

export interface ImportTaskListResponse {
  tasks: ImportTask[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ImportStatistics {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  processingTasks: number;
  totalProductsImported: number;
  successRate: number;
  recentTasks: ImportTask[];
}

export interface TemplateFields {
  required: string[];
  optional: string[];
  examples: Record<string, string>;
}

export interface ImportResult {
  success: boolean;
  totalRows: number;
  successRows: number;
  failedRows: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

class ImportService {
  private baseUrl = '/import';

  /**
   * 下载Excel模板
   */
  async downloadExcelTemplate(): Promise<Blob> {
    try {
      const response = await apiService.request({
        method: 'GET',
        url: `${this.baseUrl}/template/excel`,
        responseType: 'blob'
      });
      return new Blob([response], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
    } catch (error) {
      console.error('下载Excel模板失败:', error);
      throw error;
    }
  }

  /**
   * 下载CSV模板
   */
  async downloadCSVTemplate(): Promise<Blob> {
    try {
      const response = await apiService.request({
        method: 'GET',
        url: `${this.baseUrl}/template/csv`,
        responseType: 'blob'
      });
      return new Blob([response], { type: 'text/csv' });
    } catch (error) {
      console.error('下载CSV模板失败:', error);
      throw error;
    }
  }

  /**
   * 获取模板字段信息
   */
  async getTemplateFields(): Promise<TemplateFields> {
    try {
      const response = await apiService.get<{ data: TemplateFields }>(`${this.baseUrl}/template/fields`);
      return response.data;
    } catch (error) {
      console.error('获取模板字段失败:', error);
      throw error;
    }
  }

  /**
   * 上传并导入文件
   */
  async uploadAndImport(
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<{ task: ImportTask }> {
    try {
      const response = await apiService.upload<{ data: { task: ImportTask } }>(
        `${this.baseUrl}/upload`,
        file,
        onProgress
      );
      return response.data;
    } catch (error) {
      console.error('文件上传失败:', error);
      throw error;
    }
  }

  /**
   * 获取导入任务列表
   */
  async getImportTasks(params: ImportTaskListParams = {}): Promise<ImportTaskListResponse> {
    try {
      const response = await apiService.get<{ data: ImportTaskListResponse }>(
        `${this.baseUrl}/tasks`,
        params
      );
      return response.data;
    } catch (error) {
      console.error('获取导入任务列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取导入任务状态
   */
  async getImportTaskStatus(id: string): Promise<ImportTask> {
    try {
      const response = await apiService.get<{ data: ImportTask }>(`${this.baseUrl}/tasks/${id}`);
      return response.data;
    } catch (error) {
      console.error('获取导入任务状态失败:', error);
      throw error;
    }
  }

  /**
   * 删除导入任务
   */
  async deleteImportTask(id: string): Promise<void> {
    try {
      await apiService.delete(`${this.baseUrl}/tasks/${id}`);
    } catch (error) {
      console.error('删除导入任务失败:', error);
      throw error;
    }
  }

  /**
   * 重试导入任务
   */
  async retryImportTask(id: string): Promise<ImportTask> {
    try {
      const response = await apiService.post<{ data: ImportTask }>(`${this.baseUrl}/tasks/${id}/retry`);
      return response.data;
    } catch (error) {
      console.error('重试导入任务失败:', error);
      throw error;
    }
  }

  /**
   * 获取导入统计信息
   */
  async getImportStatistics(): Promise<ImportStatistics> {
    try {
      const response = await apiService.get<{ data: ImportStatistics }>(`${this.baseUrl}/statistics`);
      return response.data;
    } catch (error) {
      console.error('获取导入统计失败:', error);
      throw error;
    }
  }

  /**
   * 轮询任务状态
   */
  async pollTaskStatus(
    taskId: string,
    onUpdate: (task: ImportTask) => void,
    interval: number = 2000,
    maxAttempts: number = 150 // 5分钟
  ): Promise<ImportTask> {
    let attempts = 0;
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++;
          const task = await this.getImportTaskStatus(taskId);
          onUpdate(task);
          
          // 如果任务完成或失败，停止轮询
          if (task.status === 'COMPLETED' || task.status === 'FAILED' || task.status === 'CANCELLED') {
            resolve(task);
            return;
          }
          
          // 如果超过最大尝试次数，停止轮询
          if (attempts >= maxAttempts) {
            reject(new Error('轮询超时'));
            return;
          }
          
          // 继续轮询
          setTimeout(poll, interval);
        } catch (error) {
          reject(error);
        }
      };
      
      poll();
    });
  }

  /**
   * 批量删除导入任务
   */
  async batchDeleteTasks(taskIds: string[]): Promise<void> {
    try {
      await Promise.all(taskIds.map(id => this.deleteImportTask(id)));
    } catch (error) {
      console.error('批量删除导入任务失败:', error);
      throw error;
    }
  }

  /**
   * 获取任务详细结果
   */
  async getTaskResult(taskId: string): Promise<ImportResult> {
    try {
      const task = await this.getImportTaskStatus(taskId);
      
      if (task.status !== 'COMPLETED' && task.status !== 'FAILED') {
        throw new Error('任务尚未完成');
      }
      
      return {
        success: task.status === 'COMPLETED',
        totalRows: task.totalRows || 0,
        successRows: task.successRows || 0,
        failedRows: task.failedRows || 0,
        errors: task.errors || []
      };
    } catch (error) {
      console.error('获取任务结果失败:', error);
      throw error;
    }
  }

  /**
   * 导出任务报告
   */
  async exportTaskReport(taskId: string, format: 'excel' | 'csv' = 'excel'): Promise<Blob> {
    try {
      const response = await apiService.request({
        method: 'GET',
        url: `${this.baseUrl}/tasks/${taskId}/export`,
        params: { format },
        responseType: 'blob'
      });
      
      return new Blob([response], { 
        type: format === 'csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
    } catch (error) {
      console.error('导出任务报告失败:', error);
      throw error;
    }
  }

  /**
   * 验证文件格式
   */
  validateFile(file: File): string[] {
    const errors: string[] = [];
    
    // 检查文件大小 (50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      errors.push('文件大小不能超过50MB');
    }
    
    // 检查文件类型
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
      'application/vnd.ms-excel', // xls
      'text/csv' // csv
    ];
    
    if (!allowedTypes.includes(file.type)) {
      errors.push('只支持Excel (.xlsx, .xls) 和CSV (.csv) 文件');
    }
    
    // 检查文件名
    if (!file.name || file.name.trim() === '') {
      errors.push('文件名不能为空');
    }
    
    return errors;
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取任务状态显示文本
   */
  getStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      'PENDING': '等待中',
      'PROCESSING': '处理中',
      'COMPLETED': '已完成',
      'FAILED': '失败',
      'CANCELLED': '已取消'
    };
    
    return statusMap[status] || status;
  }

  /**
   * 获取任务状态颜色
   */
  getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      'PENDING': 'warning',
      'PROCESSING': 'info',
      'COMPLETED': 'success',
      'FAILED': 'error',
      'CANCELLED': 'secondary'
    };
    
    return colorMap[status] || 'secondary';
  }
}

// 创建导入服务实例
const importService = new ImportService();

export default importService;
