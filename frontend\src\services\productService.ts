import apiService from './api';
import { Product, ApiResponse } from '@/types';

export interface ProductListParams {
  page?: number;
  limit?: number;
  platform?: 'taobao' | 'tmall' | 'jd' | 'pdd';
  category?: string;
  sortBy?: 'price' | 'sales' | 'rating' | 'created_at';
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

export interface ProductListResponse {
  products: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface CreateProductData {
  title: string;
  description?: string;
  url: string;
  imageUrl?: string;
  brand?: string;
  model?: string;
  platform: 'taobao' | 'tmall' | 'jd' | 'pdd' | 'manual';
  currentPrice?: number;
  originalPrice?: number;
  sales?: number;
  rating?: number;
  reviewCount?: number;
  stock?: number;
  category?: string;
  subCategory?: string;
  tags?: string[];
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id: string;
}

export interface BatchImportData {
  products: CreateProductData[];
}

class ProductService {
  private baseUrl = '/products';

  /**
   * 获取产品列表
   */
  async getProducts(params: ProductListParams = {}): Promise<ProductListResponse> {
    try {
      const response = await apiService.get<{ data: ProductListResponse }>(this.baseUrl, params);
      return response.data;
    } catch (error) {
      console.error('获取产品列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取产品详情
   */
  async getProductById(id: string): Promise<Product> {
    try {
      const response = await apiService.get<{ data: { product: Product } }>(`${this.baseUrl}/${id}`);
      return response.data.product;
    } catch (error) {
      console.error('获取产品详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建产品
   */
  async createProduct(productData: CreateProductData): Promise<Product> {
    try {
      const response = await apiService.post<{ data: { product: Product } }>(this.baseUrl, productData);
      return response.data.product;
    } catch (error) {
      console.error('创建产品失败:', error);
      throw error;
    }
  }

  /**
   * 更新产品
   */
  async updateProduct(id: string, productData: Partial<CreateProductData>): Promise<Product> {
    try {
      const response = await apiService.put<{ data: { product: Product } }>(`${this.baseUrl}/${id}`, productData);
      return response.data.product;
    } catch (error) {
      console.error('更新产品失败:', error);
      throw error;
    }
  }

  /**
   * 删除产品
   */
  async deleteProduct(id: string): Promise<void> {
    try {
      await apiService.delete(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error('删除产品失败:', error);
      throw error;
    }
  }

  /**
   * 批量导入产品
   */
  async batchImportProducts(data: BatchImportData): Promise<{ 
    success: number; 
    failed: number; 
    errors: any[] 
  }> {
    try {
      const response = await apiService.post<{ 
        data: { 
          success: number; 
          failed: number; 
          errors: any[] 
        } 
      }>(`${this.baseUrl}/batch-import`, data);
      return response.data;
    } catch (error) {
      console.error('批量导入产品失败:', error);
      throw error;
    }
  }

  /**
   * 搜索产品
   */
  async searchProducts(query: string, filters: Partial<ProductListParams> = {}): Promise<ProductListResponse> {
    try {
      const params = {
        ...filters,
        search: query
      };
      return await this.getProducts(params);
    } catch (error) {
      console.error('搜索产品失败:', error);
      throw error;
    }
  }

  /**
   * 获取产品统计信息
   */
  async getProductStats(): Promise<{
    total: number;
    byCategory: Record<string, number>;
    byPlatform: Record<string, number>;
    byStatus: Record<string, number>;
  }> {
    try {
      const response = await apiService.get<{ 
        data: {
          total: number;
          byCategory: Record<string, number>;
          byPlatform: Record<string, number>;
          byStatus: Record<string, number>;
        }
      }>(`${this.baseUrl}/stats`);
      return response.data;
    } catch (error) {
      console.error('获取产品统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取产品分类列表
   */
  async getCategories(): Promise<string[]> {
    try {
      const response = await apiService.get<{ data: { categories: string[] } }>(`${this.baseUrl}/categories`);
      return response.data.categories;
    } catch (error) {
      console.error('获取产品分类失败:', error);
      throw error;
    }
  }

  /**
   * 获取品牌列表
   */
  async getBrands(): Promise<string[]> {
    try {
      const response = await apiService.get<{ data: { brands: string[] } }>(`${this.baseUrl}/brands`);
      return response.data.brands;
    } catch (error) {
      console.error('获取品牌列表失败:', error);
      throw error;
    }
  }

  /**
   * 切换产品跟踪状态
   */
  async toggleTracking(id: string, isTracked: boolean): Promise<Product> {
    try {
      const response = await apiService.put<{ data: { product: Product } }>(`${this.baseUrl}/${id}`, {
        isTracked
      });
      return response.data.product;
    } catch (error) {
      console.error('切换产品跟踪状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取产品价格历史
   */
  async getPriceHistory(id: string, days: number = 30): Promise<{
    date: string;
    price: number;
  }[]> {
    try {
      const response = await apiService.get<{ 
        data: { 
          priceHistory: { date: string; price: number }[] 
        } 
      }>(`${this.baseUrl}/${id}/price-history`, { days });
      return response.data.priceHistory;
    } catch (error) {
      console.error('获取产品价格历史失败:', error);
      throw error;
    }
  }

  /**
   * 分析产品
   */
  async analyzeProduct(id: string, analysisType: 'basic' | 'detailed' | 'competitive' = 'basic'): Promise<{
    analysisId: string;
    status: string;
  }> {
    try {
      const response = await apiService.post<{ 
        data: { 
          analysisId: string; 
          status: string 
        } 
      }>(`${this.baseUrl}/${id}/analyze`, { analysisType });
      return response.data;
    } catch (error) {
      console.error('分析产品失败:', error);
      throw error;
    }
  }

  /**
   * 导出产品数据
   */
  async exportProducts(format: 'csv' | 'excel' = 'excel', filters: Partial<ProductListParams> = {}): Promise<Blob> {
    try {
      const response = await apiService.get(`${this.baseUrl}/export`, {
        ...filters,
        format
      });
      return new Blob([response], { 
        type: format === 'csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
    } catch (error) {
      console.error('导出产品数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取推荐产品
   */
  async getRecommendedProducts(category?: string, limit: number = 10): Promise<Product[]> {
    try {
      const response = await apiService.get<{ data: { products: Product[] } }>(`${this.baseUrl}/recommendations`, {
        category,
        limit
      });
      return response.data.products;
    } catch (error) {
      console.error('获取推荐产品失败:', error);
      throw error;
    }
  }
}

// 创建产品服务实例
const productService = new ProductService();

export default productService;
